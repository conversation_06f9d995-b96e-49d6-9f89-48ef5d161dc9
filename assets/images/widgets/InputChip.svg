<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M104 16H24C19.5817 16 16 19.5817 16 24V104C16 108.418 19.5817 112 24 112H104C108.418 112 112 108.418 112 104V24C112 19.5817 108.418 16 104 16Z" fill="white" stroke="#E5E9F0" stroke-width="2"/>
    <g clip-path="url(#clip0_20_20)">
        <path d="M93.7013 62.23L97.7544 66.2831C97.8825 66.4113 97.8825 66.6244 97.7544 66.7525C97.6263 66.8806 97.4131 66.8806 97.285 66.7525L93.2319 62.6994C93.1463 62.5713 93.1463 62.3581 93.2319 62.23C93.36 62.1019 93.5731 62.1019 93.7013 62.23Z" fill="black"/>
        <path d="M97.7975 62.6994L93.7438 66.7525C93.6156 66.8806 93.4025 66.8806 93.2744 66.7525C93.1463 66.6244 93.1463 66.4113 93.2744 66.2831L97.3281 62.23C97.4563 62.1019 97.6694 62.1019 97.7975 62.23C97.8825 62.3581 97.8825 62.5713 97.7975 62.6994Z" fill="black"/>
    </g>
    <g opacity="0.4" filter="url(#filter0_d_20_20)">
        <rect x="24" y="52" width="80" height="24" rx="4" fill="url(#paint0_linear_20_20)"/>
    </g>
    <rect opacity="0.4" x="47" y="59" width="41" height="11" rx="2" fill="url(#paint1_linear_20_20)"/>
    <g clip-path="url(#clip1_20_20)">
        <path d="M30 64.5C30 68.0852 32.9148 71 36.5 71C40.0852 71 43 68.0852 43 64.5C43 60.9148 40.0852 58 36.5 58C32.9148 58 30 60.9148 30 64.5Z" fill="url(#paint2_linear_20_20)"/>
        <path opacity="0.4" d="M39.0086 68.4609H34.0016C33.5445 68.4609 33.1687 68.0852 33.1687 67.6281C33.1687 66.1352 34.3773 64.9164 35.8805 64.9164H37.1297C38.6227 64.9164 39.8414 66.125 39.8414 67.6281C39.8414 68.0852 39.4758 68.4609 39.0086 68.4609ZM36.5 64.7031C35.3523 64.7031 34.418 63.7688 34.418 62.6211C34.418 61.4734 35.3523 60.5391 36.5 60.5391C37.6477 60.5391 38.582 61.4734 38.582 62.6211C38.5922 63.7789 37.6477 64.7031 36.5 64.7031Z" fill="#DAE2FF"/>
    </g>
    <defs>
        <filter id="filter0_d_20_20" x="21" y="49" width="88" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="1" dy="1"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_20_20"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_20_20" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_20_20" x1="24" y1="52" x2="1345.1" y2="4455.67" gradientUnits="userSpaceOnUse">
            <stop stop-color="#A3B8FF"/>
            <stop offset="1" stop-color="#8FA3E0"/>
        </linearGradient>
        <linearGradient id="paint1_linear_20_20" x1="47" y1="59" x2="597.61" y2="2111.28" gradientUnits="userSpaceOnUse">
            <stop stop-color="#A3B8FF"/>
            <stop offset="1" stop-color="#8FA3E0"/>
        </linearGradient>
        <linearGradient id="paint2_linear_20_20" x1="30" y1="58" x2="1330" y2="1358" gradientUnits="userSpaceOnUse">
            <stop stop-color="#A3B8FF"/>
            <stop offset="1" stop-color="#8FA3E0"/>
        </linearGradient>
        <clipPath id="clip0_20_20">
            <rect width="5" height="5" fill="white" transform="translate(93 62)"/>
        </clipPath>
        <clipPath id="clip1_20_20">
            <rect width="13" height="13" fill="white" transform="translate(30 58)"/>
        </clipPath>
    </defs>
</svg>
