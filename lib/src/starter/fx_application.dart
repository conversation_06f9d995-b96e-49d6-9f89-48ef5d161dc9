import 'package:app/app.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fx_boot_starter/fx_boot_starter.dart';
import 'package:go_router/go_router.dart';
import 'package:media_kit/media_kit.dart';
import 'package:widget_module/widget_module.dart';
import 'package:widget_module/blocs/blocs.dart';

import '../flutter_unit.dart';
import '../navigation/view/app_bloc_provider.dart';
import 'start_repository.dart';
import 'package:app_update/app_update.dart';
export 'view/splash/Flutter_unit_splash.dart';
export 'view/error/app_start_error.dart';
import 'package:rinf/rinf.dart';
import '../bindings/bindings.dart';
class FxApplication with FxStarter<AppConfig> {
  const FxApplication();

  @override
  Widget get app => const AppBlocProvider(child: FlutterUnit3());

  @override
  AppStartRepository<AppConfig> get repository => const FlutterUnitStartRepo();

  @override
  void onLoaded(BuildContext context, int cost, AppConfig state) {
    debugPrint("App启动耗时:$cost ms");
    context.read<AppConfigBloc>().init(state);
    context.initWidgetData();
    if (!kAppEnv.isWeb) {
      context.read<LikeWidgetBloc>().add(const EventLoadLikeData());
      context.read<CategoryBloc>().add(const EventLoadCategory());
    }
  }




  @override
  void onStartSuccess(BuildContext context, AppConfig state) {
    //初始化流媒体播放器
    WidgetsFlutterBinding.ensureInitialized();
    // Necessary initialization for package:media_kit.
    MediaKit.ensureInitialized();
    // context
    //     .read<UpgradeBloc>()
    //     .add(CheckUpdate(appId: 1, locale: state.language.locale.toString()));

    initializeRust(assignRustSignal);

    context.go(AppRoute.wyHome.url);
  }

  @override
  void onStartError(BuildContext context, Object error, StackTrace trace) {
    context.go(AppRoute.startError.url, extra: error);
  }

  @override
  void onGlobalError(Object error, StackTrace stack) {
    print(error);
  }
}
