import 'app_l10n.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppL10nEs extends AppL10n {
  AppL10nEs([String locale = 'es']) : super(locale);

  @override
  String get deskTabWidgets => 'Colección de widgets';

  @override
  String get deskTabPainter => 'Colección de dibujos';

  @override
  String get deskTabKnowledge => 'Centro de conocimiento';

  @override
  String get deskTabTools => 'Caja de herramientas';

  @override
  String get deskTabMine => 'Info App';

  @override
  String get messageBoard => 'Tablero de Mensajes';

  @override
  String get deskTabWyHome => '位移计主页';

  @override
  String get deskTabDeviceVideo => '位移计视频';

  @override
  String get mobileTabWidgets => 'Widgets';

  @override
  String get mobileTabPainter => 'Dibujo';

  @override
  String get mobileTabKnowledge => 'Conocimiento';

  @override
  String get mobileTabTools => 'Herramientas';

  @override
  String get mobileTabMine => 'Mi';

  @override
  String get mobileTabWyHome => '位移计主页';

  @override
  String get mobileTabDeviceVideo => '位移计视频';
}
