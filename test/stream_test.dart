// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_bloc/flutter_bloc.dart';

Stream<int> countStream(int max) async* {
  for (int i = 0; i < max; i++) {
    yield i;
  }
}


Future<int> sumStream(Stream<int> stream) async {
  int sum = 0;
  await for (int value in stream) {
    sum += value;
  }
  return sum;
}
class CounterCubit extends Cubit<int> {
  CounterCubit(int initialState) : super(initialState);

  void increment(){

    addError(Exception('increment error!'), StackTrace.current);
    emit(state + 1);

  }

  @override
  void onChange(Change<int> change) {
    super.onChange(change);
    print(change);
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    print('$error, $stackTrace');
    super.onError(error, stackTrace);
  }
}

test_stream() async {
  /// Initialize a stream of integers 0-9
  Stream<int> stream = countStream(10);
  /// Compute the sum of the stream of integers
  int sum = await sumStream(stream);
  /// Print the sum
  print(sum); // 45
}
 test_cubit() async {
  final cubit = CounterCubit(0);
  final subscription = cubit.stream.listen(print);
  cubit.increment();
  cubit.increment();
  cubit.increment();
  await Future.delayed(Duration.zero);
  await subscription.cancel();
  print(cubit.state);
  cubit.close();
}

test_observer() async {
  final cubit = CounterCubit(0);
  cubit.increment();
  cubit.close();
}


sealed class CounterEvent {}

final class CounterIncrementPressed extends CounterEvent {}

class CounterBloc extends Bloc<CounterEvent, int> {
  CounterBloc() : super(0){
    on<CounterIncrementPressed>((event, emit) {
      emit(state + 1);
    });
  }

  @override
  void onChange(Change<int> change) {
    super.onChange(change);
    print(change);
  }

  @override
  void onTransition(Transition<CounterEvent, int> transition) {
    super.onTransition(transition);
    print(transition);
  }
}

test_bloc() async {
  final bloc = CounterBloc();
  print(bloc.state); // 0
  bloc.add(CounterIncrementPressed());
  bloc.add(CounterIncrementPressed());
  bloc.add(CounterIncrementPressed());
  bloc.add(CounterIncrementPressed());
  await Future.delayed(Duration.zero);
  print(bloc.state); // 1
  await bloc.close();
}

test_bloc_stream()
 async {
   final bloc = CounterBloc();
   final subscription = bloc.stream.listen(print); // 1
   bloc.add(CounterIncrementPressed());
   bloc.add(CounterIncrementPressed());
   bloc.add(CounterIncrementPressed());
   bloc.add(CounterIncrementPressed());
   bloc.add(CounterIncrementPressed());
   await Future.delayed(Duration.zero);
   await subscription.cancel();
   await bloc.close();
}


class SimpleBlocObserver extends BlocObserver {
  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    print('${bloc.runtimeType} $change');
  }
  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    print('${bloc.runtimeType} $transition');
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    print('${bloc.runtimeType} $error $stackTrace');
    super.onError(bloc, error, stackTrace);
  }

}

test_bloc_observer() async {
  Bloc.observer = SimpleBlocObserver();
  CounterBloc()
    ..add(CounterIncrementPressed())
    ..close();
}

enum AuthenticationState { unknown, authenticated, unauthenticated }



void main() async {
  // await test_cubit();
  // await test_observer();
  

  // await test_bloc();

  // await test_bloc_stream();

  // await test_bloc_observer();

}

