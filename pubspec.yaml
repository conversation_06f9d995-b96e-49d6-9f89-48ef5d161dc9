name: inteagle_monitoring_robot_app
description: "视觉位移计相关监测设备的客户端操作APP"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 0.1.0

environment:
  sdk: ">=3.5.0 <4.0.0"
workspace:
  - modules/basic_system/app
  - modules/basic_system/app_update
  - modules/basic_system/authentication
  - modules/basic_system/components
  - modules/basic_system/l10n
  - modules/basic_system/storage
  - modules/basic_system/toly_ui
  - modules/basic_system/utils

  - modules/knowledge_system/algorithm
  - modules/knowledge_system/artifact
  - modules/knowledge_system/layout

  - modules/tools_system/treasure_tools

  - modules/widget_system/widget_module
  - modules/widget_system/widgets
  - modules/wy_device_system/client

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.4

  # 路由与状态管理
  go_router: ^14.2.0 # 路由管理
  flutter_bloc: ^8.1.6 # 状态管理

  ## fx 架构
  fx_platform_adapter: ^0.0.2+1 # 平台适配器
  fx_go_router_ext: 0.0.5 # 路由
  fx_dao: 0.0.1+2 # 数据库
  fx_dio: 0.0.4
  fx_boot_starter: 0.1.1 # app 启动器
  fx_trace: 0.0.3 # 异常追踪/监听

  # 数据与持久化
  dio: ^5.4.3+1 # 网络请求
  shared_preferences: ^2.5.1 # xml 固化
  jwt_decoder: ^2.0.1 # jwt 解析
  path_provider: ^2.1.3 # 路径

  # 平台功能
  connectivity_plus: ^6.0.3 # 网络状态
  url_launcher: ^6.3.1 # url
  archive: ^4.0.2 # 解压
  file_picker: ^10.1.9 # 文件选择器
  share_plus: ^10.0.1 # 文字分享

  # 视图展示
  tolyui: 0.0.4+3 # tolyui
  refresh: ^1.0.1 # 下拉刷新
  dash_painter: ^1.0.2 # 虚线
  flutter_star: ^1.0.2 # 星星组件
  flutter_spinkit: ^5.2.0 # loading
  toggle_rotate: ^1.0.1 # 点击旋转
  wrapper: ^1.0.2 # 气泡包裹
  webview_flutter: ^4.2.4 # webview
  flutter_markdown: ^0.7.2+1 # markdown
  flutter_svg: ^2.0.17 # svg 展示

  # 逻辑处理
  image: ^4.0.17 # 图像处理
  equatable: ^2.0.5 # 相等辅助
  media_kit: ^1.1.11 # Primary package.
  media_kit_video: ^1.2.5 # For video rendering.
  media_kit_libs_video: ^1.0.5 # Native video dependencies.
  media_kit_libs_android_video: any
  media_kit_libs_ios_video: any
  media_kit_libs_macos_video: any
  media_kit_libs_windows_video: any
  media_kit_libs_linux: any
  fl_chart: ^0.69.2
  chart_sparkline: ^1.1.1
  graphic: ^2.5.1
  intl: ^0.19.0
  tolyui_rx_layout: ^1.0.0
  csv: ^5.0.0
  tab_container: ^3.5.3
  image_gallery_saver_plus: "^4.0.0"
  input_slider: ^0.3.1
  multi_split_view: ^3.6.0
  dropdown_search: ^6.0.2
  time_range_picker: ^2.3.0
  progressive_time_picker: ^1.0.3
  multicast_dns: ^0.3.3
  json_annotation: ^4.9.0
  rinf: ^8.0.0
  meta: ^1.15.0
  tuple: ^2.0.2
  permission_handler: ^12.0.0+1
  data_table_2: ^2.6.0
  buttons_tabbar: ^1.3.15
  tdesign_flutter: ^0.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: "^0.14.3"
  build_runner: ^2.4.6
  json_serializable: ^6.7.1

flutter_launcher_icons:
  android: true
  ios: true
  web: true
  windows: true
  macos: true
  image_path: "assets/images/icon_head.png"

dependency_overrides:
  web: ^1.0.0
  # 解决flutter 3.32以下无法运行的问题 https://tdesign.tencent.com/flutter/faq
  tdesign_flutter_adaptation: 3.16.0
  image_picker: 1.0.8

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/data/
    #    - assets/data/web/
    - assets/images/head_icon/
    - assets/images/widgets/
    - assets/flutter.db
    - assets/version.json

  fonts: # 配置字体，可配置多个，支持ttf和otf,ttc等字体资源
    - family: TolyIcon
      fonts:
        - asset: assets/iconfont/toly_icon.ttf
    - family: IndieFlower #字体名
      fonts:
        - asset: assets/fonts/IndieFlower-Regular.ttf
    - family: BalooBhai2 #字体名
      fonts:
        - asset: assets/fonts/BalooBhai2-Regular.ttf
    - family: Inconsolata #字体名
      fonts:
        - asset: assets/fonts/Inconsolata-Regular.ttf
    - family: Neucha #字体名
      fonts:
        - asset: assets/fonts/Neucha-Regular.ttf
    - family: ComicNeue #字体名
      fonts:
        - asset: assets/fonts/ComicNeue-Regular.ttf
    - family: CHOPS
      fonts:
        - asset: assets/fonts/CHOPS.ttf

toly:
  icon:
    src_zip: ""
    assets_dir: "assets/iconfont"
    file_dist: "packages/app/lib/app/res/toly_icon.dart"
