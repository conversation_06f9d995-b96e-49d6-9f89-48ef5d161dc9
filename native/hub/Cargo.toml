[package]
# Do not change the name of this crate.
name = "hub"
version = "0.1.0"
edition = "2024"

[lib]
# `lib` is required for non-library targets,
# such as tests and benchmarks.
# `cdylib` is for Linux, Android, Windows, and web.
# `staticlib` is for iOS and macOS.
crate-type = ["lib", "cdylib", "staticlib"]

[lints.clippy]
unwrap_used = "deny"
expect_used = "deny"
wildcard_imports = "deny"

[dependencies]
rinf = "8.0.0"
serde = { version = "1", features = ["derive"] }
tokio = { version = "1", features = ["rt", "macros", "time"] }
async-trait = "0.1.87"
messages = "0.3.1"
libsql = { version = "0.9.10", default-features = false, features = ["core", "replication", "remote", "stream"] }
reqwest = { version = "0.12.13", features = ["json", "rustls-tls"], default-features = false }
serde_json = "1.0"
mobc = "0.8.5"
futures = "0.3"
futures-util = "0.3"
anyhow = "1.0"
chrono = "0.4"
rust_xlsxwriter = { version = "0.88" }
# Uncomment below to target the web.
# tokio_with_wasm = { version = "0.8.1", features = ["rt", "macros", "time"] }
# wasm-bindgen = "0.2.100"


[package.metadata.android]
ndk_stl = "c++_shared"
