import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:client/view/components/targets/target_info_card.dart';

void main() {
  testWidgets('TargetInfoCard displays targets', (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TargetInfoCard(),
        ),
      ),
    );

    // Verify that the targets are displayed
    expect(find.text('标靶1'), findsOneWidget);
    expect(find.text('标靶2'), findsOneWidget);
    expect(find.text('标靶3'), findsOneWidget);
    expect(find.text('标靶4'), findsOneWidget);
    expect(find.text('标靶5'), findsOneWidget);
  });
}