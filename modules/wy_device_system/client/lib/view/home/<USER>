import 'package:tolyui/tolyui.dart';
import 'package:tolyui/basic/basic.dart';
import 'package:tolyui_rx_layout/tolyui_rx_layout.dart';
import 'package:flutter/material.dart';
import 'package:buttons_tabbar/buttons_tabbar.dart';

import '../components/charts/chart_panel.dart';
import '../components/charts/flexible-line-chart.dart';
import '../components/measurement/query_panel.dart';
import '../ptz_control/ptz_control.dart';

class VDMTabs extends StatefulWidget {
  const VDMTabs({super.key});

  @override
  State<VDMTabs> createState() => _VDMTabsState();
}

class _VDMTabsState extends State<VDMTabs> with TickerProviderStateMixin {
  late TabController _tabController;

  final List<MenuMeta> items = const [
    MenuMeta(label: '测量', router: "measurement", icon: Icons.assessment),
    MenuMeta(label: '设置', router: 'setting', icon: Icons.settings),
  ];

  String activeId = 'measurement';

  MenuMeta get activeMenu => items.singleWhere((e) => e.id == activeId);

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: items.length, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        activeId = items[_tabController.index].id;
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Column(
        children: [
          _buildTabBar(context),
          Expanded(
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              controller: _tabController,
              children: const [
                MeasurementTabContent(),
                SettingsTabContent(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorSize: TabBarIndicatorSize.tab,
          indicatorWeight: 3,
          indicatorColor: Theme.of(context).primaryColor,
          indicatorPadding: const EdgeInsets.symmetric(horizontal: 20),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 15,
          ),
          tabs: items.map((item) => Tab(
            height: 64,
            icon: Icon(item.icon, size: 22),
            text: item.label,
            iconMargin: const EdgeInsets.only(bottom: 6),
          )).toList(),
        ),
      ),
    );
  }
}

// 测量标签页内容
class MeasurementTabContent extends StatelessWidget {
  const MeasurementTabContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildIntegratedChartSection(),
          const SizedBox(height: 20),
          _buildStatisticsSection(),
          const SizedBox(height: 20),
          _buildControlSection(),
          const SizedBox(height: 20),
          _buildTargetDataSection(),
        ],
      ),
    );
  }

  Widget _buildEnvironmentSection() {
    return _buildSectionCard(
      child: Row$(
        gutter: 20.rx,
        cells: [
          Cell(
            span: 24.rx,
            child: EnvironmentSensorExample(),
          )
        ],
      ),
    );
  }

  Widget _buildIntegratedChartSection() {
    return IntegratedChartPanel(
      onQueryChanged: (query) {
        // 处理查询变化的逻辑
        debugPrint('查询条件更新: ${query.chartType}, 实时: ${query.isRealtime}');
      },
      chartWidget: EnvironmentSensorExample(), // 可以传入具体的图表组件
    );
  }

  Widget _buildStatisticsSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据屏幕宽度调整布局
        final isWide = constraints.maxWidth > 600;
        final span = isWide ? 8.rx : 24.rx;

        if (isWide) {
          return _buildSectionCard(
            child: Row$(
              gutter: 16.rx,
              cells: [
                Cell(span: span, child: _buildTemperatureCard()),
                Cell(span: span, child: _buildHumidityCard()),
                Cell(span: span, child: _buildPressureCard()),
              ],
            ),
          );
        } else {
          return Column(
            children: [
              _buildSectionCard(child: _buildTemperatureCard()),
              const SizedBox(height: 12),
              _buildSectionCard(child: _buildHumidityCard()),
              const SizedBox(height: 12),
              _buildSectionCard(child: _buildPressureCard()),
            ],
          );
        }
      },
    );
  }

  Widget _buildTemperatureCard() {
    return TolyStatistics(
      title: "温度",
      value: 25.6,
      enableAnimation: true,
      suffix: const Padding(
        padding: EdgeInsets.only(left: 4.0),
        child: Text('°C', style: TextStyle(fontSize: 12, color: Colors.grey)),
      ),
    );
  }

  Widget _buildHumidityCard() {
    return TolyStatistics(
      title: "湿度",
      value: 68.2,
      enableAnimation: true,
      suffix: const Padding(
        padding: EdgeInsets.only(left: 4.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('%', style: TextStyle(fontSize: 12, color: Colors.grey)),
            SizedBox(width: 4),
            Icon(Icons.water_drop, size: 16, color: Colors.blue),
          ],
        ),
      ),
    );
  }

  Widget _buildPressureCard() {
    return TolyStatistics(
      title: "压强",
      value: 1013.25,
      valueStyle: const TextStyle(color: Colors.orange),
      enableAnimation: true,
      suffix: const Padding(
        padding: EdgeInsets.only(left: 4.0),
        child: Text('hPa', style: TextStyle(fontSize: 12, color: Colors.grey)),
      ),
    );
  }

  Widget _buildControlSection() {
    return _buildSectionCard(
      title: "云台控制",
      child: Center(
        child: CameraPTZController(
          buttonSize: 52,
          onDirectionControl: (f, f2) {},
        ),
      ),
    );
  }

  Widget _buildTargetDataSection() {
    return _buildSectionCard(
      title: "标靶数据监控",
      child: SizedBox(
        height: 140,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          itemCount: 50, // 减少数量以提高性能
          itemBuilder: (context, index) => Container(
            width: 140,
            margin: const EdgeInsets.only(right: 12.0),
            child: _buildTargetCard(index),
          ),
        ),
      ),
    );
  }

  Widget _buildTargetCard(int index) {
    final value = (index * 0.5 + 10).toDouble();
    final isPositive = index % 3 != 0;

    return TolyStatistics(
      title: "标靶${index + 1}",
      value: value,
      enableAnimation: true,
      valueStyle: TextStyle(
        color: isPositive ? Colors.green : Colors.red,
        fontWeight: FontWeight.w600,
      ),
      suffix: Padding(
        padding: const EdgeInsets.only(left: 4.0),
        child: Icon(
          isPositive ? Icons.trending_up : Icons.trending_down,
          size: 16,
          color: isPositive ? Colors.green : Colors.red,
        ),
      ),
    );
  }

  Widget _buildSectionCard({String? title, required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
          ],
          child,
        ],
      ),
    );
  }
}

// 设置标签页内容
class SettingsTabContent extends StatelessWidget {
  const SettingsTabContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingsHeader(),
          const SizedBox(height: 24),
          _buildSettingsSection(
            title: "设备配置",
            icon: Icons.devices,
            children: [
              _buildSettingItem("设备名称", "VDM-001", Icons.edit),
              _buildSettingItem("固件版本", "v2.1.3", Icons.system_update),
              _buildSettingItem("网络状态", "已连接", Icons.wifi,
                  valueColor: Colors.green),
            ],
          ),
          const SizedBox(height: 20),
          _buildSettingsSection(
            title: "测量参数",
            icon: Icons.tune,
            children: [
              _buildSettingItem("采样频率", "1Hz", Icons.speed),
              _buildSettingItem("精度等级", "高精度", Icons.precision_manufacturing),
              _buildSettingItem("自动校准", "开启", Icons.auto_fix_high,
                  valueColor: Colors.blue),
            ],
          ),
          const SizedBox(height: 20),
          _buildSettingsSection(
            title: "系统设置",
            icon: Icons.settings,
            children: [
              _buildSettingItem("数据存储", "本地+云端", Icons.storage),
              _buildSettingItem("报警阈值", "自定义", Icons.notification_important),
              _buildSettingItem("日志级别", "详细", Icons.article),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.settings,
            color: Colors.white,
            size: 32,
          ),
          SizedBox(height: 12),
          Text(
            '系统设置',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 4),
          Text(
            '配置设备参数和系统选项',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, size: 20, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingItem(
      String title,
      String value,
      IconData icon, {
        Color? valueColor,
      }) {
    return InkWell(
      onTap: () {
        // 处理设置项点击
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 18, color: Colors.grey.shade600),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: valueColor ?? Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              size: 18,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }
}
