import 'package:flutter/material.dart';

class DraggableToggleButton extends StatefulWidget {
  final bool showROI;
  final Function(bool) onToggle;

  const DraggableToggleButton({
    Key? key,
    required this.showROI,
    required this.onToggle,
  }) : super(key: key);

  @override
  State<DraggableToggleButton> createState() => _DraggableToggleButtonState();
}

class _DraggableToggleButtonState extends State<DraggableToggleButton> {
  // 默认位置在右上角
  Offset _position = const Offset(0, 0);

  // 限制拖动的最大高度（只能在顶部区域拖动）
  final double _maxDragHeight = 10.0;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: Draggable(
        feedback: _buildToggleButton(isDragging: true),
        childWhenDragging: Container(), // 拖动时原位置不显示
        onDragEnd: (details) {
          // 确保按钮不会被拖出屏幕，且只能在顶部区域拖动
          final screenSize = MediaQuery.of(context).size;
          final toggleSize = const Size(130, 36); // 估计的按钮大小

          double newX = details.offset.dx;
          double newY = details.offset.dy;

          // 水平方向限制在屏幕内
          newX = newX.clamp(0, screenSize.width - toggleSize.width);

          // 垂直方向限制在顶部区域
          newY = newY.clamp(0, _maxDragHeight);

          setState(() {
            _position = Offset(newX, newY);
          });
        },
        child: _buildToggleButton(),
      ),
    );
  }

  Widget _buildToggleButton({bool isDragging = false}) {
    return Container(
      decoration: BoxDecoration(
        color: isDragging
            ? Colors.black.withOpacity(0.6)
            : Colors.black.withOpacity(0.4),
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: isDragging
            ? [BoxShadow(color: Colors.black26, blurRadius: 8, spreadRadius: 1)]
            : null,
      ),
      padding: const EdgeInsets.all(4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖动手柄
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Icon(
              Icons.drag_handle,
              size: 16,
              color: Colors.white.withOpacity(0.7),
            ),
          ),

          // 切换按钮
// 切换按钮
          InkWell(
            onTap: () {
              widget.onToggle(!widget.showROI);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                color: widget.showROI ? Colors.blue.withOpacity(0.6) : Colors.transparent,
                borderRadius: BorderRadius.circular(6.0),
                border: Border.all(color: Colors.white.withOpacity(0.8)),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.showROI ? Icons.crop : Icons.videocam,
                    size: 14,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    widget.showROI ? '图片' : '视频',
                    style: const TextStyle(fontSize: 12, color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}