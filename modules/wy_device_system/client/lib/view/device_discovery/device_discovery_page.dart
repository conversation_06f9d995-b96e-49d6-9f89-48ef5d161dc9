import 'package:client/view/device_discovery/device_item.dart';
import 'package:client/view/device_discovery/device_save.dart';
import 'package:client/view/device_home/device_overview.dart';
import 'package:client/view/widgets/empty.dart';
import 'package:client/view/widgets/wifi_icon.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import '../../model/discovered_device.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import '../../bloc/device_discovery_bloc.dart';
import '../../services/device_discover_service.dart';
import 'package:utils/utils.dart';
import 'device_search.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';

// 主页面类
class DeviceDiscoveryPage extends StatefulWidget {
  const DeviceDiscoveryPage({super.key});

  @override
  State<DeviceDiscoveryPage> createState() => _DeviceDiscoveryPageState();
}

class _DeviceDiscoveryPageState extends State<DeviceDiscoveryPage> {
  late DeviceDiscoveryBloc _discoveryBloc;

  @override
  void initState() {
    super.initState();
    _discoveryBloc = DeviceDiscoveryBloc(DeviceDiscoveryService());
    _discoveryBloc.add(StartDiscovery());
  }

  @override
  void dispose() {
    _discoveryBloc.close();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _discoveryBloc,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('视觉位移计设备发现'),
              centerTitle: false,
              actions: [
                // 搜索按钮
                IconButton(
                  icon: const Icon(TDIcons.search),
                  tooltip: '搜索设备',
                  onPressed: () {
                    showSearch(
                      context: context,
                      delegate: DeviceSearchDelegate(
                        devices: _getDevices(),
                        onDeviceSelected: _connectToDevice,
                        onDeviceDeleted: _deleteDevice,
                      ),
                    );
                  },
                ),
                // 添加设备按钮
                IconButton(
                  icon: const Icon(TDIcons.add),
                  tooltip: '添加设备',
                  onPressed: () => _addDevicePage(context, _discoveryBloc),
                ),
                BlocBuilder<DeviceDiscoveryBloc, DeviceDiscoveryState>(
                  builder: (context, state) {
                    final isDiscovering = state is DeviceDiscoveryLoaded
                        ? state.isDiscovering
                        : false;
                    return IconButton(
                      icon: isDiscovering
                          ? const TweenWifiIcon()
                          : const Icon(TDIcons.wifi_off),
                      tooltip: isDiscovering ? '停止扫描' : '开始扫描',
                      onPressed: () {
                        if (isDiscovering) {
                          context
                              .read<DeviceDiscoveryBloc>()
                              .add(StopDiscovery());
                        } else {
                          context
                              .read<DeviceDiscoveryBloc>()
                              .add(StartDiscovery());
                        }
                      },
                    );
                  },
                ),
              ],
            ),
            body: BlocBuilder<DeviceDiscoveryBloc, DeviceDiscoveryState>(
              builder: (context, state) {
                if (state is DeviceDiscoveryInitial ||
                    state is DeviceDiscoveryLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is DeviceDiscoveryError) {
                  return Empty(
                      icon: Icons.error_outline,
                      emptyText: state.message,
                      child: const TDButton(
                        text: '重试',
                        size: TDButtonSize.small,
                        theme: TDButtonTheme.primary,
                      ));
                } else if (state is DeviceDiscoveryLoaded) {
                  return _buildDeviceList(context, state);
                }

                return const Center(child: Text('未知状态'));
              },
            ),
            floatingActionButton: FloatingActionButton(
              onPressed: () => _addDevicePage(context, _discoveryBloc),
              tooltip: '添加设备',
              child: const Icon(Icons.add),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDeviceList(BuildContext context, DeviceDiscoveryLoaded state) {
    final devices = state.devices;
    if (devices.isEmpty) {
      return Empty(
        icon: Icons.devices,
        emptyText: '未发现设备',
        desc: state.isDiscovering ? '正在扫描中...' : '点击搜索按钮开始扫描',
        child: Column(
          children: [
            if (!state.isDiscovering)
              TDButton(
                text: '开始扫描',
                theme: TDButtonTheme.primary,
                onTap: () =>
                    context.read<DeviceDiscoveryBloc>().add(StartDiscovery()),
              ),
            const SizedBox(height: 28),
            TDButton(
              text: '添加设备',
              type: TDButtonType.outline,
              theme: TDButtonTheme.primary,
              onTap: () => _addDevicePage(context, _discoveryBloc),
            )
          ],
        ),
      );
    }
    return CustomScrollView(
      slivers: [
        if (state.isDiscovering)
          const SliverToBoxAdapter(
            child: LinearProgressIndicator(
              minHeight: 4,
            ),
          ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('发现 ${devices.length} 个设备',
                    style: Theme.of(context).textTheme.titleMedium),
                if (state.isDiscovering)
                  TDText('正在扫描...',
                      textColor: TDTheme.of(context).brandNormalColor)
              ],
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.all(12.0),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
              maxCrossAxisExtent: 400, // 卡片最大宽度
              mainAxisSpacing: 12.0, // 垂直间距
              crossAxisSpacing: 12.0, // 水平间距
              mainAxisExtent: 170,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final device = devices[index];
                return DeviceItem(
                    device: device,
                    onConnect: () => _connectToDevice(context, device),
                    onSave: () {
                      if (device.isSaved) {
                        context
                            .read<DeviceDiscoveryBloc>()
                            .add(UnsaveDevice(device.id));
                      } else {
                        context
                            .read<DeviceDiscoveryBloc>()
                            .add(SaveDevice(device.id));
                      }
                      TDMessage.showMessage(
                        context: context,
                        content:
                            '已${device.isSaved ? '取消' : ''}保存设备 "${device.name}"',
                        theme: MessageTheme.success,
                        duration: 3000,
                      );
                      setState(() {});
                    },
                    onDelete: () => _deleteDevice(device));
              },
              childCount: devices.length,
            ),
          ),
        )
      ],
    );
  }

  // 获取当前的设备列表
  List<DiscoveredDevice> _getDevices() {
    final state = _discoveryBloc.state;
    if (state is DeviceDiscoveryLoaded) {
      return state.devices;
    }
    return [];
  }

  // 添加设备页面
  void _addDevicePage(BuildContext context, bloc) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (buildContext) =>
            DeviceSave(onSave: (info) => _addDevice(context, bloc, info)),
      ),
    );
  }

  // 添加设备
  void _addDevice(BuildContext context, bloc, info) {
    bloc.add(AddDeviceByIdentifier(
      info.deviceIdentifier,
      name: info.name,
      ipAddress: info.ipAddress,
      port: info.port,
    ));
    setState(() {});
  }

  // 删除设备
  void _deleteDevice(DiscoveredDevice device) {
    setState(() {
      _discoveryBloc.add(DeleteDevice(device.id));
    });

    TDMessage.showMessage(
      context: context,
      content: '已删除设备 "${device.name}"',
      theme: MessageTheme.success,
      duration: 3000,
      // link: '撤销',
      // onLinkClick: () {
      //   setState(() {
      //     // 恢复设备
      //     _discoveryBloc.add(AddManualDevice(device));
      //   });
      // },
    );
  }

  // 跳转设备数据展示页面
  void _connectToDevice(BuildContext context, DiscoveredDevice device) {
    // 连接到设备后，将用户导航到设备主界面
    final wsUrl = device.wsUrl;
    String httpUrl = "http://${device.ipAddress}:${device.port}/api";
    HttpUtil.instance.rebase(httpUrl);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DeviceOverview(
            initialWebSocketUrl: wsUrl,
            deviceId: device.id,
            ip: device.ipAddress),
      ),
    );
    // 退出设备主界面后停止同步数据
    (const SyncServiceControl(
      msgType: SyncServiceControlMsgType.stop,
      databasePath: "",
      baseApi: "",
      pollInterval: 0,
    )).sendSignalToRust();
  }
}
