import 'dart:ui';

import 'package:flutter/cupertino.dart';

import '../../../model/roi.dart';

class ScaleConverter {

  static const defaultDeviceResolution = Size(3840, 2160);

  static Rect scaleRectToOriginal(Rect scaledRect, Size scaledSize, Size originalSize) {
    final scaleX = originalSize.width / scaledSize.width;
    final scaleY = originalSize.height / scaledSize.height;


    return Rect.fromLTRB(
      scaledRect.left * scaleX,
      scaledRect.top * scaleY,
      scaledRect.right * scaleX,
      scaledRect.bottom * scaleY,
    );
  }

  static ROI scaleRectToOriginalROI(Rect rect, Size scaledSize, Size originalSize) {
    final scaleX = originalSize.width / scaledSize.width;
    final scaleY = originalSize.height / scaledSize.height;

    return ROI(
      x: (rect.left * scaleX).toInt(),
      y: (rect.top * scaleY).toInt(),
      width: ((rect.right - rect.left) * scaleX).toInt(),
      height: ((rect.bottom - rect.top) * scaleY).toInt(),
      factorX: 1,
      factorY: 1,
    );

  }


  static Rect scaleRectToDisplay(Rect originalRect, Size originalSize, Size displaySize) {
    final scaleX = displaySize.width / originalSize.width;
    final scaleY = displaySize.height / originalSize.height;

    return Rect.fromLTRB(
      originalRect.left * scaleX,
      originalRect.top * scaleY,
      originalRect.right * scaleX,
      originalRect.bottom * scaleY,
    );
  }
}