import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';
import 'package:intl/intl.dart';

/// 通用传感器折线图组件，支持实时更新、图例交互、数据刷新等功能
class SensorChart extends StatefulWidget {
  /// 初始数据（可选）
  final List<Map<String, dynamic>>? initialData;

  /// X轴字段名，通常是时间戳
  final String xField;

  /// Y轴字段名（可以多个，每个字段对应一条线）
  final List<String> yFields;

  /// 每个Y轴字段对应的图例标签
  final List<String> labels;

  /// 每条线对应的颜色
  final List<Color> colors;

  /// 图表标题
  final String? title;

  /// X轴标签
  final String? xAxisLabel;

  /// Y轴标签
  final String? yAxisLabel;

  /// 是否显示数据点
  final bool showPoints;

  /// 是否启用悬停提示
  final bool enableTooltip;

  /// 是否显示十字准线
  final bool enableCrosshair;

  /// X轴是否为时间数据
  final bool isTimeData;

  /// 时间格式（如果是时间数据）
  final String timeFormat;

  /// Y轴值显示的小数位数
  final int decimalPlaces;

  /// Y轴值的单位（如：°C, %, g）
  final List<String> units;

  /// 图表高度
  final double height;

  /// 图表宽度
  final double width;

  /// 是否显示刷新按钮
  final bool showRefreshButton;

  /// 自定义数据获取函数
  final Future<List<Map<String, dynamic>>> Function()? dataLoader;

  /// 数据点最大数量
  final int maxDataPoints;

  /// 自动刷新间隔（如果为null则不自动刷新）
  final Duration? autoRefreshInterval;

  /// 是否允许通过图例交互切换数据系列显示/隐藏
  final bool interactiveLegend;

  /// Y轴是否共享同一个刻度范围（适用于多系列数据）
  final bool shareYDomain;

  /// Y轴最小值（如果为null则自动计算）
  final double? yMin;

  /// Y轴最大值（如果为null则自动计算）
  final double? yMax;

  /// Y轴刻度间隔（如果为null则自动计算）
  final int? yTickCount;
  /// 数据变化事件流控制器
  final StreamController<ChangeDataEvent<Map<String, dynamic>>>? changeDataStreamController;

  const SensorChart({
    Key? key,
    this.initialData,
    required this.xField,
    required this.yFields,
    required this.labels,
    required this.colors,
    this.title,
    this.xAxisLabel,
    this.yAxisLabel,
    this.showPoints = true,
    this.enableTooltip = true,
    this.enableCrosshair = true,
    this.isTimeData = true,
    this.timeFormat = 'HH:mm:ss',
    this.decimalPlaces = 2,
    this.units = const [],
    this.height = 300,
    this.width = double.infinity,
    this.showRefreshButton = true,
    this.dataLoader,
    this.maxDataPoints = 100000,
    this.autoRefreshInterval,
    this.interactiveLegend = true,
    this.shareYDomain = true,
    this.yMin,
    this.yMax,
    this.yTickCount,
    this.changeDataStreamController,

  }) : assert(yFields.length == labels.length && yFields.length == colors.length,
  'yFields、labels和colors必须长度相同'),
        assert(units.length == yFields.length,
        '如果提供units，其长度必须与yFields相同'),
        super(key: key);

  @override
  SensorChartState createState() => SensorChartState();
}

class SensorChartState extends State<SensorChart> {
  late final _DataWindowManager _dataManager;


  /// 图表数据
  // late List<Map<String, dynamic>> _data;

  /// 随机数生成器（用于测试数据）
  final Random _random = Random();

  /// 自动刷新定时器
  Timer? _refreshTimer;

  /// 是否正在加载数据
  bool _isLoading = false;

  /// 记录每个数据系列是否可见
  late List<bool> _seriesVisibility;

  @override
  void initState() {
    super.initState();

    // 初始化数据管理器
    _dataManager = _DataWindowManager(
      maxRawPoints: widget.maxDataPoints,
      maxVisiblePoints: _calculateOptimalPointCount(),
    );

    // 初始化数据
    if (widget.initialData != null && widget.initialData!.isNotEmpty) {
      _dataManager.addData(widget.initialData!);
    } else {
      // _generateRandomData();
    }

    // 设置系列可见性
    _seriesVisibility = List.generate(widget.yFields.length, (_) => true);

    // 设置自动刷新
    _setupAutoRefresh();
  }

  int _calculateOptimalPointCount() {
    // 基础值，可根据设备性能调整
    return 2000;
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(SensorChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果数据字段数量改变，更新可见性列表
    if (widget.yFields.length != oldWidget.yFields.length) {
      _seriesVisibility = List.generate(widget.yFields.length, (index) {
        if (index < oldWidget.yFields.length) {
          return _seriesVisibility[index];
        }
        return true; // 新增字段默认可见
      });
    }

    // 如果自动刷新间隔改变，重新设置定时器
    if (widget.autoRefreshInterval != oldWidget.autoRefreshInterval) {
      _refreshTimer?.cancel();
      _setupAutoRefresh();
    }
  }

  /// 设置自动刷新定时器
  void _setupAutoRefresh() {
    if (widget.autoRefreshInterval != null) {
      _refreshTimer = Timer.periodic(widget.autoRefreshInterval!, (_) {
        _updateData();
      });
    }
  }

  /// 刷新数据
  void _updateData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      List<Map<String, dynamic>> newData;
      if (widget.dataLoader != null) {
        newData = await widget.dataLoader!();
      } else {
        newData = [];
      }
      if (newData.isEmpty) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 使用数据管理器添加新数据
      _dataManager.addData(newData);

      // 如果提供了数据流控制器，使用流更新数据
      // if (widget.changeDataStreamController != null &&
      //     widget.changeDataStreamController!.hasListener) {
      //   widget.changeDataStreamController!.add(
      //       ChangeDataEvent(_dataManager.getVisibleData())
      //   );
      // }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('数据更新失败: $e');
    }
  }
  /// 生成随机测试数据
  void _generateRandomData() {
    final now = DateTime.now();
    final testData = <Map<String, dynamic>>[];

    // 生成过去数据
    for (int i = 10; i > 0; i--) {
      final timestamp = now.subtract(Duration(seconds: i));
      testData.add(_generateRandomDataPoint(timestamp: timestamp));
    }

    _dataManager.addData(testData);
  }

  /// 生成单个随机数据点
  Map<String, dynamic> _generateRandomDataPoint({DateTime? timestamp}) {
    final now = timestamp ?? DateTime.now();

    final dataPoint = <String, dynamic>{
      widget.xField: now,
    };

    // 为每个数据字段生成随机值
    for (final field in widget.yFields) {
      dataPoint[field] = _random.nextInt(100) + _random.nextDouble();
    }

    return dataPoint;
  }

  /// 清除数据
  void _clearData() {
    setState(() {
      _dataManager.clear();
    });
  }

  /// 切换数据系列可见性
  void _toggleSeriesVisibility(int index) {
    if (widget.interactiveLegend) {
      setState(() {
        _seriesVisibility[index] = !_seriesVisibility[index];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          if (widget.yFields.length > 1) _buildLegend(),
          Expanded(
            child: _dataManager.length > 10000 ?
            // 大数据集优化
            RepaintBoundary(
              child: _buildChart(),
            ) :
            // 小数据集普通渲染
            _buildChart(),
          ),
        ],
      ),
    );
  }

  /// 构建标题和操作按钮行
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 标题
          if (widget.title != null)
            Text(
              widget.title!,
              style: Theme.of(context).textTheme.titleLarge,
            ),

          // 操作按钮组
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 加载指示器
              if (_isLoading)
                Container(
                  width: 20,
                  height: 20,
                  margin: EdgeInsets.only(right: 8),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),

              // 刷新按钮
              if (widget.showRefreshButton)
                IconButton(
                  icon: Icon(Icons.refresh),
                  onPressed: _isLoading ? null : _updateData,
                  tooltip: '刷新数据',
                ),

              // 清除按钮
              // IconButton(
              //   icon: Icon(Icons.clear_all),
              //   onPressed: _clearData,
              //   tooltip: '清除数据',
              // ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建可交互图例
  /// 构建可交互图例
  Widget _buildLegend() {
    return Container(
      height: 40,
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Align(
        alignment: Alignment.center,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(widget.yFields.length, (index) {
              return Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _toggleSeriesVisibility(index),
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    child: Container(
                      margin: EdgeInsets.only(right: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 4,
                            decoration: BoxDecoration(
                              color: _seriesVisibility[index]
                                  ? widget.colors[index]
                                  : widget.colors[index].withOpacity(0.3),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          SizedBox(width: 6),
                          if (widget.showPoints)
                            Container(
                              width: 10,
                              height: 10,
                              decoration: BoxDecoration(
                                color: _seriesVisibility[index]
                                    ? widget.colors[index]
                                    : widget.colors[index].withOpacity(0.3),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: _seriesVisibility[index]
                                      ? widget.colors[index].withOpacity(0.8)
                                      : widget.colors[index].withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                            ),
                          if (widget.showPoints)
                            SizedBox(width: 6),
                          Text(
                            widget.labels[index],
                            style: TextStyle(
                              color: _seriesVisibility[index]
                                  ? Colors.black
                                  : Colors.black38,
                              fontWeight: _seriesVisibility[index]
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              decoration: _seriesVisibility[index]
                                  ? null
                                  : TextDecoration.lineThrough,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
  /// 计算Y轴数据范围，支持包括负数在内的所有值
  // (double, double) _calculateYAxisRange() {
  //   // 如果已明确设置了最小值和最大值，直接使用这些值
  //   if (widget.yMin != null && widget.yMax != null) {
  //     return (widget.yMin!, widget.yMax!);
  //   }
  //
  //   // 如果没有数据，返回默认范围
  //   if (_data.isEmpty) {
  //     return (0, 100); // 默认范围
  //   }
  //
  //   // 找出所有可见系列的最小值和最大值
  //   double minValue = double.infinity;
  //   double maxValue = double.negativeInfinity;
  //
  //   for (var datum in _data) {
  //     for (int i = 0; i < widget.yFields.length; i++) {
  //       if (_seriesVisibility[i]) {
  //         final fieldName = widget.yFields[i];
  //         if (!datum.containsKey(fieldName)) continue;
  //
  //         final value = datum[fieldName] as num;
  //         if (value < minValue) minValue = value.toDouble();
  //         if (value > maxValue) maxValue = value.toDouble();
  //       }
  //     }
  //   }
  //
  //   // 如果没有找到有效值，返回默认范围
  //   if (minValue == double.infinity || maxValue == double.negativeInfinity) {
  //     return (0, 100);
  //   }
  //
  //   // 添加一些边距，使图表看起来更好
  //   final range = maxValue - minValue;
  //   final padding = range * 0.1; // 10% 的边距
  //
  //   // 应用用户指定的最小值或计算值
  //   double min = widget.yMin ?? (minValue - padding);
  //   double max = widget.yMax ?? (maxValue + padding);
  //
  //   // 确保最小值和最大值不相等
  //   if (min == max) {
  //     min -= 1;
  //     max += 1;
  //   }
  //
  //   return (min, max);
  // }



  /// 构建图表
  Widget _buildChart() {
    // 如果没有数据
    if (_dataManager.length == 0) {
      return Center(child: Text('暂无数据'));
    }

    if (!_seriesVisibility.contains(true)) {
      return Center(
        child: Text('所有数据系列已隐藏，点击图例可切换显示状态'),
      );
    }

    // 准备可见字段
    final visibleFields = <String>[];
    final visibleColors = <Color>[];
    final visibleLabels = <String>[];
    final visibleUnits = <String>[];

    for (int i = 0; i < widget.yFields.length; i++) {
      if (_seriesVisibility[i]) {
        visibleFields.add(widget.yFields[i]);
        visibleColors.add(widget.colors[i]);
        visibleLabels.add(widget.labels[i]);
        if (widget.units.isNotEmpty) {
          visibleUnits.add(widget.units[i]);
        }
      }
    }

    // 获取采样后的数据
    final renderData = _dataManager.length > 10000 ?
    _dataManager.getVisibleData() :
    List.from(_dataManager._allData);

    // 创建图表变量
    final variables = <String, Variable>{};

    // 添加X轴变量
    final String xKey = '时间'; // 使用统一的X轴键
    if (widget.isTimeData) {
      variables[xKey] = Variable(
        accessor: (datum) => datum[widget.xField] as DateTime,
        scale: TimeScale(
          formatter: (value) => DateFormat(widget.timeFormat).format(value),
        ),
      );
    } else {
      variables[xKey] = Variable(
        accessor: (datum) => datum[widget.xField],
        scale: LinearScale(
          formatter: (value) => value.toStringAsFixed(widget.decimalPlaces),
        ),
      );
    }

    // 为每个可见字段添加y轴变量
    for (int i = 0; i < visibleFields.length; i++) {
      final field = visibleFields[i];
      final unit = visibleUnits.isNotEmpty ? visibleUnits[i] : '';
      final label = visibleLabels[i];

      variables[field] = Variable(
        accessor: (datum) => datum[field] as num,
        scale: LinearScale(
          title: label,
          // min: _calculateYAxisRange().$1,
          // max: _calculateYAxisRange().$2,
          tickCount: widget.yTickCount,
          niceRange: true,
          formatter: (value) {
            final formattedValue = (value as num).toStringAsFixed(widget.decimalPlaces);
            // 在格式化的值后添加单位
            return '$formattedValue';
          },
        ),
      );
    }

    // 创建图表元素
    final marks = <Mark>[];

    // 为每个可见字段添加线条和点
    for (int i = 0; i < visibleFields.length; i++) {
      final field = visibleFields[i];
      final color = visibleColors[i];

      // 添加线条
      marks.add(
        LineMark(
          position: Varset(xKey) * Varset(field),
          color: ColorEncode(value: color),
          size: SizeEncode(value: 2),
          shape: ShapeEncode(value: BasicLineShape(smooth: true)),
        ),
      );

      // 添加数据点
      if (widget.showPoints) {
        marks.add(
          PointMark(
            position: Varset(xKey) * Varset(field),
            color: ColorEncode(value: color),
            size: SizeEncode(value: 5),
          ),
        );
      }
    }

    // 创建图表
    return Chart(
      data: renderData,
      variables: variables,
      marks: marks,
      axes: [
        Defaults.horizontalAxis,
        Defaults.verticalAxis,
      ],
      selections: widget.enableTooltip ? {
        'hover': PointSelection(
          on: {
            GestureType.hover,    // 桌面端
            GestureType.tapDown,  // 点击
            GestureType.longPressMoveUpdate, // 长按并移动
          },
          dim: Dim.x,  // 只在x轴方向上拾取
        ),
      } : {},
      tooltip: TooltipGuide(
        followPointer: [true, true],
        align: Alignment.topRight,
      ),
      crosshair: CrosshairGuide(followPointer: [true, true],
          showLabel: [true,true]),
    coord: RectCoord(
      horizontalRangeUpdater: Defaults.horizontalRangeFocusEvent,
    ),
      changeDataStream: widget.changeDataStreamController,
    );
  }
}

/// 常见传感器类型的预定义配置
class SensorChartConfig {
  /// 温度图表配置
  static SensorChart temperature({
    List<Map<String, dynamic>>? initialData,
    String xField = 'timestamp',
    String yField = 'temperature',
    bool isTimeData = true,
    Color color = Colors.red,
    Future<List<Map<String, dynamic>>> Function()? dataLoader,
    Duration? autoRefreshInterval,
    double? yMin,
    double? yMax,
  }) {
    return SensorChart(
      initialData: initialData,
      xField: xField,
      yFields: [yField],
      labels: ['温度'],
      colors: [color],
      title: '温度',
      yAxisLabel: '温度 (°C)',
      units: ['°C'],
      isTimeData: isTimeData,
      dataLoader: dataLoader,
      autoRefreshInterval: autoRefreshInterval,
      yMin: yMin,
      yMax: yMax,
    );
  }

  /// 湿度图表配置
  static SensorChart humidity({
    List<Map<String, dynamic>>? initialData,
    String xField = 'timestamp',
    String yField = 'humidity',
    bool isTimeData = true,
    Color color = Colors.blue,
    Future<List<Map<String, dynamic>>> Function()? dataLoader,
    Duration? autoRefreshInterval,
    double? yMin,
    double? yMax,
  }) {
    return SensorChart(
      initialData: initialData,
      xField: xField,
      yFields: [yField],
      labels: ['湿度'],
      colors: [color],
      title: '湿度',
      yAxisLabel: '湿度 (%)',
      units: ['%'],
      isTimeData: isTimeData,
      dataLoader: dataLoader,
      autoRefreshInterval: autoRefreshInterval,
      yMin: yMin,
      yMax: yMax,
    );
  }

  /// 气压图表配置
  static SensorChart pressure({
    List<Map<String, dynamic>>? initialData,
    String xField = 'timestamp',
    String yField = 'pressure',
    bool isTimeData = true,
    Color color = Colors.purple,
    Future<List<Map<String, dynamic>>> Function()? dataLoader,
    Duration? autoRefreshInterval,
    double? yMin,
    double? yMax,
  }) {
    return SensorChart(
      initialData: initialData,
      xField: xField,
      yFields: [yField],
      labels: ['气压'],
      colors: [color],
      title: '气压',
      yAxisLabel: '气压 (hPa)',
      units: ['hPa'],
      isTimeData: isTimeData,
      dataLoader: dataLoader,
      autoRefreshInterval: autoRefreshInterval,
      yMin: yMin,
      yMax: yMax,
    );
  }

  /// IMU图表配置（加速度计）
  static SensorChart imuAccelerometer({
    List<Map<String, dynamic>>? initialData,
    String xField = 'timestamp',
    bool isTimeData = true,
    Future<List<Map<String, dynamic>>> Function()? dataLoader,
    Duration? autoRefreshInterval,
    double? yMin,
    double? yMax,
  }) {
    return SensorChart(
      initialData: initialData,
      xField: xField,
      yFields: ['accel_x', 'accel_y', 'accel_z'],
      labels: ['X轴', 'Y轴', 'Z轴'],
      colors: [Colors.red, Colors.green, Colors.blue],
      title: '加速度计',
      yAxisLabel: '加速度 (g)',
      units: ['g', 'g', 'g'],
      isTimeData: isTimeData,
      dataLoader: dataLoader,
      autoRefreshInterval: autoRefreshInterval,
      yMin: yMin,
      yMax: yMax,
    );
  }

  /// 标靶位移图表配置
  static SensorChart targetDisplacement({
    List<Map<String, dynamic>>? initialData,
    String xField = 'timestamp',
    bool isTimeData = true,
    Future<List<Map<String, dynamic>>> Function()? dataLoader,
    Duration? autoRefreshInterval,
    double? yMin,
    double? yMax,
  }) {
    return SensorChart(
      initialData: initialData,
      xField: xField,
      yFields: ['displacement_x', 'displacement_y'],
      labels: ['X轴', 'Y轴'],
      colors: [Colors.orange, Colors.teal],
      title: '标靶位移',
      yAxisLabel: '位移 (mm)',
      units: ['mm', 'mm'],
      isTimeData: isTimeData,
      dataLoader: dataLoader,
      autoRefreshInterval: autoRefreshInterval,
      yMin: yMin,
      yMax: yMax,
    );
  }

  /// 多传感器对比图表
  static SensorChart multiSensor({
    List<Map<String, dynamic>>? initialData,
    String xField = 'timestamp',
    required List<String> yFields,
    required List<String> labels,
    required List<Color> colors,
    required List<String> units,
    bool isTimeData = true,
    Future<List<Map<String, dynamic>>> Function()? dataLoader,
    Duration? autoRefreshInterval,
    double? yMin,
    double? yMax,
    String? title,
  }) {
    return SensorChart(
      initialData: initialData,
      xField: xField,
      yFields: yFields,
      labels: labels,
      colors: colors,
      title: title ?? '多传感器对比',
      units: units,
      isTimeData: isTimeData,
      dataLoader: dataLoader,
      autoRefreshInterval: autoRefreshInterval,
      shareYDomain: false, // 多传感器通常量纲不同，默认不共享Y轴
      yMin: yMin,
      yMax: yMax,
    );
  }
}

/// 使用示例：环境传感器监测
class EnvironmentSensorExample extends StatelessWidget {
  const EnvironmentSensorExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  SensorChart(
      title: '位移环境传感器数据',
      xField: 'timestamp',
      yFields: ['temperature', 'humidity', 'pressure','x', 'y'],
      labels: ['温度', '湿度', '气压', "X", "Y"],
      colors: [Colors.red, Colors.blue, Colors.purple, Colors.green, Colors.orange],
      units: ['°C', '%', 'hPa', 'mm', 'mm'],
      isTimeData: true,
      shareYDomain: false, // 不同量纲的数据，不共享Y轴
      autoRefreshInterval: Duration(milliseconds: 1000),
      dataLoader: () async {
        // 模拟网络请求延迟
        await Future.delayed(Duration(milliseconds: 1));

      // 生成100个带有随机值的数据点
        final random = Random();
        final now = DateTime.now();
        final List<Map<String, dynamic>> dataPoints = [];

        for (int i = 0; i < 1; i++) {
          // 创建从当前时间往前每10毫秒一个数据点
          final timestamp = now.subtract(Duration(milliseconds: 1000 * (99 - i)));
          dataPoints.add({
            'timestamp': timestamp,
            'temperature': 20 + 5 * random.nextDouble(),
            'humidity': 40 + 20 * random.nextDouble(),
            'pressure': 1000 + 20 * random.nextDouble(),
            'x': 2 + 1 * random.nextDouble(),
            'y': -12 + 1 * random.nextDouble(),
          });
        }

        return dataPoints;
      },
    );
  }
}

/// 使用示例：温度传感器详细监测
class TemperatureSensorExample extends StatelessWidget {
  const TemperatureSensorExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('温度监测'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SensorChartConfig.temperature(
          yMin: 15,  // 设置Y轴最小值
          yMax: 30,  // 设置Y轴最大值
          autoRefreshInterval: Duration(seconds: 1),
          dataLoader: () async {
            await Future.delayed(Duration(milliseconds: 300));
            final random = Random();
            return [
              {
                'timestamp': DateTime.now(),
                'temperature': 20 + 5 * random.nextDouble(),
              }
            ];
          },
        ),
      ),
    );
  }
}
class _DataWindowManager {
  final int maxRawPoints;
  final int maxVisiblePoints;
  final List<Map<String, dynamic>> _allData = [];
  List<Map<String, dynamic>>? _sampledData;
  bool _needsResampling = true;
  final Map<String, List<Map<String, dynamic>>> _cachedViews = {};

  _DataWindowManager({
    this.maxRawPoints = 100000,
    this.maxVisiblePoints = 2000,
  });

  void addData(List<Map<String, dynamic>> newData) {
    _allData.addAll(newData);
    debugPrint('添加数据: ${newData.length} 条, 当前总数据量: ${_allData.length} 条');
    if (_allData.length > maxRawPoints) {
      _allData.removeRange(0, _allData.length - maxRawPoints);
    }
    _needsResampling = true;
    _cachedViews.clear();
  }

  void clear() {
    _allData.clear();
    _sampledData = null;
    _cachedViews.clear();
    _needsResampling = true;
  }

  List<Map<String, dynamic>> getVisibleData() {
    if (_needsResampling || _sampledData == null) {
      _resampleData();
    }
    return _sampledData!;
  }

  void _resampleData() {
    if (_allData.isEmpty) {
      _sampledData = [];
      return;
    }

    if (_allData.length <= maxVisiblePoints) {
      _sampledData = List.from(_allData);
    } else {
      _sampledData = _downSampleData(_allData, maxVisiblePoints);
    }
    _needsResampling = false;
  }

  List<Map<String, dynamic>> _downSampleData(List<Map<String, dynamic>> data, int targetPoints) {
    final sampledData = <Map<String, dynamic>>[];
    final step = data.length / targetPoints;

    // 保留第一个点
    sampledData.add(data.first);

    for (int i = 1; i < targetPoints - 1; i++) {
      final index = (i * step).floor();
      if (index < data.length) {
        sampledData.add(data[index]);
      }
    }

    // 保留最后一个点
    if (data.length > 1) {
      sampledData.add(data.last);
    }

    return sampledData;
  }

  List<Map<String, dynamic>> getOptimizedData(List<String> visibleFields, String xField) {
    final fields = [xField, ...visibleFields];
    // 获取已采样的数据
    final baseData = getVisibleData();

    // 优化数据结构，只保留必要字段
    final optimizedData = <Map<String, dynamic>>[];
    for (final item in baseData) {
      final newItem = <String, dynamic>{};
      for (final field in fields) {
        if (item.containsKey(field)) {
          newItem[field] = item[field];
        }
      }
      optimizedData.add(newItem);
    }

    return optimizedData;
  }

  int get length => _allData.length;
}
