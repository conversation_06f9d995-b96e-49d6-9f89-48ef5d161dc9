import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';
import 'package:intl/intl.dart';

import 'flexible-line-chart.dart';

/// 传感器数据Sparkline组件
/// 轻量级迷你折线图，用于在有限空间内展示数据趋势
class SensorSparkline extends StatelessWidget {
  /// 数据源
  final List<Map<String, dynamic>> data;

  /// 数据字段名
  final String field;

  /// 线条颜色
  final Color color;

  /// 是否显示点
  final bool showPoints;

  /// 是否显示最新值
  final bool showLatestValue;

  /// 是否显示填充区域
  final bool showFillArea;

  /// 是否显示网格线
  final bool showGrid;

  /// 是否显示坐标轴
  final bool showAxis;

  /// 小数位数
  final int decimalPlaces;

  /// 单位文本
  final String unit;

  /// 图表高度
  final double height;

  /// 图表宽度
  final double width;

  /// 组件背景色
  final Color? backgroundColor;

  /// X轴字段名
  final String xField;

  /// 是否为时间数据
  final bool isTimeData;

  /// 线条宽度
  final double lineWidth;

  /// 最大显示点数
  final int? maxPoints;

  /// 构造函数
  const SensorSparkline({
    Key? key,
    required this.data,
    required this.field,
    this.color = Colors.blue,
    this.showPoints = false,
    this.showLatestValue = true,
    this.showFillArea = false,
    this.showGrid = false,
    this.showAxis = false,
    this.decimalPlaces = 1,
    this.unit = '',
    this.height = 50,
    this.width = 120,
    this.backgroundColor,
    this.xField = 'timestamp',
    this.isTimeData = true,
    this.lineWidth = 1.5,
    this.maxPoints,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return SizedBox(
        height: height,
        width: width,
        child: Center(
          child: Text(
            '暂无数据',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 10,
            ),
          ),
        ),
      );
    }

    // 过滤数据，只保留指定最大点数
    final List<Map<String, dynamic>> filteredData = maxPoints != null && data.length > maxPoints!
        ? data.sublist(data.length - maxPoints!)
        : data;

    // 获取最新值
    final latestValue = filteredData.isNotEmpty
        ? filteredData.last[field] as num
        : 0;

    // 格式化最新值
    final latestValueText = '${latestValue.toStringAsFixed(decimalPlaces)}$unit';

    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        children: [
          // 主图表
          Positioned.fill(
            child: _buildChart(filteredData, context),
          ),

          // 最新值文本
          if (showLatestValue)
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  latestValueText,
                  style: TextStyle(
                    color: color,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart(List<Map<String, dynamic>> chartData, BuildContext context) {
    // 创建图表变量
    final variables = <String, Variable>{};

    // X轴变量
    if (isTimeData) {
      variables['x'] = Variable(
        accessor: (datum) => datum[xField] as DateTime,
        scale: TimeScale(),
      );
    } else {
      variables['x'] = Variable(
        accessor: (datum) => datum[xField],
        scale: LinearScale(),
      );
    }

    // Y轴变量
    variables[field] = Variable(
      accessor: (datum) => datum[field] as num,
      scale: LinearScale(),
    );

    // 创建图表元素
    final List<Mark> marks = [];

    // 添加填充区域
    if (showFillArea) {
      marks.add(
        AreaMark(
          position: Varset('x') * Varset(field),
          color: ColorEncode(value: color.withOpacity(0.1)),
          shape: ShapeEncode(value: BasicAreaShape(smooth: true)),
        ),
      );
    }

    // 添加线条
    marks.add(
      LineMark(
        position: Varset('x') * Varset(field),
        color: ColorEncode(value: color),
        size: SizeEncode(value: lineWidth),
        shape: ShapeEncode(value: BasicLineShape(smooth: true)),
      ),
    );

    // 添加数据点
    if (showPoints) {
      marks.add(
        PointMark(
          position: Varset('x') * Varset(field),
          color: ColorEncode(value: color),
          size: SizeEncode(value: 3),
        ),
      );
    }

    return Chart(
      data: chartData,
      variables: variables,
      marks: marks,
      axes: showAxis ? [
        Defaults.horizontalAxis,
        Defaults.verticalAxis,
      ] : [],
      coord: RectCoord(
        color: Colors.transparent,
      ),
      padding: (Size size) => EdgeInsets.symmetric(
        horizontal: showAxis ? 8.0 : 0.0,
        vertical: showAxis ? 8.0 : 0.0,
      ),
    );
  }
}

/// 创建水平排列的多个Sparkline视图
class SparklineRow extends StatelessWidget {
  /// 多个Sparkline配置
  final List<SparklineConfig> configs;

  /// 间距
  final double spacing;

  /// 构造函数
  const SparklineRow({
    Key? key,
    required this.configs,
    this.spacing = 8.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(configs.length * 2 - 1, (index) {
        // 添加Sparkline或间隔
        if (index % 2 == 0) {
          final configIndex = index ~/ 2;
          final config = configs[configIndex];
          return _buildSparklineCard(context, config);
        } else {
          return SizedBox(width: spacing);
        }
      }),
    );
  }

  /// 构建单个Sparkline卡片
  Widget _buildSparklineCard(BuildContext context, SparklineConfig config) {
    return Expanded(
      child: Card(
        elevation: 1,
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                config.title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
              SizedBox(height: 4),
              SensorSparkline(
                data: config.data,
                field: config.field,
                color: config.color,
                unit: config.unit,
                width: double.infinity,
                height: 40,
                showLatestValue: true,
                showFillArea: true,
                lineWidth: 1.5,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Sparkline配置
class SparklineConfig {
  /// 标题
  final String title;

  /// 数据源
  final List<Map<String, dynamic>> data;

  /// 数据字段
  final String field;

  /// 颜色
  final Color color;

  /// 单位
  final String unit;

  /// 构造函数
  SparklineConfig({
    required this.title,
    required this.data,
    required this.field,
    required this.color,
    this.unit = '',
  });
}

/// 实时传感器数据仪表板示例
class SensorDashboardExample extends StatefulWidget {
  const SensorDashboardExample({Key? key}) : super(key: key);

  @override
  _SensorDashboardExampleState createState() => _SensorDashboardExampleState();
}

class _SensorDashboardExampleState extends State<SensorDashboardExample> {
  // 温度数据
  List<Map<String, dynamic>> _temperatureData = [];

  // 湿度数据
  List<Map<String, dynamic>> _humidityData = [];

  // 气压数据
  List<Map<String, dynamic>> _pressureData = [];

  // 定时器
  Timer? _timer;

  // 随机数生成器
  final Random _random = Random();

  @override
  void initState() {
    super.initState();

    // 生成初始历史数据
    _generateInitialData();

    // 设置定时更新
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _updateData();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  /// 生成初始历史数据
  void _generateInitialData() {
    final now = DateTime.now();

    for (int i = 20; i > 0; i--) {
      final timestamp = now.subtract(Duration(seconds: i));

      // 温度数据
      _temperatureData.add({
        'timestamp': timestamp,
        'value': 22 + _random.nextDouble() * 3,
      });

      // 湿度数据
      _humidityData.add({
        'timestamp': timestamp,
        'value': 45 + _random.nextDouble() * 10,
      });

      // 气压数据
      _pressureData.add({
        'timestamp': timestamp,
        'value': 1013 + _random.nextDouble() * 5,
      });
    }
  }

  /// 更新数据
  void _updateData() {
    setState(() {
      final now = DateTime.now();

      // 添加新温度数据
      _temperatureData.add({
        'timestamp': now,
        'value': 22 + _random.nextDouble() * 3,
      });

      // 添加新湿度数据
      _humidityData.add({
        'timestamp': now,
        'value': 45 + _random.nextDouble() * 10,
      });

      // 添加新气压数据
      _pressureData.add({
        'timestamp': now,
        'value': 1013 + _random.nextDouble() * 5,
      });

      // 限制数据点数量
      if (_temperatureData.length > 60) {
        _temperatureData.removeAt(0);
      }

      if (_humidityData.length > 60) {
        _humidityData.removeAt(0);
      }

      if (_pressureData.length > 60) {
        _pressureData.removeAt(0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('传感器数据仪表板'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              '实时传感器数据',
              style: Theme.of(context).textTheme.headlineSmall,
            ),

            SizedBox(height: 8),

            // 时间戳
            Text(
              '最后更新: ${DateFormat('HH:mm:ss').format(DateTime.now())}',
              style: TextStyle(color: Colors.grey),
            ),

            SizedBox(height: 16),

            // Sparkline行
            SparklineRow(
              configs: [
                SparklineConfig(
                  title: '温度',
                  data: _temperatureData,
                  field: 'value',
                  color: Colors.red,
                  unit: '°C',
                ),
                SparklineConfig(
                  title: '湿度',
                  data: _humidityData,
                  field: 'value',
                  color: Colors.blue,
                  unit: '%',
                ),
                SparklineConfig(
                  title: '气压',
                  data: _pressureData,
                  field: 'value',
                  color: Colors.purple,
                  unit: 'hPa',
                ),
              ],
            ),

            SizedBox(height: 24),

            // 详细图表标题
            Text(
              '详细数据趋势',
              style: Theme.of(context).textTheme.titleLarge,
            ),

            SizedBox(height: 16),

            // 温度详细图表
            Container(
              height: 200,
              child: SensorChart(
                title: '温度趋势',
                xField: 'timestamp',
                yFields: ['value'],
                labels: ['温度'],
                colors: [Colors.red],
                units: ['°C'],
                isTimeData: true,
                initialData: _temperatureData,
                showPoints: false,
              ),
            ),

            SizedBox(height: 16),

            // 湿度详细图表
            Container(
              height: 200,
              child: SensorChart(
                title: '湿度趋势',
                xField: 'timestamp',
                yFields: ['value'],
                labels: ['湿度'],
                colors: [Colors.blue],
                units: ['%'],
                isTimeData: true,
                initialData: _humidityData,
                showPoints: false,
              ),
            ),

            SizedBox(height: 16),

            // 气压详细图表
            Container(
              height: 200,
              child: SensorChart(
                title: '气压趋势',
                xField: 'timestamp',
                yFields: ['value'],
                labels: ['气压'],
                colors: [Colors.purple],
                units: ['hPa'],
                isTimeData: true,
                initialData: _pressureData,
                showPoints: false,
              ),
            ),
          ],
        ),
      ),
    );
  }
}