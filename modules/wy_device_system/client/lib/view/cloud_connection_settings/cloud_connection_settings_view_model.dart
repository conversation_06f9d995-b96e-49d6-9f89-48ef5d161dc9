import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:utils/utils.dart';
import 'package:dio/dio.dart';

import '../../bloc/wy_device_blocs.dart';

class CloudConnectionViewModel with ChangeNotifier {

  final WyDeviceBloc wyDeviceBloc;

  bool isCloud1Enabled = true;
  bool isCloud2Enabled = false;
  Map<String, dynamic> cloudConfig = {};
  bool isLoading = true;



  // 添加连接状态属性
  bool isCloud1Connected = false;
  bool isCloud2Connected = false;

  // 自动保存相关状态
  bool _isSaving = false;
  bool _hasChanges = false;
  Timer? _saveDebounceTimer;
  Timer? _connectionStatusTimer;

  String? _saveError ;

  // 获取当前保存状态
  bool get isSaving => _isSaving;
  bool get hasChanges => _hasChanges;
  String? get saveError => _saveError;

  CloudConnectionViewModel(this.wyDeviceBloc) {
    _init();
  }

  Future<void> _init() async {
    isLoading = true;
    notifyListeners();

    try {
      await _loadDefaultConfig();
    } catch (e) {
      debugPrint('加载云配置失败: $e');
      // 加载默认配置
      await _loadDefaultConfig();
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  List<String> get platformTypes => ['StdMqtt','Inteagle'];




  // 添加连接状态方法
  Future<void> connectCloud(String cloudKey) async {
    try {

      final cloudNum = cloudKey == 'cloud1' ? 1 : 2;
      if (cloudNum == 1) {
        isCloud1Connected = true;
      } else {
        isCloud2Connected = true;
      }
      notifyListeners();
    } catch (e) {
      String errorMessage = e is DioException
          ? (e.response?.data != null
          ? (e.response?.data['msg'] ?? e.response?.data.toString())
          : e.message ?? e.toString())
          : e.toString();
      wyDeviceBloc.add(ErrorOccurred("加载云配置失败: $errorMessage"));

    }
  }


  Future<void> disconnectCloud(String cloudKey) async {
    final cloudNum = cloudKey == 'cloud1' ? 1 : 2;
    if (cloudNum == 1) {
      isCloud1Connected = false;
    } else {
      isCloud2Connected = false;
    }
    notifyListeners();
  }


  Future<void> _loadDefaultConfig() async {
    isLoading = true;
    notifyListeners();

    try {
      var result = await HttpUtil.instance.client.get('/config/cloud',
      );

      var data = result.data['data'];

      debugPrint('云配置数据: $data');

      var cloud1Config = data['cloud1'];
      var cloud2Config = data['cloud2'];
      var c1ConnectStatus = data['cloud_connect_status']['cloud1']?? false;
      var c2ConnectStatus = data['cloud_connect_status']['cloud2']?? false;
      isCloud1Connected = c1ConnectStatus;
      isCloud2Connected = c2ConnectStatus;


      cloudConfig['cloud1'] = cloud1Config ?? getDefaultCloudConfig();
      cloudConfig['cloud2'] = cloud2Config ?? getDefaultCloudConfig();

      // 检查配置中云的启用状态
      isCloud1Enabled = cloudConfig['cloud1']['enabled'] ?? false;
      isCloud2Enabled = cloudConfig['cloud2']['enabled'] ?? false;

      _startConnectionStatusTimer();
      isLoading = false;
      notifyListeners();
    } catch (e) {
      String errorMessage = e is DioException
          ? (e.response?.data != null
          ? (e.response?.data['msg'] ?? e.response?.data.toString())
          : e.message ?? e.toString())
          : e.toString();
      wyDeviceBloc.add(ErrorOccurred("加载云配置失败: $errorMessage"));

      // 使用默认空配置
      cloudConfig = {
        "cloud1": getDefaultCloudConfig(),
        "cloud2": getDefaultCloudConfig()
      };

      isLoading = false;
      notifyListeners();
    }
  }

  Map<String, dynamic> getDefaultCloudConfig() {
    return {
      "platform_connect": "StdMqtt",
      "enabled": false,
      "host": "broker.emqx.io",
      "port": 1883,
      "client_id": "wy_device_${DateTime.now().millisecondsSinceEpoch}",
      "username": "",
      "password": "",
      "qos": 1,
      "keep_alive": {"secs": 60, "nanos": 0},
      "clean_session": true,
      "topics": {
        "telemetry": "v1/things/wy/telemetry",
        "attributes": "v1/things/wy/attributes",
        "commands": "v1/things/wy/rpc",
        "response": "v1/things/wy/rpc/resp"
      },
      "ssl_enabled": false,
      "payload_format": "Json"
    };
  }

  void toggleCloud(int cloudNum, bool enabled) {
    String cloudKey = cloudNum == 1 ? 'cloud1' : 'cloud2';

    if (cloudNum == 1) {
      isCloud1Enabled = enabled;
    } else {
      isCloud2Enabled = enabled;
    }
    if (cloudConfig[cloudKey] == null) {
      cloudConfig[cloudKey] = getDefaultCloudConfig();
    }

    // 同步更新配置中的 enabled 字段
    cloudConfig[cloudKey]['enabled'] = enabled;
    notifyListeners();
    _debouncedSave();
  }

// 在 CloudConnectionViewModel 中
  void updatePlatformType(String cloudKey, String platformType) {
    if (cloudConfig[cloudKey] == null) {
      cloudConfig[cloudKey] = getDefaultCloudConfig();
    }

    // 先更新平台类型
    cloudConfig[cloudKey]['platform_connect'] = platformType;

    // 如果是 Inteagle，配置预设值
    if (platformType == 'Inteagle') {
      cloudConfig[cloudKey]['host'] = 'broker.emqx.io';
      cloudConfig[cloudKey]['port'] = 1883;
      cloudConfig[cloudKey]['ssl_enabled'] = false;
      cloudConfig[cloudKey]['client_id'] = 'inteagle_${DateTime.now().millisecondsSinceEpoch}';
      cloudConfig[cloudKey]['username'] = '';
      cloudConfig[cloudKey]['password'] = '';
    }

    notifyListeners();

    // 平台类型变更是重要设置，立即保存
    _debouncedSave();
  }

  void updateCloudConfig(String cloudKey, String field, dynamic value) {
    cloudConfig[cloudKey][field] = value;
    notifyListeners();
    _debouncedSave();
  }

  void updateTopicConfig(String cloudKey, String topic, String value) {
    cloudConfig[cloudKey]['topics'][topic] = value;
    notifyListeners();
    _debouncedSave();
  }

  void updateProvisionConfig(String cloudKey, String field, dynamic value) {
    if (!cloudConfig[cloudKey].containsKey('provision')) {
      cloudConfig[cloudKey]['provision'] = {
        "provision_key": "",
        "provision_secret": "",
        "username": "provision",
        "password": "",
        "is_registered": false
      };
    }

    cloudConfig[cloudKey]['provision'][field] = value;
    notifyListeners();
  }

  Future<bool> saveConfig() async {
    try {
      final Map<String, dynamic> resultConfig = {};

        resultConfig['cloud1'] = cloudConfig['cloud1'];
        resultConfig['cloud2'] = cloudConfig['cloud2'];

      // 提交http 请求保存
      var result = await HttpUtil.instance.client.post('/config/cloud',
        data: json.encode(resultConfig)
      );
      debugPrint('保存云配置成功: $result');

      return true;
    } catch (e) {
      this._saveError = e.toString();
      String errorMessage = e is DioException
          ? (e.response?.data != null
          ? (e.response?.data['msg'] ?? e.response?.data.toString())
          : e.message ?? e.toString())
          : e.toString();
      wyDeviceBloc.add(ErrorOccurred("加载云配置失败: $errorMessage"));
      return false;
    }
  }

  // 自动保存功能
  Future<void> _debouncedSave() async {
    _hasChanges = true;
    notifyListeners();

    // 取消现有计时器
    _saveDebounceTimer?.cancel();

    // 设置新计时器
    _saveDebounceTimer = Timer(const Duration(milliseconds: 800), () async {
      if (_hasChanges) {
        _isSaving = true;
        notifyListeners();

        try {
          await saveConfig();
          _hasChanges = false;
        } catch (e) {
          debugPrint('自动保存失败: $e');
        } finally {
          _isSaving = false;
          notifyListeners();
        }
      }
    });
  }

  // 用于定时检查云连接状态
  void _startConnectionStatusTimer() {
    // 取消之前的定时器（如果存在）
    _connectionStatusTimer?.cancel();

    // 每30秒检查一次连接状态
    _connectionStatusTimer = Timer.periodic(const Duration(seconds: 10), (_) async {
      try {
        var result = await HttpUtil.instance.client.get('/cloud/status');

        var statusData = result.data['data'];


        // 更新连接状态
        isCloud1Connected = statusData['cloud1'] ?? false;
        isCloud2Connected = statusData['cloud2'] ?? false;
        notifyListeners();
      } catch (e) {
        debugPrint('获取云连接状态失败: $e');
      }
    });
  }

  @override
  void dispose() {
    _saveDebounceTimer?.cancel();
    _connectionStatusTimer?.cancel();
    super.dispose();
  }

}