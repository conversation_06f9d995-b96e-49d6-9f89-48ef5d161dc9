import 'package:client/bloc/web_socket_bloc.dart';
import 'package:client/model/measurement_query.dart';
import 'package:client/model/wy_device.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

// 设备状态
class StatusItem {
  final WyDeviceStatus status;
  final IconData icon;
  final Color color;
  final String label;

  StatusItem({
    required this.status,
    required this.icon,
    required this.color,
    required this.label,
  });
}

final List<StatusItem> statusList = [
  StatusItem(
      status: WyDeviceStatus.idle,
      icon: Icons.pause_circle,
      color: Colors.grey,
      label: '空闲'),
  StatusItem(
      status: WyDeviceStatus.initializing,
      icon: Icons.sync,
      color: Colors.orange,
      label: '初始化中'),
  StatusItem(
      status: WyDeviceStatus.testing,
      icon: Icons.science,
      color: Colors.purple,
      label: '调试中'),
  StatusItem(
      status: WyDeviceStatus.measuring,
      icon: Icons.monitor_heart,
      color: Colors.green,
      label: '测量中'),
  StatusItem(
      status: WyDeviceStatus.accuracyCalibration,
      icon: Icons.build_circle_outlined,
      color: Colors.yellow,
      label: '标定中')
];

StatusItem findStatusItem(WyDeviceStatus deviceStatus) {
  try {
    return statusList.firstWhere(
      (item) => item.status == deviceStatus,
    );
  } catch (e) {
    return statusList[0];
  }
}

// websocket状态
class ConnectionStatus {
  final IconData icon;
  final Color color;
  final String tooltip;

  ConnectionStatus({
    required this.icon,
    required this.color,
    required this.tooltip,
  });

  factory ConnectionStatus.fromState(WebSocketState state) {
    if (state is WebSocketConnected || state is WebSocketMessageReceived) {
      return ConnectionStatus(
        icon: Icons.cloud_done,
        color: Colors.green,
        tooltip: '已连接',
      );
    } else if (state is WebSocketConnecting) {
      return ConnectionStatus(
        icon: Icons.cloud_sync,
        color: Colors.amber,
        tooltip: '连接中',
      );
    } else if (state is WebSocketDisconnected || state is WebSocketError) {
      return ConnectionStatus(
        icon: Icons.cloud_off,
        color: Colors.red,
        tooltip: state is WebSocketError ? '错误' : '未连接',
      );
    } else {
      return ConnectionStatus(
        icon: Icons.cloud,
        color: Colors.grey,
        tooltip: '未知状态',
      );
    }
  }
}

// 数据点模型
class DataPoint {
  final DateTime timestamp;
  final double sigmaX;
  final double sigmaY;

  DataPoint({
    required this.timestamp,
    required this.sigmaX,
    required this.sigmaY,
  });
}

// 数据类型
class DataTypeItem {
  final String label;
  final MeasurementType value;
  final List<String> fields;

  DataTypeItem(
      {required this.label, required this.value, required this.fields});
}

final List<DataTypeItem> dataTypeList = [
  DataTypeItem(
      value: MeasurementType.displacement,
      label: '实时数据监测',
      fields: ['displacement_x', 'displacement_y']),
  // DataTypeItem(
  //     value: MeasurementType.acceleration,
  //     label: '环境温湿度',
  //     fields: ['accel_magnitude']),
  // DataTypeItem(
  //     value: MeasurementType.envStatus,
  //     label: 'envStatus',
  //     fields: ['temperature', 'humidity', 'pressure']),
  // DataTypeItem(
  //     value: MeasurementType.imu,
  //     label: 'imu',
  //     fields: ['accel_x', 'accel_y', 'accel_z']),
  // DataTypeItem(
  //     value: MeasurementType.multiSensor,
  //     label: 'multiSensor',
  //     fields: [
  //       'temperature',
  //       'humidity',
  //       'accel_magnitude',
  //       'displacement_magnitude'
  //     ]),
];

DataTypeItem findDataType(MeasurementType value) {
  try {
    return dataTypeList.firstWhere(
      (item) => item.value == value,
    );
  } catch (e) {
    return dataTypeList[0];
  }
}

// 可以快速选择的时间选项
class TimeOption {
  final String label;
  final Duration? value;
  TimeOption({required this.label, this.value});
}

final List<TimeOption> historyTimeOptions = [
  TimeOption(label: '3天', value: const Duration(days: 3)),
  TimeOption(label: '7天', value: const Duration(days: 7)),
  TimeOption(label: '30天', value: const Duration(days: 30)),
  TimeOption(label: '90天', value: const Duration(days: 90)),
  TimeOption(label: '自定义'),
];

final List<TimeOption> realTimeOptions = [
  TimeOption(label: '1分钟', value: const Duration(minutes: 1)),
  TimeOption(label: '5分钟', value: const Duration(minutes: 5)),
  TimeOption(label: '10分钟', value: const Duration(minutes: 10)),
];

// 设备设置的选项
class SettingOption {
  final String label;
  final IconData icon;
  SettingOption({required this.label, required this.icon});
}

final List<SettingOption> settingOptions = [
  SettingOption(label: '设备信息', icon: TDIcons.logo_codesandbox),
  SettingOption(label: '标靶管理', icon: TDIcons.bridge),
  SettingOption(label: '参数配置', icon: TDIcons.adjustment),
  SettingOption(label: '补光灯', icon: TDIcons.brightness),
  SettingOption(label: '云连接', icon: TDIcons.object_storage),
  SettingOption(label: '固件升级', icon: TDIcons.uninstall),
  SettingOption(label: '查看图像', icon: TDIcons.image_1),
  SettingOption(label: '启动测量', icon: TDIcons.camera_1),
  SettingOption(label: '停止测量', icon: TDIcons.screenshot),
  SettingOption(label: '重启', icon: TDIcons.swap),
  SettingOption(label: '关机', icon: TDIcons.poweroff),
];
