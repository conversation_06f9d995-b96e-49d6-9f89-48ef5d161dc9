import 'package:client/bloc/web_socket_bloc.dart';
import 'package:client/bloc/wy_device_blocs.dart';
import 'package:client/model/wy_device.dart';
import 'package:client/view/device_home/device_status_view_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HomeAppBar extends StatefulWidget implements PreferredSizeWidget {
  const HomeAppBar({super.key});

  @override
  State<HomeAppBar> createState() => _HomeAppBarState();
  @override
  Size get preferredSize => const Size.fromHeight(50);
}

class _HomeAppBarState extends State<HomeAppBar> {
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: const Text('视觉位移计客户端'),
      centerTitle: false,
      actions: [
        //设备状态指示器
        BlocBuilder<WyDeviceBloc, WyDeviceState>(builder: (context, state) {
          final deviceStatus = state.wyDeviceStatus ?? WyDeviceStatus.idle;
          final statusItem = findStatusItem(deviceStatus);

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(statusItem.icon, color: statusItem.color),
              const SizedBox(width: 4),
              Text(
                statusItem.label,
                style: TextStyle(color: statusItem.color, fontSize: 12),
              ),
            ],
          );
        }),
        BlocBuilder<WebSocketBloc, WebSocketState>(builder: (context, state) {
          final status = ConnectionStatus.fromState(state);
          return IconButton(
            icon: Icon(status.icon, color: status.color),
            tooltip: status.tooltip,
            onPressed: () {
              if (state is WebSocketConnected) {
                context.read<WebSocketBloc>().add(DisconnectWebSocket());
              } else if (state is WebSocketDisconnected ||
                  state is WebSocketError) {
                context
                    .read<WebSocketBloc>()
                    .add(ConnectWebSocket("ws://*************:9999/ws"));
              }
            },
          );
        })
      ],
    );
  }
}
