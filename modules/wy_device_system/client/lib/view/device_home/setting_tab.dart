import 'package:client/bloc/targets_blocs.dart';
import 'package:client/bloc/wy_device_blocs.dart';
import 'package:client/model/wy_device.dart';
import 'package:client/view/cloud_connection_settings/cloud_connection_settings.dart';
import 'package:client/view/components/wy_device_setting/setting_params.dart';
import 'package:client/view/components/wy_device_setting/setting_pwm.dart';
import 'package:client/view/device_home/device_status_view_model.dart';

import 'package:client/view/device_home/measurement_start_panel.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:utils/utils.dart';

class SettingTab extends StatefulWidget {
  final void Function(String) toPath;
  const SettingTab({super.key, required this.toPath});

  @override
  State<SettingTab> createState() => _SettingTabState();
}

class _SettingTabState extends State<SettingTab> {
  WyDeviceStatus _status = WyDeviceStatus.idle;
  bool _isShowInfo = false;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WyDeviceBloc, WyDeviceState>(builder: (context, state) {
      _status = state.wyDeviceStatus ?? WyDeviceStatus.idle;
      final deviceStatus = findStatusItem(_status);
      return CustomScrollView(slivers: [
        SliverToBoxAdapter(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _isShowInfo = !_isShowInfo;
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 10, // 圆点宽度
                        height: 10,
                        // 圆点高度
                        decoration: BoxDecoration(
                          color: deviceStatus.color, // 圆点颜色
                          borderRadius: BorderRadius.circular(5), // 圆角半径，确保为圆形
                        ),
                      ),
                      const SizedBox(
                        width: 12,
                      ),
                      TDText('设备状态：${deviceStatus.label}',
                          font: TDTheme.of(context).fontTitleExtraLarge),
                    ],
                  ),
                  _isShowInfo
                      ? const Icon(TDIcons.chevron_up)
                      : const Icon(TDIcons.chevron_down)
                ],
              ),
            ),
          ),
        ),
        if (_isShowInfo)
          SliverToBoxAdapter(
            child: _buildDeviceInfo(context, state),
          ),
        SliverPadding(
          padding: const EdgeInsets.all(12.0),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
              maxCrossAxisExtent: 120, // 卡片最大宽度
            ),
            delegate: SliverChildBuilderDelegate(
              (buildContext, index) =>
                  _buildControlItem(context, state, settingOptions[index]),
              childCount: settingOptions.length,
            ),
          ),
        )
      ]);
    });
  }

  // 设备信息
  Widget _buildDeviceInfo(context, state) {
    final deviceInfo = state.deviceAttribute;
    return TDCellGroup(
      cells: [
        TDCell(title: 'MCU固件版本：${deviceInfo?.wyMCUFirmwareV}'),
        TDCell(title: '固件版本：${deviceInfo?.currentFwVersion}'),
        TDCell(title: '设备型号：${deviceInfo?.model}'),
        TDCell(title: '设备ID：${deviceInfo?.wyID}'),
        const TDCell(title: '最后上线时间：2025/06/10 11:00:00'),
        const TDCell(title: '最后下线时间：2025/06/10 11:00:00'),
      ],
    );
  }

  // 创建按钮组件
  Widget _buildControlItem(context, state, SettingOption item) {
    return GestureDetector(
      onTap: () {
        switch (item.label) {
          case '设备信息':
            _showDeviceInfo(context, state);
            break;
          case '标靶管理':
            widget.toPath('targetManage');
            break;
          case '参数配置':
            _setParameter(context, state);
            break;
          case '云连接':
            _setCloudConnection(context);
            break;
          case '固件升级':
            _updateFw(context);
            break;
          case '补光灯':
            _setLight(context, state);
            break;
          case '查看图像':
            // widget.toPath('history');
            widget.toPath('viewImage');
            break;
          case '启动测量':
            startMeasure(context, _status);
            break;
          case '停止测量':
            stopMeasuring(context, _status);
            break;
          case '重启':
            _restart(context);
            break;
          case '关机':
            _close(context);
            break;
        }
      },
      child: Column(
        spacing: 8,
        children: [
          TDAvatar(
            size: TDAvatarSize.medium,
            type: TDAvatarType.icon,
            shape: TDAvatarShape.square,
            icon: item.icon,
          ),
          TDText(
            item.label,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 点击设备信息按钮展示设备信息
  void _showDeviceInfo(context, state) {
    Navigator.of(context).push(
      TDSlidePopupRoute(
        slideTransitionFrom: SlideTransitionFrom.center,
        builder: (buildContext) {
          final screenSize = MediaQuery.of(context).size;
          return TDPopupCenterPanel(
            closeClick: () {
              Navigator.maybePop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              width: screenSize.width * 0.8,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 16,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: _buildDeviceInfo(context, state),
            ),
          );
        },
      ),
    );
  }

  // 参数配置
  void _setParameter(context, WyDeviceState state) {
    Navigator.of(context).push(
      TDSlidePopupRoute(
        slideTransitionFrom: SlideTransitionFrom.center,
        builder: (buildContext) {
          return SettingParams(
              deviceAttribute: state.deviceAttribute,
              onConfirm: (attributes) {
                print(state.deviceAttribute!.toJson());
                print(attributes);

                // 更新

                // _updateAttribute(updatedAttributes);
                Navigator.pop(context);
              });
        },
      ),
    );
  }

  void _setCloudConnection(context) {
    Navigator.of(context).push(
      TDSlidePopupRoute(
        modalBarrierColor: TDTheme.of(context).fontGyColor2,
        slideTransitionFrom: SlideTransitionFrom.center,
        builder: (buildContext) {
          final screenSize = MediaQuery.of(context).size;
          final wyDeviceBloc = BlocProvider.of<WyDeviceBloc>(context);
          return TDPopupCenterPanel(
              closeClick: () {
                Navigator.maybePop(context);
              },
              child: MultiProvider(
                providers: [
                  BlocProvider<WyDeviceBloc>.value(value: wyDeviceBloc),
                ],
                child: BlocBuilder<WyDeviceBloc, WyDeviceState>(
                  builder: (context, state) {
                    return Container(
                      padding: const EdgeInsets.fromLTRB(12, 36, 12, 24),
                      width: screenSize.width * 0.8,
                      height: screenSize.height * 0.6,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 16,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const CloudConnectionSettings(),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }

  // 固件升级
  void _updateFw(context) {
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return TDAlertDialog(
          title: '固件升级',
          content: '确定要升级吗？',
          rightBtnAction: () {
            // TODO:固件升级
            Navigator.pop(buildContext);
          },
        );
      },
    );
  }

  // 设置补光灯
  void _setLight(context, WyDeviceState state) {
    Navigator.of(context).push(
      TDSlidePopupRoute(
        slideTransitionFrom: SlideTransitionFrom.center,
        builder: (buildContext) {
          return TDPopupCenterPanel(
            closeClick: () {
              Navigator.maybePop(context);
            },
            child: SettingPwm(
                deviceAttribute: state.deviceAttribute,
                onConfirm: (attributes) {
                  print(state.deviceAttribute!.toJson());
                  WyDeviceAttribute updatedAttributes =
                      state.deviceAttribute!.copyWith(
                    pwm: attributes['pwm'] as int?,
                    pwSelfAdaptive: attributes['pwSelfAdaptive'] as bool?,
                    wyReportSelfAdaptiveInterval:
                        attributes['wyReportSelfAdaptiveInterval'] as int?,
                    minimumAdaptivePwm:
                        attributes['minimumAdaptivePwm'] as int?,
                    maximumAdaptivePwm:
                        attributes['maximumAdaptivePwm'] as int?,
                  );

                  // 更新

                  _updateAttribute(updatedAttributes);
                  Navigator.pop(context);
                }),
          );
        },
      ),
    );
  }

  void _updateAttribute(WyDeviceAttribute updatedAttributes) {
    final bloc = context.read<WyDeviceBloc>();
    print(updatedAttributes.toJson());
    bloc.add(UpdateDeviceAttributes(updatedAttributes));
  }

  // 重启
  void _restart(context) {
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return TDAlertDialog(
          title: '重启',
          content: '确定要重启吗？',
          rightBtnAction: () {
            // TODO:重启
            Navigator.pop(buildContext);
          },
        );
      },
    );
  }

// 关机
  void _close(context) {
    final textEditing = TextEditingController(text: '20');
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return TDInputDialog(
          textEditingController: textEditing,
          title: '输入关机时间（分）',
          hintText: '输入关机时间',
          rightBtn: TDDialogButtonOptions(
            title: '确定',
            action: () async {
              final request= {
                "method": "shutdown",
                "params": {
                  "duration": int.parse(textEditing.text) * 60
                }
              };
              var response = await HttpUtil.instance.client.post('/rpc', data: request);
              print(textEditing.text);
              Navigator.pop(buildContext);
            },
          ),
        );
      },
    );
  }
}
