import 'package:client/bloc/measurement_bloc.dart';
import 'package:client/bloc/targets_blocs.dart';
import 'package:client/bloc/web_socket_bloc.dart';
import 'package:client/bloc/wy_device_blocs.dart';
import 'package:client/model/wy_device.dart';
import 'package:client/repository/target_repository.dart';
import 'package:client/services/measurement_websocket_service.dart';
import 'package:client/view/device_home/app_bar.dart';
import 'package:client/view/device_home/device_home.dart';
import 'package:client/view/device_home/measurement_tab.dart';
import 'package:client/view/device_home/setting_tab.dart';
import 'package:client/view/device_home/target_manage.dart';
import 'package:client/view/device_home/view_image.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DeviceOverview extends StatefulWidget {
  final String initialWebSocketUrl;
  final String deviceId;
  final String ip;

  const DeviceOverview({
    super.key,
    required this.initialWebSocketUrl,
    required this.deviceId,
    required this.ip,
  });

  @override
  State<DeviceOverview> createState() => _DeviceOverviewState();
}

class _DeviceOverviewState extends State<DeviceOverview>
    with SingleTickerProviderStateMixin {
  late List<TDTab> _tabs;
  late TabController _tabController;
  late WyDeviceStatus deviceStatus;
  late final TargetBloc _targetBloc;
  late final WyDeviceBloc _wyDeviceBloc;
  late final WebSocketBloc _webSocketBloc;
  late final MeasurementChartBloc _measurementChartBloc;
  MeasurementWebSocketService? _measurementWebSocketService;
  @override
  void initState() {
    super.initState();
    _tabs = [const TDTab(text: '实时数据'), const TDTab(text: '设备设置')];
    _tabController = TabController(length: _tabs.length, vsync: this);
    deviceStatus = WyDeviceStatus.idle;
    _wyDeviceBloc = WyDeviceBloc(
      deviceId: widget.deviceId,
      ipAddress: widget.ip,
    );
    TargetRepository repo = TargetRepositoryImpl(wyDeviceBloc:_wyDeviceBloc);
    _targetBloc = TargetBloc(repo);
    _webSocketBloc = WebSocketBloc(
      widget.initialWebSocketUrl,
      targetBloc: _targetBloc,
      deviceBloc: _wyDeviceBloc,
    );

    // 初始化MeasurementChartBloc
    _measurementChartBloc = MeasurementChartBloc(maxDataPoints: 1000);

    // 初始化服务连接WebSocketBloc和MeasurementChartBloc
    _measurementWebSocketService = MeasurementWebSocketService(
      webSocketBloc: _webSocketBloc,
      measurementChartBloc: _measurementChartBloc,
    );

    _webSocketBloc.add(ConnectWebSocket(widget.initialWebSocketUrl));

    _targetBloc.add(const TargetsLoading());
    // 标靶列表
    _targetBloc.add(const RequestTargets());
    // 设备信息
    _wyDeviceBloc.add(LoadDeviceAttributes());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _targetBloc.close();
    _wyDeviceBloc.close();
    _webSocketBloc.close();
    _measurementChartBloc.close();
    _measurementWebSocketService?.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        BlocProvider<TargetBloc>.value(value: _targetBloc),
        BlocProvider<WyDeviceBloc>.value(value: _wyDeviceBloc),
        BlocProvider<WebSocketBloc>.value(value: _webSocketBloc),
        BlocProvider<MeasurementChartBloc>.value(value: _measurementChartBloc),
        Provider<MeasurementWebSocketService>.value(
            value: _measurementWebSocketService!),
      ],
      child: BlocBuilder<TargetBloc, TargetState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return MultiBlocListener(
            listeners: [
              BlocListener<WebSocketBloc, WebSocketState>(
                listener: (context, state) {
                  if (state is WebSocketConnected) {
                    TDMessage.showMessage(
                      context: context,
                      content: 'WebSocket连接成功',
                      theme: MessageTheme.success,
                      duration: 3000,
                    );
                  } else if (state is WebSocketError) {
                    TDMessage.showMessage(
                      context: context,
                      content: 'WebSocket错误',
                      theme: MessageTheme.error,
                      duration: 3000,
                    );
                  } else if (state is WebSocketDisconnected) {
                    TDMessage.showMessage(
                      context: context,
                      content: 'WebSocket已断开连接',
                      theme: MessageTheme.warning,
                      duration: 3000,
                    );
                  }
                },
              ),
              BlocListener<WyDeviceBloc, WyDeviceState>(
                listenWhen: (previous, current) => previous != current,
                listener: (context, state) {
                  if (state.errorMessage != null) {
                    context
                        .read<WyDeviceBloc>()
                        .add(WyDeviceBlocClearErrorMessage());
                  }
                },
              ),
              BlocListener<TargetBloc, TargetState>(
                listenWhen: (previous, current) => previous != current,
                listener: (context, state) {
                  if (state.errorMessage != null) {
                    context
                        .read<TargetBloc>()
                        .add(TargetBlocClearErrorMessage());
                  }
                },
              ),
            ],
            child: Scaffold(
                backgroundColor: Colors.white,
                appBar: const HomeAppBar(),
                body: Column(
                  children: [
                    TDTabBar(
                      tabs: _tabs,
                      controller: _tabController,
                      showIndicator: true,
                      backgroundColor: Colors.white,
                      isScrollable: false,
                    ),
                    Expanded(
                      child: TDTabBarView(
                        controller: _tabController,
                        children: [
                          MeasurementTab(
                              toPath: (String name) => _toPath(context, name)),
                          SettingTab(
                              toPath: (String name) => _toPath(context, name))
                        ],
                      ),
                    )
                  ],
                )),
          );
        },
      ),
    );
  }

  void _toPath(context, String name) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (buildContext) {
        Widget page;
        switch (name) {
          case 'home':
            page = DeviceHome(context: context);
          case 'targetManage':
            page = TargetManage(context: context);
          case 'viewImage':
            page = ViewImage(context: context);
          default:
            page = Container();
        }
        return MultiProvider(
          providers: [
            BlocProvider<TargetBloc>.value(value: _targetBloc),
            BlocProvider<WyDeviceBloc>.value(value: _wyDeviceBloc),
            BlocProvider<WebSocketBloc>.value(value: _webSocketBloc),
          ],
          child: page,
        );
      }),
    );
  }
}
