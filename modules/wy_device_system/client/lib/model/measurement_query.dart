
class MeasurementQuery{
  final MeasurementType chartType;
  final DateTime startTime;
  final DateTime endTime;

  final String? selectedTargetId;
  final bool isRealtime;
  const MeasurementQuery({
    required this.chartType,
    required this.startTime,
    required this.endTime,
    this.selectedTargetId,
    this.isRealtime = false,
  });

  @override
  String toString() {
    return 'Query{chartType: $chartType, startTime: $startTime, endTime: $endTime, selectedTargetId: $selectedTargetId, isRealtime: $isRealtime}';
  }




  // 使用工厂方法创建默认查询
  factory MeasurementQuery.defaultQuery() {
    return MeasurementQuery(
      chartType: MeasurementType.displacement,
      startTime: DateTime.now().subtract(const Duration(hours: 1)),
      endTime: DateTime.now(),
    );
  }

}


enum MeasurementType {
  displacement,
  envStatus,
  imu,
  acceleration,
  multiSensor,
}

extension MeasurementTypeExtension on MeasurementType {
  static MeasurementType fromString(String value) {
    return MeasurementType.values.firstWhere(
          (type) => type.toString().split('.').last == value,
      orElse: () => MeasurementType.displacement,
    );
  }

  String get displayName {
    switch (this) {
      case MeasurementType.displacement:
        return '位移图表';
      case MeasurementType.envStatus:
        return '环境状态';
      case MeasurementType.imu:
        return 'IMU';
      case MeasurementType.acceleration:
        return '加速度';
      case MeasurementType.multiSensor:
        return '多传感器';
    }
  }
}


