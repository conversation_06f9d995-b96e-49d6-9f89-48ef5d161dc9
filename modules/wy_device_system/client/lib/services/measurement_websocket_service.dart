import 'dart:async';

import 'package:flutter/foundation.dart';
import '../bloc/measurement_bloc.dart';
import '../bloc/web_socket_bloc.dart';
import '../model/measurement.dart';

class MeasurementWebSocketService {
  final WebSocketBloc webSocketBloc;
  final MeasurementChartBloc measurementChartBloc;
  StreamSubscription? _subscription;

  MeasurementWebSocketService({
    required this.webSocketBloc,
    required this.measurementChartBloc,
  }) {
    _init();
  }

  void _init() {
    _subscription = webSocketBloc.stream.listen((state) {
      if (state is WebSocketMessageReceived) {
        _handleWebSocketMessage(state.message);
      }
    });
  }

  void dispose() {
    _subscription?.cancel();
  }

  void _handleWebSocketMessage(Map<String, dynamic> message) {

    if(message.containsKey('Telemetry')) {
      final msg = message['Telemetry'];
      if (msg.containsKey('Displacements')) {
        _handleDisplacementMsg(msg);
      }
      if (msg.containsKey('DeviceEnvStatus')) {
        _handleEnvironmentalData(msg);
      }

    }


  }

  void _handleDisplacementMsg(Map<String, dynamic> message) {
    measurementChartBloc.add(DisplacementUpdateData(message));
  }

    void _handleDisplacementData(Map<String, dynamic> message) {

      final data = message['Displacements'];
      try {
        if (data is List) {
          debugPrint('Received displacement data list with ${data.length} items');
          for (var item in data) {
            if (item is Map) {
              final typedItem = Map<String, dynamic>.from(item);
              // 检查是否包含displacement对象
              if (typedItem.containsKey('displacement') && typedItem['displacement'] is Map) {
                var displacement = Map<String, dynamic>.from(typedItem['displacement']);

                // 合并targetId和时间戳到displacement对象中
                displacement['targetId'] = typedItem['targetId'];

                // 处理时间戳
                if (!displacement.containsKey('timestamp') && typedItem.containsKey('ts')) {
                  try {
                    int timestamp;
                    if (typedItem['ts'] is int) {
                      timestamp = typedItem['ts'];
                    } else if (typedItem['ts'] is String) {
                      timestamp = int.parse(typedItem['ts']);
                    } else {
                      timestamp = int.parse(typedItem['ts'].toString());
                    }
                    displacement['timestamp'] = DateTime.fromMillisecondsSinceEpoch(timestamp).toIso8601String();
                  } catch (e) {
                    debugPrint('时间戳解析错误: ${typedItem['ts']} - $e');
                    // 提供一个默认时间戳
                    displacement['timestamp'] = DateTime.now().toIso8601String();
                  }
                } else if (displacement.containsKey('timestamp')) {
                  // 确保timestamp是字符串格式
                  try {
                    if (displacement['timestamp'] is int) {
                      // 如果是int，转换为ISO8601字符串
                      displacement['timestamp'] = DateTime.fromMillisecondsSinceEpoch(
                          displacement['timestamp']
                      ).toIso8601String();
                    } else if (displacement['timestamp'] is! String) {
                      // 如果不是字符串也不是int，尝试转换
                      int timestamp = int.parse(displacement['timestamp'].toString());
                      displacement['timestamp'] = DateTime.fromMillisecondsSinceEpoch(timestamp).toIso8601String();
                    }
                  } catch (e) {
                    debugPrint('时间戳格式转换错误: ${displacement['timestamp']} - $e');
                    displacement['timestamp'] = DateTime.now().toIso8601String();
                  }
                }

                _processDisplacementDataItem(displacement);
              } else {
                // 如果没有displacement对象，直接处理当前项
                _processDisplacementDataItem(typedItem);
              }
            }
          }
        } else if (data is Map) {
          final typedData = Map<String, dynamic>.from(data);
          _processDisplacementDataItem(typedData);
        }
      } catch (e) {
        debugPrint('解析位移数据错误: $e');
      }

  }

  void _processDisplacementDataItem(Map<String, dynamic> item) {
    final targetId = item['targetId'];

    // X轴位移
    if (item.containsKey('x')) {
      measurementChartBloc.add(AddMeasurementData(Measurement(
        timestamp: DateTime.parse(item['timestamp']),
        value: (item['x'] as num).toDouble(),
        type: 'x',
        targetId: targetId,
      )));
    }

    // Y轴位移
    if (item.containsKey('y')) {
      measurementChartBloc.add(AddMeasurementData(Measurement(
        timestamp: DateTime.parse(item['timestamp']),
        value: (item['y'] as num).toDouble(),
        type: 'y',
        targetId: targetId,
      )));
    }

    // Z轴位移
    if (item.containsKey('z')) {
      measurementChartBloc.add(AddMeasurementData(Measurement(
        timestamp: DateTime.parse(item['timestamp']),
        value: (item['z'] as num).toDouble(),
        type: 'z',
        targetId: targetId,
      )));
    }

    if (item.containsKey('sigmaX')) {
      measurementChartBloc.add(AddMeasurementData(Measurement(
        timestamp: DateTime.parse(item['timestamp']),
        value: (item['sigmaX'] as num).toDouble(),
        type: 'sigmaX',
        targetId: targetId,
      )));
    }
    if (item.containsKey('sigmaY')) {
      measurementChartBloc.add(AddMeasurementData(Measurement(
        timestamp: DateTime.parse(item['timestamp']),
        value: (item['sigmaY'] as num).toDouble(),
        type: 'sigmaY',
        targetId: targetId,
      )));
    }


  }

  void _handleEnvironmentalData(Map<String, dynamic> message) {

      final data = message['DeviceEnvStatus'];
      try {
        // 处理时间戳
        DateTime timestamp;
        if (data.containsKey('ts')) {
          try {
            int ts;
            if (data['ts'] is int) {
              ts = data['ts'];
            } else {
              ts = int.parse(data['ts'].toString());
            }
            timestamp = DateTime.fromMillisecondsSinceEpoch(ts);
          } catch (e) {
            debugPrint('时间戳解析错误: ${data['ts']} - $e');
            timestamp = DateTime.now();
          }
        } else {
          timestamp = DateTime.now();
        }

        // 处理温度数据
        if (data.containsKey('wyUpTemp') && data['wyUpTemp'] != null) {
          measurementChartBloc.add(AddMeasurementData(Measurement(
            timestamp: timestamp,
            value: double.parse(data['wyUpTemp'].toString()),
            type: 'temperature',
          )));
        }

        // 处理湿度数据
        if (data.containsKey('wyUpHumidity') && data['wyUpHumidity'] != null) {
          measurementChartBloc.add(AddMeasurementData(Measurement(
            timestamp: timestamp,
            value: double.parse(data['wyUpHumidity'].toString()),
            type: 'humidity',
          )));
        }

        // 处理压力数据
        if (data.containsKey('wyUpPressure') && data['wyUpPressure'] != null) {
          measurementChartBloc.add(AddMeasurementData(Measurement(
            timestamp: timestamp,
            value: double.parse(data['wyUpPressure'].toString()),
            type: 'pressure',
          )));
        }
      } catch (e) {
        debugPrint('解析环境数据错误: $e');
      }

  }
  void _handleIMUData(Map<String, dynamic> message) {
    if (message.containsKey('data')) {
      final data = message['data'];
      final targetId = data['targetId'];

      try {
        // 加速度数据处理
        if (data.containsKey('accel_x')) {
          measurementChartBloc.add(AddMeasurementData(Measurement(
            timestamp: DateTime.parse(data['timestamp']),
            value: (data['accel_x'] as num).toDouble(),
            type: 'accel_x',
            targetId: targetId,
          )));
        }

        if (data.containsKey('accel_y')) {
          measurementChartBloc.add(AddMeasurementData(Measurement(
            timestamp: DateTime.parse(data['timestamp']),
            value: (data['accel_y'] as num).toDouble(),
            type: 'accel_y',
            targetId: targetId,
          )));
        }

        if (data.containsKey('accel_z')) {
          measurementChartBloc.add(AddMeasurementData(Measurement(
            timestamp: DateTime.parse(data['timestamp']),
            value: (data['accel_z'] as num).toDouble(),
            type: 'accel_z',
            targetId: targetId,
          )));
        }
      } catch (e) {
        debugPrint('解析IMU数据错误: $e');
      }
    }
  }
}
