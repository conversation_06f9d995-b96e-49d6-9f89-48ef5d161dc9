import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
Future<String> getDatabasePath(String dbName) async {
  late final Directory directory;

  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    // 桌面平台
    directory = await getApplicationSupportDirectory();
  } else {
    // 移动平台
    directory = await getApplicationDocumentsDirectory();
  }
  // 创建数据库专用目录
  final dbDirectory = Directory(path.join(directory.path, 'databases'));
  if (!await dbDirectory.exists()) {
    await dbDirectory.create(recursive: true);
  }
  return path.join(dbDirectory.path, dbName);
}