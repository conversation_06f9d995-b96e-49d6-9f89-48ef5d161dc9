{"widgetCollection": "ウィジェットコレクション", "paintCollection": "描画コレクション", "knowledgeCollection": "知識集", "treasureTools": "ツールボックス", "collectCollection": "コレクション集", "essentialCollection": "要点集", "homeAccount": "アプリ情報", "homeAccountTabInfo": "アプリについて", "homeAccountTabMe": "本王に連絡", "homeAccountSupport": "プロジェクトをサポート", "searchWidget": "ウィジェットを検索", "stateless": "ステートレス", "stateful": "ステートフル", "single": "シングルレンダリング", "multi": "マルチレンダリング", "sliver": "スライバー", "proxy": "プロキシ", "other": "その他", "homeTabWidget": "ウィジェット", "homeTabPaint": "描画", "homeTabKnowledge": "知識", "homeTabTools": "ツール", "homeTabMine": "マイ", "dataManagement": "データ管理", "userCollection": "マイコレクション", "aboutApplications": "アプリについて", "contactThisKing": "本王に連絡", "appSettings": "アプリ設定", "darkMode": "ダークモード", "themeColorSetting": "テーマカラー設定", "fontSetting": "フォント設定", "settingLanguageText": "多言語", "codeHighlightStyle": "コードハイライトスタイル", "versionInformation": "バージョン情報", "displayPerformanceFloatingLayer": "パフォーマンスフローティングレイヤーを表示", "showFloatingTools": "フローティングツールを表示", "followSystem": "システムに従う", "afterOpeningWillFollowTheSystemToOpenOrCloseDarkMode": "有効にすると、システムに従ってダークモードをオンまたはオフにします", "manualSetting": "手動設定", "lightMode": "ライトモード", "settingLanguage": "言語設定", "appDetails": "アプリ詳細", "checkUpdate": "新しいバージョンを確認", "downloadNewVersion": "新しいバージョンをダウンロード", "downloadingNewVersion": "新しいバージョンをダウンロード中...", "currentIsNew": "現在のアプリは最新バージョンです！", "checkDatabaseNewVersion": "データベースの新しいバージョンを確認", "viewThisProjectGithubRepository": "《このプロジェクトのGithubリポジトリを表示》", "favorite": "お気に入り", "enterComponentName": "コンポーネント名を入力", "containerComponents": "コンテナコンポーネント", "componentTavern": "コンポーネント酒場", "cherishedComponents": "大切なコンポーネント", "textImageCollection": "テキスト画像集", "layoutCollection": "レイアウト集", "eventCollection": "イベント集", "animationCollection": "アニメーション集", "slidingCollection": "スライド集", "decorationCollection": "装飾集", "assemblyCollection": "アセンブリ集", "functionCollection": "機能集", "popupCollection": "ポップアップ集", "themeCollection": "テーマ集", "derivativeCollection": "派生集", "hardToCategorize": "分類が難しい", "basicDrawing": "基本描画", "basicDrawingDesc": "基本的な図形描画のケースを収録しています。これらのケースは、描画を始めたばかりのプログラマーにとって非常に役立ちます。これらのケースを通じて、点、線、矩形、円、円弧、テキスト、画像などの基本的な図形の描画方法を学び、Canvas、Paint、Pathなどの描画の核心オブジェクトの使用方法を理解できます。", "animationGesture": "アニメーションとジェスチャー", "animationGestureDesc": "アニメーションとジェスチャーの描画ケースを収録しています。これらのケースは、描画をより操作可能にします。これらのケースを通じて、スライド、回転、拡大縮小、移動などの効果を学び、描画が静的な表現だけでなくなることを理解できます。", "particleDrawing": "粒子描画", "particleDrawingDesc": "粒子関連の描画ケースを収録しています。これらのケースは、描画のトップレベルの操作です。これらのケースを通じて、粒子を使用して驚くべき視覚効果を描画する方法を学び、粒子時計、粒子爆発、粒子背景などの効果を実現し、描画に無限の可能性を与えます。", "interestingDrawing": "面白い描画", "interestingDrawingDesc": "いくつかの面白い描画ケースを収録しています。ここで一緒に描画の楽しさ、プログラミングの楽しさ、そして知恵の楽しさを体験しましょう。", "artGallery": "アートギャラリー", "artGalleryDesc": "殿堂級の描画ケースを収録しています。これらのケースは、描画の頂点作品であり、それらは実用性がなく、いかなるニーズのためでもありません。それらは存在するために存在し、人間の知恵と表現の媒体であり、芸術と呼ばれます。", "drawingOfImages": "このサンプルでは、画像を描画する方法を紹介します。画像をロードし、指定された領域に画像リソースを描画します。上層に45度傾いたグリッド線を描画し、線の描画を練習します。", "digitalDisplayTube": "このサンプルでは、LEDデジタル表示管を描画する方法を紹介し、パスPathの使用、変換、組み合わせ、およびコンポーネントのカプセル化の知識を練習します。非常に良い描画ケースです。", "pathDrawing": "このサンプルでは、簡単なパスの描画とキャンバスの回転を紹介し、アニメーションと組み合わせて風車を回転させます。これは、描画とアニメーションを組み合わせた非常に簡潔なケースです。", "gridCoordinateSystem": "このサンプルでは、線パスとテキストを使用してグリッド座標系を描画し、描画オブジェクトをカプセル化して再利用しやすくします。座標系は描画時に参考を提供し、入門に最適です。", "polarCoordinateSystemOfFaces": "このサンプルでは、平面の極座標系を使用して描画し、関数方程式に基づいて極座標を収集して描画する方法を紹介します。", "drawFunctionCurvesForPathPairs": "このサンプルでは、パスを使用して関数曲線を描画し、関数曲線上の少数の点をベジェ曲線でフィッティングする方法を紹介します。", "drawRegularPolygons": "このサンプルでは、円内で点を収集し、正多角形を描画する方法を紹介します。描画とパス形成の練習に最適なケースです。\n特殊操作：+、- で辺の数を変更", "randomNumberProcessing": "このサンプルでは、矩形の描画と乱数処理を紹介します。点の集合を使用して矩形の位置情報を決定し、それを描画します。データの制御能力を練習できます。", "clockDrawing": "このサンプルでは、時計の描画を通じて、Flutterでの回転目盛りの描画テクニックを練習し、アニメーションで時計の針を回転させます。", "drawSprings": "このサンプルでは、バネを描画し、垂直にドラッグして伸縮し、手を離すと復元アニメーションを行う方法を紹介します。非常に良い総合的な小ケースです。特殊操作:上下にドラッグしてバネを伸縮", "theApplicationOfAnglesInDrawing": "このサンプルでは、ある点を中心に回転運動を行う方法を紹介します。これにより、2点間の角度を描画に適用する方法を学びます。\n特殊操作：クリックして実行", "usingShadersAndFilters": "このサンプルでは、描画でシェーダーとフィルターを使用し、アニメーションで数値を変化させて回転する光の効果を実現する方法を紹介します。", "pathDrawingFunctionCurve": "このサンプルでは、パスを使用して関数曲線を描画し、パス測定を使用してアニメーションを行う方法を紹介します。", "thePathOfBingDwenDwen": "このサンプルでは、2022年北京冬季オリンピックのマスコットであるビンドゥンドゥンのパスを描画し、パス測定を使用してアニメーションを行います。\n特殊操作：クリックして実行", "drawCubicBesselCurve": "このサンプルでは、3次ベジェ曲線を描画し、タッチポイントを使用してある点がアクティブかどうかを判断し、それに応じて点の位置を制御してドラッグ制御効果を実現する方法を紹介します。\n特殊操作：クリックで点を描画、ダブルクリックでクリア", "theEffectOfAnimationCurve": "このサンプルでは、アニメーションカーブの効果を直感的に確認し、アニメーションに対する理解を深めます。\n特殊操作：クリックして実行", "randomParticlesAndBoundaryBouncing": "このサンプルでは、ランダムな粒子を作成し、境界でのバウンスロジックを処理する方法を紹介します。粒子運動を学ぶのに非常に良い入門ケースです。特殊操作:クリックで停止/実行", "particleCollision": "このサンプルでは、個々の粒子の衝突検出を行い、複数の粒子に分裂させる方法を紹介します。非常に面白いケースです。\n特殊操作：クリックでリセット", "particle": "このサンプルでは、画像を粒子として表現し、粒子にアニメーションを適用して爆発効果を実現する方法を紹介します。\n特殊操作：クリックして実行", "rectangleAndRandomNumbers": "このサンプルでは、矩形の描画と乱数処理を紹介します。点の集合を使用して矩形の位置情報を決定し、それを描画します。データの制御能力を練習できます。\n特殊操作：クリックしてランダム生成", "bingDwenDwen": "このサンプルでは、2022年北京冬季オリンピックのマスコットであるビンドゥンドゥンの形を描画し、パス描画やグラデーションなどの知識を学びます。", "pufengInjectionTest": "このサンプルでは、蒲豊の針投げテストのプロセスを実装し、確率を使用して円周率を推定します。描画の小技やデータの論理処理を学ぶことができます。", "ticTacToe": "このサンプルでは、三目並べの描画と論理検証を通じて、ジェスチャー、描画、アニメーション、検証などの重要なスキルを組み合わせます。非常に良い練習ケースです。\n特殊操作：ダブルクリックでリセット", "tiledLines": "このサンプルは、generativeartistry.comのtiled-linesに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "joyDivision": "このサンプルは、generativeartistry.comのjoy-divisionに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "cubicDisarray": "このサンプルは、generativeartistry.comのcubic-disarrayに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "triangularMesh": "このサンプルは、generativeartistry.comのtriangular-meshに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "unDeuxTrois": "このサンプルは、generativeartistry.comのun-deux-troisに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "circlePacking": "このサンプルは、generativeartistry.comのcircle-packingに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "hypnoticSquares": "このサンプルは、generativeartistry.comのhypnotic-squaresに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "pietMondrian": "このサンプルは、generativeartistry.comのpiet-mondrianに由来し、xrr2016がFlutterで実装しました。リポジトリアドレス:flutter-generative-artistry", "downloadCompressedPackage": "使用方法:\n1. iconfont.cnでアイコンを選び、プロジェクトに追加し、圧縮パッケージをダウンロードします。\n2. Flutterプロジェクトのアドレスを選択し、リソースと生成物ファイルの場所を設定します。\n3. コード生成ボタンをクリックすると、関連するコードが生成されます。", "qAIssues": "要点集録のQAデータは、rUnitのpointタグが付いたissuesに収録されています。データを提供する必要がある場合は、issuesで質問してください。", "tips": "注:", "visualSorting": "可視化ソート", "visual": "可視ソート", "insertion": "挿入ソート", "bubble": "バブルソート", "cocktail": "カクテルソート（双方向バブルソート）", "comb": "コームソート", "pigeonHole": "鳩の巣ソート", "shell": "シェルソート", "selection": "選択ソート", "gnome": "ノームソート", "cycle": "サイクルソート", "heap": "ヒープソート", "quick": "クイックソート", "merge": "マージソート", "sortingAlgorithmConfiguration": "ソートアルゴリズム設定", "dataCount": "データ数（個数）", "timeInterval": "時間間隔（マイクロ秒）", "randomSeed": "ランダムシード", "codeGeneration": "コード生成", "generateCode": "コードを生成", "artifactLocation": "生成物の場所", "codeClassLocation": "コードクラスの保存場所", "resourceDirectory": "リソースディレクトリ", "iconfontResourceLocation": "iconfontリソースの保存場所", "projectPath": "プロジェクトパス", "inputProjectAddress": "プロジェクトアドレスを選択または入力してください", "iconfontCompressedPackagePath": "Iconfont圧縮パッケージのパス", "pleaseSelectOrInputIconfontCompressedPackagePath": "iconfontのダウンロードした圧縮パッケージのパスを選択または入力してください", "stayTuned": "お楽しみに", "iconFont": "IconFont", "dataClass": "データクラス", "stateManagement": "状態管理", "jsonParsing": "Json解析", "clickHereToJump": "ここをクリックしてジャンプ", "knowledgeTabToly": "ジェット文庫", "knowledgeTabAlgo": "アルゴリズム演繹", "knowledgeTabLayout": "レイアウト宝庫", "knowledgeTabPoint": "要点宝庫", "knowledgeConstruction": "建設中", "knowledgeToJuejin": "掘金へ", "srcPath": "ソースアドレス", "widgetsInn": "ウィジェット酒場", "likedWidgets": "大切なウィジェット", "relatedComponents": "関連コンポーネント", "backupFavoritesCollectionData": "お気に入りコレクションデータのバックアップ", "syncFavoritesCollectionData": "お気に入りコレクションデータの同期", "favoritesCollectionDataReset": "お気に入りコレクションデータのリセット", "resetSuccess": "リセット成功！", "dataSetBackupSuccess": "データセットのバックアップ成功！", "dataSetBackupFailure": "データセットのバックアップ失敗！", "dataSynchronizationCopySuccess": "データ同期コピー成功！", "dataSynchronizationCopyFailure": "データ同期コピー失敗！", "destructionRed": "破滅の赤", "rageOrange": "怒りのオレンジ", "warningYellow": "警告の黄", "camouflageGreen": "偽装の緑", "coldBlue": "冷徹な青", "infiniteBlue": "無限の藍", "mysteryPurple": "神秘の紫", "destinyBlack": "帰宿の黒", "showBackground": "背景を表示", "toly": "張風捷特烈", "dartHandbook": "Dart 手引き", "codeCopiedSuccessfully": "コードがコピーされました", "favoriteFolderManagement": "お気に入りフォルダの管理", "assembly": "コンポーネント", "draw": "描画", "knowledge": "知識", "collection": "コレクション", "my": "私の", "picture": "枚", "widgetInn": "コンポーネント酒場", "emptySearch": "データがありません、俺もどうしようもない\n(≡ _ ≡)/~┴┴", "searchSomething": "友よ、何か検索しよう…≧◔◡◔≦", "slogan": "Flutterの連携、プログラマーの連携"}