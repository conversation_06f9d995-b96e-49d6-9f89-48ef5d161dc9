{"widgetCollection": "위젯 컬렉션", "paintCollection": "그림 컬렉션", "knowledgeCollection": "지식 모음", "treasureTools": "도구 보물상자", "collectCollection": "컬렉션 모음", "essentialCollection": "핵심 모음", "homeAccount": "앱 정보", "homeAccountTabInfo": "앱 정보", "homeAccountTabMe": "저에게 연락", "homeAccountSupport": "프로젝트 지원", "searchWidget": "위젯 검색", "stateless": "무상태", "stateful": "상태 있음", "single": "단일 렌더링", "multi": "다중 렌더링", "sliver": "슬라이버", "proxy": "프록시", "other": "기타", "homeTabWidget": "위젯", "homeTabPaint": "그리기", "homeTabKnowledge": "지식", "homeTabTools": "도구", "homeTabMine": "내 정보", "dataManagement": "데이터 관리", "userCollection": "내 컬렉션", "aboutApplications": "앱 정보", "contactThisKing": "저에게 연락", "appSettings": "앱 설정", "darkMode": "다크 모드", "themeColorSetting": "테마 색상 설정", "fontSetting": "폰트 설정", "settingLanguageText": "다국어", "codeHighlightStyle": "코드 하이라이트 스타일", "versionInformation": "버전 정보", "displayPerformanceFloatingLayer": "성능 플로팅 레이어 표시", "showFloatingTools": "플로팅 도구 표시", "followSystem": "시스템 따라가기", "afterOpeningWillFollowTheSystemToOpenOrCloseDarkMode": "시스템 설정에 따라 다크 모드를 켜거나 끕니다.", "manualSetting": "수동 설정", "lightMode": "라이트 모드", "settingLanguage": "언어 설정", "appDetails": "앱 상세 정보", "checkUpdate": "새 버전 확인", "downloadNewVersion": "새 버전 다운로드", "downloadingNewVersion": "새 버전 다운로드 중...", "currentIsNew": "현재 버전이 최신입니다!", "checkDatabaseNewVersion": "데이터베이스 새 버전 확인", "viewThisProjectGithubRepository": "《이 프로젝트의 Github 저장소 보기》", "favorite": "즐겨찾기", "enterComponentName": "컴포넌트 이름 입력", "containerComponents": "컨테이너 컴포넌트", "componentTavern": "컴포넌트 선술집", "cherishedComponents": "소중한 컴포넌트", "textImageCollection": "텍스트 이미지 모음", "layoutCollection": "레이아웃 모음", "eventCollection": "이벤트 모음", "animationCollection": "애니메이션 모음", "slidingCollection": "슬라이딩 모음", "decorationCollection": "장식 모음", "assemblyCollection": "조립 모음", "functionCollection": "기능 모음", "popupCollection": "팝업 모음", "themeCollection": "테마 모음", "derivativeCollection": "파생 모음", "hardToCategorize": "분류하기 어려움", "basicDrawing": "기본 그리기", "basicDrawingDesc": "기본 도형 그리기 예제를 포함합니다. 이 예제들은 그리기를 처음 접하는 프로그래머에게 매우 친숙할 것입니다. 이 예제들을 통해 점, 선, 사각형, 원, 호, 텍스트, 이미지 등 기본 도형을 그리는 방법을 배우고, Canvas, Paint, Path 등 그리기의 핵심 객체 사용법을 익힐 수 있습니다.", "animationGesture": "애니메이션 제스처", "animationGestureDesc": "애니메이션과 제스처 그리기 예제를 포함합니다. 이 예제들은 그리기를 더욱 상호작용적으로 만듭니다. 이 예제들을 통해 슬라이드, 회전, 확대/축소, 이동 등의 효과를 배울 수 있으며, 그리기가 단순히 정적인 표현이 아니게 됩니다.", "particleDrawing": "입자 그리기", "particleDrawingDesc": "입자 관련 그리기 예제를 포함합니다. 이 예제들은 그리기의 최고 수준의 작업입니다. 이 예제들을 통해 입자를 사용하여 놀라운 시각적 효과를 그리는 방법을 배울 수 있습니다. 입자 시계, 입자 폭발, 입자 배경 등 다양한 효과를 통해 그리기의 무한한 가능성을 경험할 수 있습니다.", "interestingDrawing": "재미있는 그리기", "interestingDrawingDesc": "재미있는 그리기 예제를 포함합니다. 여기서 그리기, 프로그래밍, 지혜의 즐거움을 함께 경험해 보세요.", "artGallery": "예술 갤러리", "artGalleryDesc": "최고 수준의 그리기 예제를 포함합니다. 이 예제들은 그리기의 정점에 있는 작품들로, 실용성은 없지만 존재 자체로 의미가 있는 예술 작품입니다.", "drawingOfImages": "이 예제는 이미지를 그리는 방법을 소개합니다: 이미지를 로드하고 지정된 영역에 그립니다. 상단에 45도 각도의 그리드 선을 그려 선 그리기를 연습합니다.", "digitalDisplayTube": "이 예제는 LED 디지털 디스플레이 튜브를 그리는 방법을 소개하며, Path 사용, 변환, 조합 및 컴포넌트 캡슐화를 연습합니다. 매우 좋은 그리기 예제입니다.", "pathDrawing": "이 예제는 간단한 경로 그리기 및 캔버스 회전을 소개하며, 애니메이션을 결합하여 풍차를 회전시킵니다. 매우 간결한 그리기와 애니메이션 결합 예제입니다.", "gridCoordinateSystem": "이 예제는 선 경로와 텍스트를 사용하여 그리드 좌표계를 그리는 방법을 소개하며, 그리기 객체를 캡슐화하여 재사용하기 쉽게 만듭니다. 좌표계는 그리기 시 참조를 제공하며, 입문자에게 필수입니다.", "polarCoordinateSystemOfFaces": "이 예제는 평면의 극좌표계를 그리는 방법을 소개하며, 함수 방정식에 따라 극좌표를 수집하여 그립니다.", "drawFunctionCurvesForPathPairs": "이 예제는 경로를 사용하여 함수 곡선을 그리는 방법을 소개하며, 함수 곡선 상의 소량의 점을 베지어 곡선으로 피팅합니다.", "drawRegularPolygons": "이 예제는 원 안에서 점을 수집하여 정다각형을 그리는 방법을 소개하며, 그리기 및 경로 형성 연습에 매우 좋은 예제입니다.\n특수 조작: +, - 로 변 수정", "randomNumberProcessing": "이 예제는 사각형 그리기 및 난수 처리를 소개합니다. 점 집합을 통해 사각형 위치 정보를 결정하고 그립니다. 데이터 제어 능력을 연습할 수 있습니다.", "clockDrawing": "이 예제는 시계 그리기를 통해 Flutter에서 회전 눈금 그리기 기술을 연습하고, 애니메이션을 통해 시계 바늘이 회전하도록 합니다.", "drawSprings": "이 예제는 스프링을 그리는 방법을 소개하며, 수직 드래그로 스프링을 늘이거나 줄이고, 놓을 때 복원 애니메이션을 수행합니다. 매우 좋은 종합 예제입니다. 특수 조작: 위아래 드래그로 스프링 늘이기/줄이기", "theApplicationOfAnglesInDrawing": "이 예제는 특정 점을 중심으로 회전 운동을 수행하는 방법을 소개합니다. 이를 통해 두 점 사이의 각도가 그리기에서 어떻게 적용되는지 배울 수 있습니다.\n특수 조작: 클릭하여 실행", "usingShadersAndFilters": "이 예제는 그리기에서 셰이더와 필터를 사용하는 방법을 소개하며, 애니메이션을 통해 수치를 변경하여 회전하는 빛의 효과를 만듭니다.", "pathDrawingFunctionCurve": "이 예제는 경로를 사용하여 함수 곡선을 그리는 방법을 소개하며, 경로 측정을 사용하여 애니메이션을 만듭니다.", "thePathOfBingDwenDwen": "이 예제는 2022년 베이징 동계 올림픽 마스코트 빙둔둔의 경로를 그리며, 경로 측정을 사용하여 애니메이션을 만듭니다.\n특수 조작: 클릭하여 실행", "drawCubicBesselCurve": "이 예제는 3차 베지어 곡선을 그리는 방법을 소개하며, 접점을 통해 특정 점이 활성화되었는지 판단하여 점의 위치를 제어합니다.\n특수 조작: 클릭하여 점 그리기, 더블 클릭하여 지우기", "theEffectOfAnimationCurve": "이 예제는 애니메이션 곡선의 효과를 직관적으로 보여줍니다. 이를 통해 애니메이션에 대한 이해를 높일 수 있습니다.\n특수 조작: 클릭하여 실행", "randomParticlesAndBoundaryBouncing": "이 예제는 무작위 입자를 생성하고 경계에서 튕기는 로직을 처리하는 방법을 소개합니다. 입자 운동을 배우기에 매우 좋은 입문 예제입니다. 특수 조작: 클릭하여 정지/실행", "particleCollision": "이 예제는 개별 입자에 대한 충돌 감지를 수행하고, 여러 입자로 분열시키는 방법을 소개합니다. 매우 재미있는 예제입니다.\n특수 조작: 클릭하여 재설정", "particle": "이 예제는 이미지를 입자로 표현하고, 입자에 애니메이션을 적용하여 폭발 효과를 만드는 방법을 소개합니다.\n특수 조작: 클릭하여 실행", "rectangleAndRandomNumbers": "이 예제는 사각형 그리기 및 난수 처리를 소개합니다. 점 집합을 통해 사각형 위치 정보를 결정하고 그립니다. 데이터 제어 능력을 연습할 수 있습니다.\n특수 조작: 클릭하여 무작위 생성", "bingDwenDwen": "이 예제는 2022년 베이징 동계 올림픽 마스코트 빙둔둔의 형태를 그리는 방법을 소개하며, 경로 그리기 및 그라데이션 색상 사용법을 배울 수 있습니다.", "pufengInjectionTest": "이 예제는 푸펑 투시험의 테스트 과정을 구현하며, 확률을 통해 원주율을 추정합니다. 그리기 기술 및 데이터 논리 처리 방법을 배울 수 있습니다.", "ticTacToe": "이 예제는 틱택토 그리기 및 논리 검증을 통해 제스처, 그리기, 애니메이션, 검증 등의 중요한 기술을 종합적으로 연습할 수 있는 매우 좋은 예제입니다.\n특수 조작: 더블 클릭하여 재설정", "tiledLines": "이 예제는 generativeartistry.com의 tiled-lines에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "joyDivision": "이 예제는 generativeartistry.com의 joy-division에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "cubicDisarray": "이 예제는 generativeartistry.com의 cubic-disarray에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "triangularMesh": "이 예제는 generativeartistry.com의 triangular-mesh에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "unDeuxTrois": "이 예제는 generativeartistry.com의 un-deux-trois에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "circlePacking": "이 예제는 generativeartistry.com의 circle-packing에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "hypnoticSquares": "이 예제는 generativeartistry.com의 hypnotic-squares에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "pietMondrian": "이 예제는 generativeartistry.com의 piet-mondrian에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry", "downloadCompressedPackage": "사용 방법:\n1. iconfont.cn에서 아이콘을 선택하고 프로젝트에 추가한 후 압축 파일을 다운로드합니다.\n2. Flutter 프로젝트 경로를 선택하고 리소스 및 산출물 파일 위치를 설정합니다.\n3. 코드 생성 버튼을 클릭하여 관련 코드를 생성합니다.", "qAIssues": "핵심 모음의 QA 데이터는 rUnit의 point 태그가 있는 issues에 포함되어 있습니다. 데이터를 제공하려면 issues에서 질문하세요.", "tips": "참고:", "visualSorting": "시각적 정렬", "visual": "시각적 정렬", "insertion": "삽입 정렬", "bubble": "버블 정렬", "cocktail": "칵테일 정렬(양방향 버블 정렬)", "comb": "빗질 정렬", "pigeonHole": "비둘기집 정렬", "shell": "셸 정렬", "selection": "선택 정렬", "gnome": "노움 정렬", "cycle": "순환 정렬", "heap": "힙 정렬", "quick": "퀵 정렬", "merge": "병합 정렬", "sortingAlgorithmConfiguration": "정렬 알고리즘 설정", "dataCount": "데이터 수(개수)", "timeInterval": "시간 간격(마이크로초)", "randomSeed": "무작위 시드", "codeGeneration": "코드 생성", "generateCode": "코드 생성", "artifactLocation": "산출물 위치", "codeClassLocation": "코드 클래스 위치", "resourceDirectory": "리소스 디렉토리", "iconfontResourceLocation": "iconfont 리소스 위치", "projectPath": "프로젝트 경로", "inputProjectAddress": "프로젝트 주소를 선택하거나 입력하세요", "iconfontCompressedPackagePath": "Iconfont 압축 파일 경로", "pleaseSelectOrInputIconfontCompressedPackagePath": "iconfont 다운로드 압축 파일 경로를 선택하거나 입력하세요", "stayTuned": "기대해 주세요", "iconFont": "IconFont", "dataClass": "데이터 클래스", "stateManagement": "상태 관리", "jsonParsing": "Json 파싱", "clickHereToJump": "여기를 클릭하여 이동", "knowledgeTabToly": "제트 문고", "knowledgeTabAlgo": "알고리즘 연습", "knowledgeTabLayout": "레이아웃 보물", "knowledgeTabPoint": "핵심 보물", "knowledgeConstruction": "구축 중", "knowledgeToJuejin": "Juejin으로 이동", "srcPath": "소스 경로", "widgetsInn": "위젯 선술집", "likedWidgets": "소중한 위젯", "relatedComponents": "관련 컴포넌트", "backupFavoritesCollectionData": "즐겨찾기 컬렉션 데이터 백업", "syncFavoritesCollectionData": "즐겨찾기 컬렉션 데이터 동기화", "favoritesCollectionDataReset": "즐겨찾기 컬렉션 데이터 재설정", "resetSuccess": "재설정 성공!", "dataSetBackupSuccess": "데이터 세트 백업 성공!", "dataSetBackupFailure": "데이터 세트 백업 실패!", "dataSynchronizationCopySuccess": "데이터 동기화 복사 성공!", "dataSynchronizationCopyFailure": "데이터 동기화 복사 실패!", "destructionRed": "파괴의 빨강", "rageOrange": "분노의 주황", "warningYellow": "경고의 노랑", "camouflageGreen": "위장의 초록", "coldBlue": "냉담의 파랑", "infiniteBlue": "무한의 남색", "mysteryPurple": "신비의 보라", "destinyBlack": "운명의 검정", "showBackground": "배경 표시", "toly": "장풍제특렬", "dartHandbook": "Dart 핸드북", "codeCopiedSuccessfully": "코드 복사 성공", "favoriteFolderManagement": "즐겨찾기 폴더 관리", "assembly": "컴포넌트", "draw": "그리기", "knowledge": "지식", "collection": "컬렉션", "my": "내 정보", "picture": "그림", "widgetInn": "위젯 선술집", "emptySearch": "데이터가 없습니다, 형님도 방법이 없어요\n(≡ _ ≡)/~┴┴", "searchSomething": "형님, 뭘 검색하시겠어요...≧◔◡◔≦", "slogan": "Flutter의 연합, 프로그래머의 연합"}