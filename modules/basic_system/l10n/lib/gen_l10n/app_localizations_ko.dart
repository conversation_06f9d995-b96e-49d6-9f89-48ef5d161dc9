import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get widgetCollection => '위젯 컬렉션';

  @override
  String get paintCollection => '그림 컬렉션';

  @override
  String get knowledgeCollection => '지식 모음';

  @override
  String get treasureTools => '도구 보물상자';

  @override
  String get collectCollection => '컬렉션 모음';

  @override
  String get essentialCollection => '핵심 모음';

  @override
  String get homeAccount => '앱 정보';

  @override
  String get homeAccountTabInfo => '앱 정보';

  @override
  String get homeAccountTabMe => '저에게 연락';

  @override
  String get homeAccountSupport => '프로젝트 지원';

  @override
  String get searchWidget => '위젯 검색';

  @override
  String get stateless => '무상태';

  @override
  String get stateful => '상태 있음';

  @override
  String get single => '단일 렌더링';

  @override
  String get multi => '다중 렌더링';

  @override
  String get sliver => '슬라이버';

  @override
  String get proxy => '프록시';

  @override
  String get other => '기타';

  @override
  String get homeTabWidget => '위젯';

  @override
  String get homeTabPaint => '그리기';

  @override
  String get homeTabKnowledge => '지식';

  @override
  String get homeTabTools => '도구';

  @override
  String get homeTabMine => '내 정보';

  @override
  String get dataManagement => '데이터 관리';

  @override
  String get userCollection => '내 컬렉션';

  @override
  String get aboutApplications => '앱 정보';

  @override
  String get contactThisKing => '저에게 연락';

  @override
  String get appSettings => '앱 설정';

  @override
  String get darkMode => '다크 모드';

  @override
  String get themeColorSetting => '테마 색상 설정';

  @override
  String get fontSetting => '폰트 설정';

  @override
  String get settingLanguageText => '다국어';

  @override
  String get codeHighlightStyle => '코드 하이라이트 스타일';

  @override
  String get versionInformation => '버전 정보';

  @override
  String get displayPerformanceFloatingLayer => '성능 플로팅 레이어 표시';

  @override
  String get showFloatingTools => '플로팅 도구 표시';

  @override
  String get followSystem => '시스템 따라가기';

  @override
  String get afterOpeningWillFollowTheSystemToOpenOrCloseDarkMode => '시스템 설정에 따라 다크 모드를 켜거나 끕니다.';

  @override
  String get manualSetting => '수동 설정';

  @override
  String get lightMode => '라이트 모드';

  @override
  String get settingLanguage => '언어 설정';

  @override
  String get appDetails => '앱 상세 정보';

  @override
  String get checkUpdate => '새 버전 확인';

  @override
  String get downloadNewVersion => '새 버전 다운로드';

  @override
  String get downloadingNewVersion => '새 버전 다운로드 중...';

  @override
  String get currentIsNew => '현재 버전이 최신입니다!';

  @override
  String get checkDatabaseNewVersion => '데이터베이스 새 버전 확인';

  @override
  String get viewThisProjectGithubRepository => '《이 프로젝트의 Github 저장소 보기》';

  @override
  String get favorite => '즐겨찾기';

  @override
  String get enterComponentName => '컴포넌트 이름 입력';

  @override
  String get containerComponents => '컨테이너 컴포넌트';

  @override
  String get componentTavern => '컴포넌트 선술집';

  @override
  String get cherishedComponents => '소중한 컴포넌트';

  @override
  String get textImageCollection => '텍스트 이미지 모음';

  @override
  String get layoutCollection => '레이아웃 모음';

  @override
  String get eventCollection => '이벤트 모음';

  @override
  String get animationCollection => '애니메이션 모음';

  @override
  String get slidingCollection => '슬라이딩 모음';

  @override
  String get decorationCollection => '장식 모음';

  @override
  String get assemblyCollection => '조립 모음';

  @override
  String get functionCollection => '기능 모음';

  @override
  String get popupCollection => '팝업 모음';

  @override
  String get themeCollection => '테마 모음';

  @override
  String get derivativeCollection => '파생 모음';

  @override
  String get hardToCategorize => '분류하기 어려움';

  @override
  String get basicDrawing => '기본 그리기';

  @override
  String get basicDrawingDesc => '기본 도형 그리기 예제를 포함합니다. 이 예제들은 그리기를 처음 접하는 프로그래머에게 매우 친숙할 것입니다. 이 예제들을 통해 점, 선, 사각형, 원, 호, 텍스트, 이미지 등 기본 도형을 그리는 방법을 배우고, Canvas, Paint, Path 등 그리기의 핵심 객체 사용법을 익힐 수 있습니다.';

  @override
  String get animationGesture => '애니메이션 제스처';

  @override
  String get animationGestureDesc => '애니메이션과 제스처 그리기 예제를 포함합니다. 이 예제들은 그리기를 더욱 상호작용적으로 만듭니다. 이 예제들을 통해 슬라이드, 회전, 확대/축소, 이동 등의 효과를 배울 수 있으며, 그리기가 단순히 정적인 표현이 아니게 됩니다.';

  @override
  String get particleDrawing => '입자 그리기';

  @override
  String get particleDrawingDesc => '입자 관련 그리기 예제를 포함합니다. 이 예제들은 그리기의 최고 수준의 작업입니다. 이 예제들을 통해 입자를 사용하여 놀라운 시각적 효과를 그리는 방법을 배울 수 있습니다. 입자 시계, 입자 폭발, 입자 배경 등 다양한 효과를 통해 그리기의 무한한 가능성을 경험할 수 있습니다.';

  @override
  String get interestingDrawing => '재미있는 그리기';

  @override
  String get interestingDrawingDesc => '재미있는 그리기 예제를 포함합니다. 여기서 그리기, 프로그래밍, 지혜의 즐거움을 함께 경험해 보세요.';

  @override
  String get artGallery => '예술 갤러리';

  @override
  String get artGalleryDesc => '최고 수준의 그리기 예제를 포함합니다. 이 예제들은 그리기의 정점에 있는 작품들로, 실용성은 없지만 존재 자체로 의미가 있는 예술 작품입니다.';

  @override
  String get drawingOfImages => '이 예제는 이미지를 그리는 방법을 소개합니다: 이미지를 로드하고 지정된 영역에 그립니다. 상단에 45도 각도의 그리드 선을 그려 선 그리기를 연습합니다.';

  @override
  String get digitalDisplayTube => '이 예제는 LED 디지털 디스플레이 튜브를 그리는 방법을 소개하며, Path 사용, 변환, 조합 및 컴포넌트 캡슐화를 연습합니다. 매우 좋은 그리기 예제입니다.';

  @override
  String get pathDrawing => '이 예제는 간단한 경로 그리기 및 캔버스 회전을 소개하며, 애니메이션을 결합하여 풍차를 회전시킵니다. 매우 간결한 그리기와 애니메이션 결합 예제입니다.';

  @override
  String get gridCoordinateSystem => '이 예제는 선 경로와 텍스트를 사용하여 그리드 좌표계를 그리는 방법을 소개하며, 그리기 객체를 캡슐화하여 재사용하기 쉽게 만듭니다. 좌표계는 그리기 시 참조를 제공하며, 입문자에게 필수입니다.';

  @override
  String get polarCoordinateSystemOfFaces => '이 예제는 평면의 극좌표계를 그리는 방법을 소개하며, 함수 방정식에 따라 극좌표를 수집하여 그립니다.';

  @override
  String get drawFunctionCurvesForPathPairs => '이 예제는 경로를 사용하여 함수 곡선을 그리는 방법을 소개하며, 함수 곡선 상의 소량의 점을 베지어 곡선으로 피팅합니다.';

  @override
  String get drawRegularPolygons => '이 예제는 원 안에서 점을 수집하여 정다각형을 그리는 방법을 소개하며, 그리기 및 경로 형성 연습에 매우 좋은 예제입니다.\n특수 조작: +, - 로 변 수정';

  @override
  String get randomNumberProcessing => '이 예제는 사각형 그리기 및 난수 처리를 소개합니다. 점 집합을 통해 사각형 위치 정보를 결정하고 그립니다. 데이터 제어 능력을 연습할 수 있습니다.';

  @override
  String get clockDrawing => '이 예제는 시계 그리기를 통해 Flutter에서 회전 눈금 그리기 기술을 연습하고, 애니메이션을 통해 시계 바늘이 회전하도록 합니다.';

  @override
  String get drawSprings => '이 예제는 스프링을 그리는 방법을 소개하며, 수직 드래그로 스프링을 늘이거나 줄이고, 놓을 때 복원 애니메이션을 수행합니다. 매우 좋은 종합 예제입니다. 특수 조작: 위아래 드래그로 스프링 늘이기/줄이기';

  @override
  String get theApplicationOfAnglesInDrawing => '이 예제는 특정 점을 중심으로 회전 운동을 수행하는 방법을 소개합니다. 이를 통해 두 점 사이의 각도가 그리기에서 어떻게 적용되는지 배울 수 있습니다.\n특수 조작: 클릭하여 실행';

  @override
  String get usingShadersAndFilters => '이 예제는 그리기에서 셰이더와 필터를 사용하는 방법을 소개하며, 애니메이션을 통해 수치를 변경하여 회전하는 빛의 효과를 만듭니다.';

  @override
  String get pathDrawingFunctionCurve => '이 예제는 경로를 사용하여 함수 곡선을 그리는 방법을 소개하며, 경로 측정을 사용하여 애니메이션을 만듭니다.';

  @override
  String get thePathOfBingDwenDwen => '이 예제는 2022년 베이징 동계 올림픽 마스코트 빙둔둔의 경로를 그리며, 경로 측정을 사용하여 애니메이션을 만듭니다.\n특수 조작: 클릭하여 실행';

  @override
  String get drawCubicBesselCurve => '이 예제는 3차 베지어 곡선을 그리는 방법을 소개하며, 접점을 통해 특정 점이 활성화되었는지 판단하여 점의 위치를 제어합니다.\n특수 조작: 클릭하여 점 그리기, 더블 클릭하여 지우기';

  @override
  String get theEffectOfAnimationCurve => '이 예제는 애니메이션 곡선의 효과를 직관적으로 보여줍니다. 이를 통해 애니메이션에 대한 이해를 높일 수 있습니다.\n특수 조작: 클릭하여 실행';

  @override
  String get randomParticlesAndBoundaryBouncing => '이 예제는 무작위 입자를 생성하고 경계에서 튕기는 로직을 처리하는 방법을 소개합니다. 입자 운동을 배우기에 매우 좋은 입문 예제입니다. 특수 조작: 클릭하여 정지/실행';

  @override
  String get particleCollision => '이 예제는 개별 입자에 대한 충돌 감지를 수행하고, 여러 입자로 분열시키는 방법을 소개합니다. 매우 재미있는 예제입니다.\n특수 조작: 클릭하여 재설정';

  @override
  String get particle => '이 예제는 이미지를 입자로 표현하고, 입자에 애니메이션을 적용하여 폭발 효과를 만드는 방법을 소개합니다.\n특수 조작: 클릭하여 실행';

  @override
  String get rectangleAndRandomNumbers => '이 예제는 사각형 그리기 및 난수 처리를 소개합니다. 점 집합을 통해 사각형 위치 정보를 결정하고 그립니다. 데이터 제어 능력을 연습할 수 있습니다.\n특수 조작: 클릭하여 무작위 생성';

  @override
  String get bingDwenDwen => '이 예제는 2022년 베이징 동계 올림픽 마스코트 빙둔둔의 형태를 그리는 방법을 소개하며, 경로 그리기 및 그라데이션 색상 사용법을 배울 수 있습니다.';

  @override
  String get pufengInjectionTest => '이 예제는 푸펑 투시험의 테스트 과정을 구현하며, 확률을 통해 원주율을 추정합니다. 그리기 기술 및 데이터 논리 처리 방법을 배울 수 있습니다.';

  @override
  String get ticTacToe => '이 예제는 틱택토 그리기 및 논리 검증을 통해 제스처, 그리기, 애니메이션, 검증 등의 중요한 기술을 종합적으로 연습할 수 있는 매우 좋은 예제입니다.\n특수 조작: 더블 클릭하여 재설정';

  @override
  String get tiledLines => '이 예제는 generativeartistry.com의 tiled-lines에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get joyDivision => '이 예제는 generativeartistry.com의 joy-division에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get cubicDisarray => '이 예제는 generativeartistry.com의 cubic-disarray에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get triangularMesh => '이 예제는 generativeartistry.com의 triangular-mesh에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get unDeuxTrois => '이 예제는 generativeartistry.com의 un-deux-trois에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get circlePacking => '이 예제는 generativeartistry.com의 circle-packing에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get hypnoticSquares => '이 예제는 generativeartistry.com의 hypnotic-squares에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get pietMondrian => '이 예제는 generativeartistry.com의 piet-mondrian에서 유래되었으며, xrr2016이 Flutter로 구현했습니다. 저장소 주소: flutter-generative-artistry';

  @override
  String get downloadCompressedPackage => '사용 방법:\n1. iconfont.cn에서 아이콘을 선택하고 프로젝트에 추가한 후 압축 파일을 다운로드합니다.\n2. Flutter 프로젝트 경로를 선택하고 리소스 및 산출물 파일 위치를 설정합니다.\n3. 코드 생성 버튼을 클릭하여 관련 코드를 생성합니다.';

  @override
  String get qAIssues => '핵심 모음의 QA 데이터는 rUnit의 point 태그가 있는 issues에 포함되어 있습니다. 데이터를 제공하려면 issues에서 질문하세요.';

  @override
  String get tips => '참고:';

  @override
  String get visualSorting => '시각적 정렬';

  @override
  String get visual => '시각적 정렬';

  @override
  String get insertion => '삽입 정렬';

  @override
  String get bubble => '버블 정렬';

  @override
  String get cocktail => '칵테일 정렬(양방향 버블 정렬)';

  @override
  String get comb => '빗질 정렬';

  @override
  String get pigeonHole => '비둘기집 정렬';

  @override
  String get shell => '셸 정렬';

  @override
  String get selection => '선택 정렬';

  @override
  String get gnome => '노움 정렬';

  @override
  String get cycle => '순환 정렬';

  @override
  String get heap => '힙 정렬';

  @override
  String get quick => '퀵 정렬';

  @override
  String get merge => '병합 정렬';

  @override
  String get sortingAlgorithmConfiguration => '정렬 알고리즘 설정';

  @override
  String get dataCount => '데이터 수(개수)';

  @override
  String get timeInterval => '시간 간격(마이크로초)';

  @override
  String get randomSeed => '무작위 시드';

  @override
  String get codeGeneration => '코드 생성';

  @override
  String get generateCode => '코드 생성';

  @override
  String get artifactLocation => '산출물 위치';

  @override
  String get codeClassLocation => '코드 클래스 위치';

  @override
  String get resourceDirectory => '리소스 디렉토리';

  @override
  String get iconfontResourceLocation => 'iconfont 리소스 위치';

  @override
  String get projectPath => '프로젝트 경로';

  @override
  String get inputProjectAddress => '프로젝트 주소를 선택하거나 입력하세요';

  @override
  String get iconfontCompressedPackagePath => 'Iconfont 압축 파일 경로';

  @override
  String get pleaseSelectOrInputIconfontCompressedPackagePath => 'iconfont 다운로드 압축 파일 경로를 선택하거나 입력하세요';

  @override
  String get stayTuned => '기대해 주세요';

  @override
  String get iconFont => 'IconFont';

  @override
  String get dataClass => '데이터 클래스';

  @override
  String get stateManagement => '상태 관리';

  @override
  String get jsonParsing => 'Json 파싱';

  @override
  String get clickHereToJump => '여기를 클릭하여 이동';

  @override
  String get knowledgeTabToly => '제트 문고';

  @override
  String get knowledgeTabAlgo => '알고리즘 연습';

  @override
  String get knowledgeTabLayout => '레이아웃 보물';

  @override
  String get knowledgeTabPoint => '핵심 보물';

  @override
  String get knowledgeConstruction => '구축 중';

  @override
  String get knowledgeToJuejin => 'Juejin으로 이동';

  @override
  String get srcPath => '소스 경로';

  @override
  String get widgetsInn => '위젯 선술집';

  @override
  String get likedWidgets => '소중한 위젯';

  @override
  String get relatedComponents => '관련 컴포넌트';

  @override
  String get backupFavoritesCollectionData => '즐겨찾기 컬렉션 데이터 백업';

  @override
  String get syncFavoritesCollectionData => '즐겨찾기 컬렉션 데이터 동기화';

  @override
  String get favoritesCollectionDataReset => '즐겨찾기 컬렉션 데이터 재설정';

  @override
  String get resetSuccess => '재설정 성공!';

  @override
  String get dataSetBackupSuccess => '데이터 세트 백업 성공!';

  @override
  String get dataSetBackupFailure => '데이터 세트 백업 실패!';

  @override
  String get dataSynchronizationCopySuccess => '데이터 동기화 복사 성공!';

  @override
  String get dataSynchronizationCopyFailure => '데이터 동기화 복사 실패!';

  @override
  String get destructionRed => '파괴의 빨강';

  @override
  String get rageOrange => '분노의 주황';

  @override
  String get warningYellow => '경고의 노랑';

  @override
  String get camouflageGreen => '위장의 초록';

  @override
  String get coldBlue => '냉담의 파랑';

  @override
  String get infiniteBlue => '무한의 남색';

  @override
  String get mysteryPurple => '신비의 보라';

  @override
  String get destinyBlack => '운명의 검정';

  @override
  String get showBackground => '배경 표시';

  @override
  String get toly => '장풍제특렬';

  @override
  String get dartHandbook => 'Dart 핸드북';

  @override
  String get codeCopiedSuccessfully => '코드 복사 성공';

  @override
  String get favoriteFolderManagement => '즐겨찾기 폴더 관리';

  @override
  String get assembly => '컴포넌트';

  @override
  String get draw => '그리기';

  @override
  String get knowledge => '지식';

  @override
  String get collection => '컬렉션';

  @override
  String get my => '내 정보';

  @override
  String get picture => '그림';

  @override
  String get widgetInn => '위젯 선술집';

  @override
  String get emptySearch => '데이터가 없습니다, 형님도 방법이 없어요\n(≡ _ ≡)/~┴┴';

  @override
  String get searchSomething => '형님, 뭘 검색하시겠어요...≧◔◡◔≦';

  @override
  String get slogan => 'Flutter의 연합, 프로그래머의 연합';
}
