import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get widgetCollection => 'Сборник компонентов';

  @override
  String get paintCollection => 'Сборник рисования';

  @override
  String get knowledgeCollection => 'Сборник знаний';

  @override
  String get treasureTools => 'Сундучок инструментов';

  @override
  String get collectCollection => 'Сборник избранного';

  @override
  String get essentialCollection => 'Сборник ключевых моментов';

  @override
  String get homeAccount => 'Информация о приложении';

  @override
  String get homeAccountTabInfo => 'О приложении';

  @override
  String get homeAccountTabMe => 'Связаться с этим королём';

  @override
  String get homeAccountSupport => 'Проекты поддержки';

  @override
  String get searchWidget => 'Поиск компонентов';

  @override
  String get stateless => 'Без состояния';

  @override
  String get stateful => 'С состоянием';

  @override
  String get single => 'Один рендеринг';

  @override
  String get multi => 'Многократный рендеринг';

  @override
  String get sliver => 'Скользящая панель';

  @override
  String get proxy => 'Прокси';

  @override
  String get other => 'Другие';

  @override
  String get homeTabWidget => 'Компоненты';

  @override
  String get homeTabPaint => 'Рисование';

  @override
  String get homeTabKnowledge => 'Знания';

  @override
  String get homeTabTools => 'Инструменты';

  @override
  String get homeTabMine => 'Мой профиль';

  @override
  String get dataManagement => 'Управление данными';

  @override
  String get userCollection => 'Мои избранные';

  @override
  String get aboutApplications => 'О приложении';

  @override
  String get contactThisKing => 'Связаться с этим королём';

  @override
  String get appSettings => 'Настройки приложения';

  @override
  String get darkMode => 'Тёмный режим';

  @override
  String get themeColorSetting => 'Настройки темы';

  @override
  String get fontSetting => 'Настройки шрифта';

  @override
  String get settingLanguageText => 'Многоязычность';

  @override
  String get codeHighlightStyle => 'Стиль подсветки кода';

  @override
  String get versionInformation => 'Информация о версии';

  @override
  String get displayPerformanceFloatingLayer => 'Показать плавающий слой производительности';

  @override
  String get showFloatingTools => 'Показать плавающие инструменты';

  @override
  String get followSystem => 'Следовать за системой';

  @override
  String get afterOpeningWillFollowTheSystemToOpenOrCloseDarkMode => 'После включения будет следовать за системой для включения или отключения тёмного режима';

  @override
  String get manualSetting => 'Ручные настройки';

  @override
  String get lightMode => 'Светлый режим';

  @override
  String get settingLanguage => 'Настройки языка';

  @override
  String get appDetails => 'Детали приложения';

  @override
  String get checkUpdate => 'Проверить обновление';

  @override
  String get downloadNewVersion => 'Скачать новое обновление';

  @override
  String get downloadingNewVersion => 'Скачивание нового обновления...';

  @override
  String get currentIsNew => 'Текущая версия приложения уже самая новая!';

  @override
  String get checkDatabaseNewVersion => 'Проверить обновление базы данных';

  @override
  String get viewThisProjectGithubRepository => 'Посмотреть репозиторий проекта на Github';

  @override
  String get favorite => 'Добавлено в избранное';

  @override
  String get enterComponentName => 'Введите название компонента';

  @override
  String get containerComponents => 'Контейнерные компоненты';

  @override
  String get componentTavern => 'Таверна компонентов';

  @override
  String get cherishedComponents => 'Ценные компоненты';

  @override
  String get textImageCollection => 'Сборник изображений и текста';

  @override
  String get layoutCollection => 'Сборник макетов';

  @override
  String get eventCollection => 'Сборник событий';

  @override
  String get animationCollection => 'Сборник анимаций';

  @override
  String get slidingCollection => 'Сборник слайдов';

  @override
  String get decorationCollection => 'Сборник декораций';

  @override
  String get assemblyCollection => 'Сборник сборки';

  @override
  String get functionCollection => 'Сборник функций';

  @override
  String get popupCollection => 'Сборник всплывающих окон';

  @override
  String get themeCollection => 'Сборник тем';

  @override
  String get derivativeCollection => 'Сборник производных';

  @override
  String get hardToCategorize => 'Трудно классифицировать';

  @override
  String get basicDrawing => 'Основы рисования';

  @override
  String get basicDrawingDesc => 'Включает несколько примеров базового рисования, которые будут полезны для начинающих программистов. Эти примеры научат рисованию базовых фигур, таких как точки, линии, прямоугольники, круги и текст.';

  @override
  String get animationGesture => 'Анимация жестов';

  @override
  String get animationGestureDesc => 'Содержит примеры рисования анимаций и жестов, которые сделают рисование более интерактивным. Эти примеры научат использовать анимации и жесты, такие как скольжение, вращение, масштабирование и перемещение.';

  @override
  String get particleDrawing => 'Частицы в рисовании';

  @override
  String get particleDrawingDesc => 'Включает примеры рисования с использованием частиц. Эти примеры покажут, как использовать частицы для создания зрелищных эффектов, таких как взрывы или анимации с частицами.';

  @override
  String get interestingDrawing => 'Интересные рисунки';

  @override
  String get interestingDrawingDesc => 'Примеры интересных и забавных рисунков, где мы можем наслаждаться процессом рисования и программирования.';

  @override
  String get artGallery => 'Галерея искусства';

  @override
  String get artGalleryDesc => 'Содержит примеры высокого искусства, которые не предназначены для практических целей, а являются выражением человеческого интеллекта и творчества.';

  @override
  String get drawingOfImages => 'Пример рисования изображений: загрузка изображения и его отрисовка в определенной области. На верхнем слое добавлены линии сетки для практики рисования линий.';

  @override
  String get digitalDisplayTube => 'Пример рисования цифровых трубок с использованием пути Path и анимации.';

  @override
  String get pathDrawing => 'Пример рисования простых путей с анимацией.';

  @override
  String get gridCoordinateSystem => 'Пример рисования сетки координат с линиями и текстом для новичков.';

  @override
  String get polarCoordinateSystemOfFaces => 'Пример рисования полярной координатной системы с использованием функций.';

  @override
  String get drawFunctionCurvesForPathPairs => 'Пример рисования функциональных кривых с использованием пути и Беезье.';

  @override
  String get drawRegularPolygons => 'Пример рисования правильных многоугольников внутри круга.';

  @override
  String get randomNumberProcessing => 'Пример рисования прямоугольников с обработкой случайных чисел.';

  @override
  String get clockDrawing => 'Пример рисования часов с анимацией.';

  @override
  String get drawSprings => 'Пример рисования пружины с анимацией сжатия и растяжения.';

  @override
  String get theApplicationOfAnglesInDrawing => 'Пример рисования с использованием углов для вращения.';

  @override
  String get usingShadersAndFilters => 'Пример использования шейдеров и фильтров для создания анимации.';

  @override
  String get pathDrawingFunctionCurve => 'Пример рисования кривых с помощью пути и измерения анимации.';

  @override
  String get thePathOfBingDwenDwen => 'Пример рисования пути маскота Олимпиады Пекин-2022 с использованием анимации пути.';

  @override
  String get drawCubicBesselCurve => 'Пример рисования кривой кубического Безье с возможностью перетаскивания точек.';

  @override
  String get theEffectOfAnimationCurve => 'Пример демонстрации эффекта анимационной кривой для углубленного понимания анимаций.';

  @override
  String get randomParticlesAndBoundaryBouncing => 'Пример создания случайных частиц с отскоком от границ.';

  @override
  String get particleCollision => 'Пример столкновений частиц с анимацией их распада.';

  @override
  String get particle => 'Пример взрыва изображений с использованием частиц.';

  @override
  String get rectangleAndRandomNumbers => 'Пример рисования прямоугольников с использованием случайных чисел.';

  @override
  String get bingDwenDwen => 'Пример рисования формы маскота Олимпиады Пекин-2022.';

  @override
  String get pufengInjectionTest => 'Пример теста для вычисления числа Пи с использованием вероятности.';

  @override
  String get ticTacToe => 'Пример игры в крестики-нолики с логикой и анимацией.';

  @override
  String get tiledLines => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get joyDivision => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get cubicDisarray => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get triangularMesh => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get unDeuxTrois => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get circlePacking => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get hypnoticSquares => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get pietMondrian => 'Пример, основанный на генеративном искусстве, реализованный с помощью Flutter.';

  @override
  String get downloadCompressedPackage => 'Инструкция по использованию:\n1. Выберите и скачайте иконки на iconfont.cn.\n2. Настройте путь к проекту Flutter.\n3. Нажмите кнопку для генерации кода.';

  @override
  String get qAIssues => 'Данные QA из сборника важных моментов rUnit будут добавлены в issues с меткой point.';

  @override
  String get tips => 'Совет:';

  @override
  String get visualSorting => 'Визуальная сортировка';

  @override
  String get visual => 'Визуальная сортировка';

  @override
  String get insertion => 'Сортировка вставками';

  @override
  String get bubble => 'Пузырьковая сортировка';

  @override
  String get cocktail => 'Коктейльная сортировка';

  @override
  String get comb => 'Сортировка расческой';

  @override
  String get pigeonHole => 'Сортировка по принципу дырок для голубей';

  @override
  String get shell => 'Сортировка Шелла';

  @override
  String get selection => 'Сортировка выбором';

  @override
  String get gnome => 'Сортировка гномом';

  @override
  String get cycle => 'Циклическая сортировка';

  @override
  String get heap => 'Сортировка кучей';

  @override
  String get quick => 'Быстрая сортировка';

  @override
  String get merge => 'Сортировка слиянием';

  @override
  String get sortingAlgorithmConfiguration => 'Настройки сортировки';

  @override
  String get dataCount => 'Количество данных';

  @override
  String get timeInterval => 'Интервал времени';

  @override
  String get randomSeed => 'Случайное зерно';

  @override
  String get codeGeneration => 'Генерация кода';

  @override
  String get generateCode => 'Сгенерировать код';

  @override
  String get artifactLocation => 'Местоположение артефакта';

  @override
  String get codeClassLocation => 'Местоположение класса кода';

  @override
  String get resourceDirectory => 'Каталог ресурсов';

  @override
  String get iconfontResourceLocation => 'Местоположение ресурсов iconfont';

  @override
  String get projectPath => 'Путь проекта';

  @override
  String get inputProjectAddress => 'Выберите или введите путь к проекту';

  @override
  String get iconfontCompressedPackagePath => 'Путь к сжатию иконок';

  @override
  String get pleaseSelectOrInputIconfontCompressedPackagePath => 'Пожалуйста, выберите или введите путь к сжатому пакету iconfont';

  @override
  String get stayTuned => 'Ожидайте';

  @override
  String get iconFont => 'IconFont';

  @override
  String get dataClass => 'Класс данных';

  @override
  String get stateManagement => 'Управление состоянием';

  @override
  String get jsonParsing => 'Парсинг JSON';

  @override
  String get clickHereToJump => 'Нажмите здесь для перехода';

  @override
  String get knowledgeTabToly => 'Библиотека Jet';

  @override
  String get knowledgeTabAlgo => 'Алгоритмы';

  @override
  String get knowledgeTabLayout => 'Библиотека макетов';

  @override
  String get knowledgeTabPoint => 'Библиотека ключевых моментов';

  @override
  String get knowledgeConstruction => 'На стадии строительства';

  @override
  String get knowledgeToJuejin => 'Перейти на Juejin';

  @override
  String get srcPath => 'Путь к исходному коду';

  @override
  String get widgetsInn => 'Таверна компонентов';

  @override
  String get likedWidgets => 'Избранные компоненты';

  @override
  String get relatedComponents => 'Связанные компоненты';

  @override
  String get backupFavoritesCollectionData => 'Резервное копирование данных избранного';

  @override
  String get syncFavoritesCollectionData => 'Синхронизация данных избранного';

  @override
  String get favoritesCollectionDataReset => 'Сброс данных избранного';

  @override
  String get resetSuccess => 'Сброс успешен!';

  @override
  String get dataSetBackupSuccess => 'Резервное копирование данных прошло успешно!';

  @override
  String get dataSetBackupFailure => 'Ошибка резервного копирования данных!';

  @override
  String get dataSynchronizationCopySuccess => 'Синхронизация данных успешна!';

  @override
  String get dataSynchronizationCopyFailure => 'Ошибка синхронизации данных!';

  @override
  String get destructionRed => 'Красный разрушения';

  @override
  String get rageOrange => 'Оранжевый гнева';

  @override
  String get warningYellow => 'Желтый предупреждения';

  @override
  String get camouflageGreen => 'Зеленый камуфляжа';

  @override
  String get coldBlue => 'Холодный синий';

  @override
  String get infiniteBlue => 'Бесконечный синий';

  @override
  String get mysteryPurple => 'Тайный фиолетовый';

  @override
  String get destinyBlack => 'Черный судьбы';

  @override
  String get showBackground => 'Показать фон';

  @override
  String get toly => 'Толь';

  @override
  String get dartHandbook => 'Руководство Dart';

  @override
  String get codeCopiedSuccessfully => 'Код успешно скопирован';

  @override
  String get favoriteFolderManagement => 'Управление папками избранного';

  @override
  String get assembly => 'Сборка';

  @override
  String get draw => 'Рисование';

  @override
  String get knowledge => 'Знания';

  @override
  String get collection => 'Коллекция';

  @override
  String get my => 'Мои';

  @override
  String get picture => 'Картинка';

  @override
  String get widgetInn => 'Таверна компонентов';

  @override
  String get emptySearch => 'Данных нет, я тоже не знаю, что делать\n(≡ _ ≡)/~┴┴';

  @override
  String get searchSomething => 'Друзья, давайте что-то искать…≧◔◡◔≦';

  @override
  String get slogan => 'Связь Flutter, связь программистов';
}
