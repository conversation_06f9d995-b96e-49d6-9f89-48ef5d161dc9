{"id": 128, "name": "SimpleDialog", "localName": "シンプルダイアログ", "info": "シンプルなダイアログ構造で、ヘッダーと中央のコンポーネントを指定できます。タイトルと内容のテキストスタイルとマージン、影の深さ、形状などの属性を持っています。SimpleDialogOptionと一緒に使用されることが多いです。", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "SimpleDialogの基本使用", "desc": ["【title】 : トップコンポーネント   【Widget】", "【children】 : 子コンポーネントリスト  【List<Widget>】", "【titlePadding】 : トップパディング  【EdgeInsetsGeometry】", "【contentPadding】 : コンテンツパディング  【EdgeInsetsGeometry】", "【backgroundColor】 : 背景色  【Color】", "【elevation】 : 影の深さ  【double】", "【shape】 : 形状   【ShapeBorder】"]}]}