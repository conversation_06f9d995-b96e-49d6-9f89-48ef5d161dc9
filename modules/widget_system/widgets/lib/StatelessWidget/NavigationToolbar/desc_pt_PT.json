{"id": 214, "name": "NavigationToolbar", "localName": "Barra de Ferramentas de Navegação", "info": "Componente estrutural genérico no modo esquerdo-central-direito, pode especificar a margem esquerda do componente central e se está centralizado. O código-fonte é usado em estruturas de barras de navegação como AppBar.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do NavigationToolbar", "desc": ["【leading】: Componente esquerdo   【Widget】", "【middle】: Componente central   【Widget】", "【trailing】: Componente direito   【Widget】", "【centerMiddle】: Se o componente central está centralizado   【bool】", "【middleSpacing】: Distância do componente central à esquerda    【double】"]}]}