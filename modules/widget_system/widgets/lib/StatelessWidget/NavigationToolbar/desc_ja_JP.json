{"id": 214, "name": "NavigationToolbar", "localName": "ナビゲーションツールバー", "info": "左中右モードの汎用構造コンポーネントで、中央コンポーネントの左側のマージンと中央揃えかどうかを指定できます。ソースコードはAppBarなどのナビゲーションバー構造で使用されています。", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "NavigationToolbarの基本的な使用", "desc": ["【leading】 : 左側コンポーネント   【Widget】", "【middle】: 中央コンポーネント   【Widget】", "【trailing】: 右側コンポーネント   【Widget】", "【centerMiddle】: 中央コンポーネントを中央揃えにするかどうか   【bool】", "【middleSpacing】: 中央コンポーネントの左側からの距離    【double】"]}]}