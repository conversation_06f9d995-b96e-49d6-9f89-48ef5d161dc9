{"id": 58, "name": "TabBar", "localName": "Barra de pestañas", "info": "Barra de pestañas deslizante y clicable, generalmente utilizada en la parte inferior de AppBar, se puede usar junto con TabBarView para lograr un efecto de deslizamiento de página.", "lever": 3, "family": 1, "linkIds": [57, 59, 148], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de TabBar", "desc": ["【controller】 : Controlador   【TabController】", "【indicatorColor】 : Color del indicador   【Color del indicador】", "【indicatorWeight】 : Altura del indicador   【double】", "【indicatorPadding】 : Márgenes del indicador   【EdgeInsetsGeometry】", "【labelStyle】 : Estilo del texto de la pestaña   【TextStyle】", "【unselectedLabelStyle】 : Estilo del texto no seleccionado   【TextStyle】", "【isScrollable】 : Deslizable   【bool】", "【onTap】 : Callback al hacer clic en la pestaña   【Function(int)】", "【tabs】 : Componentes de la pestaña   【List<Widget>】"]}, {"file": "node2_noShadow.dart", "name": "Se puede lograr sin efecto de onda configurando Theme", "desc": ["Simplemente configure el color de la onda en Theme como transparente."]}]}