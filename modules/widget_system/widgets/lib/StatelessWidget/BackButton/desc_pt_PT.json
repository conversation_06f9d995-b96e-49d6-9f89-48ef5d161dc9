{"id": 31, "name": "BackButton", "localName": "Botão de Voltar", "info": "Um IconButton com função de voltar, o ícone de voltar não pode ser alterado. Comporta-se de forma diferente no iOS e no Android.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Propried<PERSON> do BackButton", "desc": ["【color】: Cor   【Color】", "【style】: Estilo do botão   【ButtonStyle?】", "【onPressed】: Evento de clique   【Function】", "Se onPressed estiver vazio, a pilha atual será encerrada"]}]}