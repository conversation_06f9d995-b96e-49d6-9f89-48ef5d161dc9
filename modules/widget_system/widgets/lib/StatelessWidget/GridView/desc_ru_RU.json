{"id": 163, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "Сетка", "info": "Содержит несколько компонентов и отображает их в виде сетки. Может быть создан с использованием count, extent, custom, builder и других методов. Имеет свойства, такие как внутренние отступы, обратное направление, контроллер прокрутки и другие.", "lever": 5, "family": 0, "linkIds": [21, 162], "nodes": [{"file": "node1_base.dart", "name": "Конструктор GridView.count", "desc": ["【children】 : С<PERSON>ис<PERSON><PERSON> дочерних компонентов   【List<Widget>】", "【crossAxisCount】 : Количество элементов в строке по главной оси  【int】", "【mainAxisSpacing】 : Расстояние между строками по главной оси  【double】", "【crossAxisSpacing】 : Расстояние между строками по поперечной оси  【double】", "【childAspectRatio】 : Соотношение длины по главной оси к длине по поперечной оси  【double】", "【crossAxisCount】 : Количество элементов в строке по главной оси  【int】"]}, {"file": "node2_direction.dart", "name": "Направление прокрутки GridView", "desc": ["【scrollDirection】 : Направление прокрутки   【Axis】", "【reverse】 : Обратное направление прокрутки   【bool】", "【shrinkWrap】 : Обертывание при отсутствии границ  【bool】"]}, {"file": "node3_extend.dart", "name": "Направление прокрутки GridView", "desc": ["【scrollDirection】 : Направление прокрутки   【Axis】", "【reverse】 : Обратное направление прокрутки   【bool】", "【shrinkWrap】 : Обертывание при отсутствии границ  【bool】"]}, {"file": "node4_builder.dart", "name": "Конструктор GridView.builder", "desc": ["【itemCount】 : Количество элементов   【int】", "【gridDelegate】 : Делегат сетки   【SliverGridDelegate】", "【itemBuilder】 : Конструктор элементов  【IndexedWidgetBuilder】"]}]}