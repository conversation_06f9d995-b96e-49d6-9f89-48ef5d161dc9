{"id": 163, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "Rasterkomponente", "info": "Enthält mehrere Komponenten und organisiert sie in einem Raster. Kann über count, extent, custom, builder usw. konstruiert werden. Hat Eigenschaften wie Innenabstand, ob um<PERSON>ke<PERSON><PERSON>, Scroll-Controller usw.", "lever": 5, "family": 0, "linkIds": [21, 162], "nodes": [{"file": "node1_base.dart", "name": "GridView.count <PERSON><PERSON>", "desc": ["【children】 : Liste der Unterkomponenten   【List<Widget>】", "【crossAxisCount】 : <PERSON><PERSON><PERSON> der Boxen pro Zeile in der Hauptachse  【int】", "【mainAxisSpacing】 : Abstand zwischen den Zeilen in der Hauptachse  【double】", "【crossAxisSpacing】 : Abstand zwischen den Zeilen in der Querachse  【double】", "【childAspectRatio】 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Hauptlänge zu Querlänge der Box  【double】", "【crossAxisCount】 : <PERSON><PERSON><PERSON> der Boxen pro Zeile in der Hauptachse  【int】"]}, {"file": "node2_direction.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": ["【scrollDirection】 : Scrollrichtung   【Axis】", "【reverse】 : Ob umgekehrt gescrollt wird   【bool】", "【shrinkWrap】 : Ob bei fehlenden Grenzen umwickelt wird  【bool】"]}, {"file": "node3_extend.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": ["【scrollDirection】 : Scrollrichtung   【Axis】", "【reverse】 : Ob umgekehrt gescrollt wird   【bool】", "【shrinkWrap】 : Ob bei fehlenden Grenzen umwickelt wird  【bool】"]}, {"file": "node4_builder.dart", "name": "GridView.builder Konstruktion", "desc": ["【itemCount】 : <PERSON><PERSON><PERSON> der Einträge   【int】", "【gridDelegate】 : Rasterdelegat   【SliverGridDelegate】", "【itemBuilder】 : Eintragskonstruktor  【IndexedWidgetBuilder】"]}]}