{"id": 126, "name": "Dialog", "localName": "<PERSON><PERSON><PERSON>", "info": "Das einfachste Dialogfeld-Panel, das eine Inhaltskomponente enthält und Eigenschaften wie Schattentiefe, Hintergrundfarbe, Form usw. angeben kann.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung des Dialogs", "desc": ["【child】: <PERSON><PERSON><PERSON><PERSON><PERSON>   【Widget】", "【elevation】: Sc<PERSON>tentiefe  【double】", "【backgroundColor】: Hintergrundfarbe  【Color】", "【shape】: Form   【ShapeBorder】"]}]}