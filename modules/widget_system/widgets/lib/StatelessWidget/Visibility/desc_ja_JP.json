{"id": 10, "name": "Visibility", "localName": "表示/非表示コンポーネント", "info": "コンポーネントの表示または非表示を制御し、非表示時のプレースホルダーコンポーネントを設定できます。類似の機能を持つコンポーネントとしてOffStageコンポーネントがあります。", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "visibleに基づいて内部の子コンポーネントの表示/非表示を制御", "desc": ["【visible】 : 表示するかどうか  【bool】", "【child】: 子コンポーネント   【Widget】", "デフォルトでは、子コンポーネントが非表示になると元の領域を失います。"]}, {"file": "node2_replacement.dart", "name": "replacementを使用して非表示時のプレースホルダーを設定", "desc": ["【replacement】 : 非表示時のプレースホルダーコンポーネント  【Widget】"]}]}