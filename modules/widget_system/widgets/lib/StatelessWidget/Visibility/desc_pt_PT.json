{"id": 10, "name": "Visibility", "localName": "Componente de Visibilidade", "info": "Controla a exibição ou ocultação de um componente, podendo definir um componente de espaço reservado após a ocultação. Um componente com funcionalidade semelhante é o OffStage.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Controla a visibilidade dos componentes internos com base no visível", "desc": ["【visible】 : Se deve ser exibido  【bool】", "【child】: <PERSON><PERSON><PERSON>   【Widget】", "<PERSON><PERSON> pad<PERSON><PERSON>, o filho perde a área original quando oculto."]}, {"file": "node2_replacement.dart", "name": "replacement pode ser usado como espaço reservado ao ocultar", "desc": ["【replacement】 : Componente de espaço reservado ao ocultar  【Widget】"]}]}