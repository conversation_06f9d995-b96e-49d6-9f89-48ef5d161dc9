{"id": 127, "name": "AlertDialog", "localName": "<PERSON><PERSON><PERSON>", "info": "Eine allgemeine Dialogstruktur, die Komponenten im Kopf-, Mittel- und Fußbereich angeben kann. Verfügt über Textstile und Ränder für Titel und Inhalt, Schattentiefe, Form und andere Attribute.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AlertDialog", "desc": ["【title】 : Oberste Komponente   【Widget】", "【content】 : Inhaltskomponente  【Widget】", "【titleTextStyle】 : Textstil für den Titel  【TextStyle】", "【contentTextStyle】 : Textstil für den Inhalt  【TextStyle】", "【titlePadding】 : Innenabstand für den Titel  【EdgeInsetsGeometry】", "【contentPadding】 : Innenabstand für den Inhalt  【EdgeInsetsGeometry】", "【actions】 : Liste der Komponenten in der unteren rechten Ecke  【List<Widget>】", "【backgroundColor】 : Hintergrundfarbe  【Color】", "【elevation】 : Sc<PERSON>tentiefe  【double】", "【shape】 : Form   【ShapeBorder】"]}]}