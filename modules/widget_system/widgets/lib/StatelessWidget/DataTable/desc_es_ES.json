{"id": 102, "name": "DataTable", "localName": "Tabla de datos", "info": "Un componente de tabla que permite realizar operaciones como hacer clic, modificar, ordenar, etc., con lógica personalizada.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de DataTable", "desc": ["【columns】 : columnas   【List<DataColumn>】", "【rows】 : filas  【List<DataRow>】"]}, {"file": "node2_operation.dart", "name": "Ordenación de DataTable", "desc": ["【sortColumnIndex】 : índice de columna   【int】", "【columnSpacing】 : espaciado de columnas   【double】", "【sortAscending】 : orden ascendente  【bool】"]}]}