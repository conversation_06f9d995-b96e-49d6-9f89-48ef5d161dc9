{"id": 141, "name": "SnackBarAction", "localName": "SnackBar-Schaltfläche", "info": "Wird im Allgemeinen nur in SnackBar verwendet, akzeptiert Klick-Ereignisse. Nach einem Klick wird die Schaltfläche deaktiviert, es können Farbe und deaktivierte Farbe angegeben werden.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SnackBarAction", "desc": ["【label】 :  Beschriftung  【String】", "【textColor】 :  Textfarbe   【Color】", "【disabledTextColor】 :  Deaktivierte Textfarbe   【Color】", "【onPressed】 :  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  【Function()】"]}]}