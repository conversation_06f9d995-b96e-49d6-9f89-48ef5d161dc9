{"id": 12, "name": "ChoiceChip", "localName": "선택 칩", "info": "Chip 컴포넌트와 유사한 스타일로, 몇 가지 선택 속성이 있습니다. 선택 시 색상, 그림자 색상 및 선택 이벤트를 지정할 수 있습니다.", "lever": 3, "family": 0, "linkIds": [11, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "ChoiceChip의 일반적인 표현은 다음과 같습니다", "desc": ["【selectedColor】: 선택 시 색상   【Color】", "【selectedShadowColor】: 선택 시 그림자 색상   【Color】", "【onSelected】: 선택 이벤트   【Fuction(bool)】", "    다른 속성은 Chip 컴포넌트와 동일하며, 오른쪽 컴포넌트는 없습니다."]}]}