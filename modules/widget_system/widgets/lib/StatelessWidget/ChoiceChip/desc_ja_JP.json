{"id": 12, "name": "ChoiceChip", "localName": "選択チップ", "info": "Chipコンポーネントと似たスタイルで、いくつかの選択属性があります。選択時の色、影の色、および選択イベントを指定できます。", "lever": 3, "family": 0, "linkIds": [11, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "ChoiceChipの通常の表現は以下の通りです", "desc": ["【selectedColor】: 選択時の色   【Color】", "【selectedShadowColor】: 選択時の影の色   【Color】", "【onSelected】: 選択イベント   【Fuction(bool)】", "    他の属性はChipコンポーネントと同じで、右側のコンポーネントはありません。"]}]}