{"id": 212, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Эквивалентно слою занавеса, который предотвращает взаимодействие пользователя с виджетами за ним. Можно определить, будет ли нажатие вызывать возврат в стек, с помощью dismissible. В исходном коде используется для компонентов, связанных с диалогами.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Введение в ModalBarrier", "desc": ["【dismissible】 : Возврат при нажатии   【bool】", "【color】 : Цвет   【Color】"]}]}