{"id": 168, "name": "Theme", "localName": "Theme", "info": "You can obtain the ThemeData object through Theme.of. You can also specify a theme to be applied to the descendant components of Theme.", "lever": 4, "family": 0, "linkIds": [65, 169, 224], "nodes": [{"file": "node1_base.dart", "name": "Text Style-ThemeData#TextTheme", "desc": ["Child components can use the theme data obtained through ThemeData.of."]}, {"file": "node2_use.dart", "name": "Usage of Theme", "desc": ["Using Theme, you can specify a wide range of properties as themes, which will be applied to all descendant components, such as specifying properties for fonts, sliders, cards, text, dividers, buttons, etc."]}]}