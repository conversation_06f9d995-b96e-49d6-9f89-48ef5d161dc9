{"id": 168, "name": "Theme", "localName": "<PERSON><PERSON>", "info": "Pode obter o objeto ThemeData através de Theme.of. Também pode especificar um tema para ser aplicado aos componentes descendentes do Theme.", "lever": 4, "family": 0, "linkIds": [65, 169, 224], "nodes": [{"file": "node1_base.dart", "name": "Estilo de texto-ThemeData#TextTheme", "desc": ["Os componentes filhos podem obter e usar os dados do tema através de ThemeData.of."]}, {"file": "node2_use.dart", "name": "Uso do Theme", "desc": ["Ao usar Theme, pode especificar uma grande variedade de propriedades como tema, que serão aplicadas a todos os componentes descendentes, como fonte, slider, cartão, texto, divisor, botão, etc."]}]}