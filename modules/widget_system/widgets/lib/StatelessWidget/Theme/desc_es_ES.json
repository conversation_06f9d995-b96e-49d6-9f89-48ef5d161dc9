{"id": 168, "name": "Theme", "localName": "<PERSON><PERSON>", "info": "Puedes obtener el objeto ThemeData a través de Theme.of. También puedes especificar un tema para aplicarlo a los componentes descendientes de Theme.", "lever": 4, "family": 0, "linkIds": [65, 169, 224], "nodes": [{"file": "node1_base.dart", "name": "Estilo de texto-ThemeData#TextTheme", "desc": ["Los componentes secundarios pueden obtener y utilizar los datos del tema a través de ThemeData.of."]}, {"file": "node2_use.dart", "name": "Uso de Theme", "desc": ["Usando <PERSON>, puedes especificar una gran cantidad de atributos como tema, que se aplicarán a todos los componentes descendientes, como la fuente, el control deslizante, la tarjeta, el texto, el divisor, el botón, etc."]}]}