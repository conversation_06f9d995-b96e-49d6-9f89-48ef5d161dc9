{"id": 361, "name": "EndDrawerButton", "localName": "Botão de gaveta direita", "info": "Um botão de ícone de gaveta direita, usando EndDrawerButtonIcon para exibir o ícone, o evento de clique padrão pode abrir a gaveta direita.", "lever": 1, "family": 0, "linkIds": [275, 276], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do EndDrawerButton", "desc": ["【onPressed】 : Evento de clique  【VoidCallback?】", "【style】: Estilo do botão   【ButtonStyle?】", "Quando onPressed estiver vazio, ao clicar, a gaveta direita será aberta."]}]}