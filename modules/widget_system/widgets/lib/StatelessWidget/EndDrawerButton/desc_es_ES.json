{"id": 361, "name": "EndDrawerButton", "localName": "Botón de cajón derecho", "info": "Un botón de icono de cajón derecho, que utiliza EndDrawerButtonIcon para mostrar el icono. El evento de clic predeterminado puede abrir el cajón derecho.", "lever": 1, "family": 0, "linkIds": [275, 276], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de EndDrawerButton", "desc": ["【onPressed】 : Evento de clic  【VoidCallback?】", "【style】: <PERSON><PERSON><PERSON> del botón   【ButtonStyle?】", "<PERSON>uando onPressed está vacío, al hacer clic se abrirá el cajón derecho."]}]}