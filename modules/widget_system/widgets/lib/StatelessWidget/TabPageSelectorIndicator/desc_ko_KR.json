{"id": 206, "name": "TabPageSelectorIndicator", "localName": "탭 페이지 선택기 표시기", "info": "테두리가 있는 원형 컴포넌트로, 크기, 색상, 테두리 색상을 지정할 수 있습니다. TabPageSelector의 일부이며 일반적으로 단독으로 사용되지 않습니다.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TabPageSelectorIndicator 기본 사용법", "desc": ["【size】: 크기   【double】", "【backgroundColor】: 배경색   【Color】", "【borderColor】: 테두리 색상    【Color】"]}]}