{"id": 206, "name": "TabPageSelectorIndicator", "localName": "In<PERSON><PERSON> d'<PERSON><PERSON><PERSON>", "info": "Un composant circulaire avec une bordure, pouvant spécifier la taille, la couleur et la couleur de la bordure. Fait partie du TabPageSelector, généralement non utilisé seul.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de TabPageSelectorIndicator", "desc": ["【size】: Taille   【double】", "【backgroundColor】: <PERSON>uleur de fond   【Color】", "【borderColor】: <PERSON><PERSON><PERSON> de la bordure    【Color】"]}]}