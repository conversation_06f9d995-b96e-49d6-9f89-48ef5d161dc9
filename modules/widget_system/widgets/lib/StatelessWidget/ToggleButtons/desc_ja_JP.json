{"id": 33, "name": "ToggleButtons", "localName": "トグルボタングループ", "info": "コンポーネントリストを受け取り、境界線、角丸、色などの属性を指定できます。特定のロジックに基づいて、複数のボタンの単一選択または複数選択の要件を実現できます。", "lever": 4, "family": 0, "linkIds": [332, 262], "nodes": [{"file": "node1_single.dart", "name": "ToggleButtons単一選択トグル", "desc": ["【children】: 子コンポーネントセット   【List<Widget>】", "【borderWidth】: 境界線の幅   【double】", "【borderRadius】: 角丸   【BorderRadius】", "【isSelected】: 選択状態セット   【List<bool>】", "【onPressed】: クリックイベント   【Function(int)】"]}, {"file": "node2_color.dart", "name": "ToggleButtonsカラープロパティ", "desc": ["【borderColor】: 境界線の色   【Color】", "【selectedBorderColor】: 選択時の境界線の色   【Color】", "【selectedColor】: 選択時のコンポーネントの色   【Color】", "【fillColor】: 選択時の塗りつぶし色   【Color】", "【splashColor】: 波紋の色   【Color】"]}, {"file": "node3_multi.dart", "name": "ToggleButtons複数選択トグル", "desc": ["状態変換のロジックを制御して、異なる効果を生み出すことができます。"]}]}