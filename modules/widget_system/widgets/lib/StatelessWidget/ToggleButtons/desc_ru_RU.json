{"id": 33, "name": "ToggleButtons", "localName": "Группа переключателей", "info": "Принимает список компонентов, можно указать свойства границы, радиуса, цвета и т.д. В зависимости от конкретной логики, можно реализовать требования для одного или нескольких кнопок.", "lever": 4, "family": 0, "linkIds": [332, 262], "nodes": [{"file": "node1_single.dart", "name": "Одиночный переключатель ToggleButtons", "desc": ["【children】: Набор дочерних компонентов   【List<Widget>】", "【borderWidth】: Ширина границы   【double】", "【borderRadius】: Радиус угла   【BorderRadius】", "【isSelected】: Набор выбранных   【List<bool>】", "【onPressed】: Событие нажатия   【Function(int)】"]}, {"file": "node2_color.dart", "name": "Цветовые свойства ToggleButtons", "desc": ["【borderColor】: Цвет границы   【Color】", "【selectedBorderColor】: Цвет выбранной границы   【Color】", "【selectedColor】: Цвет компонента при выборе   【Color】", "【fillColor】: Цвет заполнения при выборе   【Color】", "【splashColor】: Цвет водяного знака   【Color】"]}, {"file": "node3_multi.dart", "name": "Множественный переключатель ToggleButtons", "desc": ["Можно управлять логикой преобразования состояния для создания различных эффектов."]}]}