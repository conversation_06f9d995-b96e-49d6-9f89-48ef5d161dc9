{"id": 205, "name": "TabPageSelector", "localName": "Seletor de Deslizamento de Abas", "info": "Geralmente usado como um indicador em conjunto com o TabBarView, compartilhando um TabController. Pode especificar cor, tamanho e cor de seleção.", "lever": 2, "family": 0, "linkIds": [206, 59], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do TabPageSelector", "desc": ["【controller】 : Controlador   【TabController】", "【indicatorSize】: Tamanho do Indicador   【double】", "【selectedColor】: Cor de Seleção   【Color】", "【color】: Cor    【Color】"]}]}