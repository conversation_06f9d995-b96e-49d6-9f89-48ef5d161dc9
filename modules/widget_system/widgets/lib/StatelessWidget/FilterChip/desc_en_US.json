{"id": 15, "name": "FilterChip", "localName": "Filter Chip", "info": "Similar in style to the Chip component, with properties for selection and selection events. When selected, the upper layer of the left component will be masked by a ✔️.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 14, 153], "nodes": [{"file": "node1_base.dart", "name": "FilterChip can accept selection events", "desc": ["【selected】: Whether selected   【bool】", "【onSelected】: Selection event   【Function(bool)】", "【selectedColor】: Color after selection   【Color】", "【selectedShadowColor】: Shadow color after selection   【Color】,"]}]}