{"id": 11, "name": "Chip", "localName": "Composant Petite Barre", "info": "Une petite barre horizontale aux bords arrondis, pouvant contenir trois composants à gauche, au centre et à droite. Peut spécifier la couleur, la couleur de l'ombre et l'événement de clic.", "lever": 4, "family": 0, "linkIds": [12, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "La performance normale de Chip est la suivante", "desc": ["【avatar】: Composant gauche   【Widget】", "【label】: Composant central   【Widget】", "【padding】 : Marge intérieure  【EdgeInsetsGeometry】", "【labelPadding】: Mar<PERSON> du label   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "Peut définir la couleur et l'ombre", "desc": ["【backgroundColor】: <PERSON>uleur de fond   【Color】", "【shadowColor】: <PERSON><PERSON>ur de l'ombre   【Color】", "【elevation】: Profondeur de l'ombre   【double】"]}, {"file": "node3_delete.dart", "name": "Peut définir un bouton de clic à droite", "desc": ["【deleteIcon】: <PERSON>mpo<PERSON><PERSON> droit (généralement une icône)   【Widget】", "【deleteIconColor】: <PERSON>uleur du composant droit   【Color】", "【onDeleted】: Événement de clic du composant droit   【Function】"]}]}