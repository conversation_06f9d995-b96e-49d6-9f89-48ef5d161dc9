{"id": 14, "name": "InputChip", "localName": "Kombinierter Chip", "info": "Ähnlich wie die Chip-Komponente, integriert Klick-, Lösch- und Auswahlereignisse. Hinweis: Klick- und Auswahlereignisse können nicht gleichzeitig vorhanden sein.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "Kann <PERSON>- und Lösch-Ereignisse akzeptieren", "desc": ["【onPressed】: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   【Function()】", "【onDeleted】: Lösch-Ereignis   【Function()】"]}, {"file": "node2_select.dart", "name": "Kann Auswahl-Ereignisse akzeptieren", "desc": ["【selected】: Ob ausgewählt   【bool】", "【onSelected】: Auswahl-Ereignis   【Function(bool)】"]}]}