{"id": 17, "name": "CheckboxListTile", "localName": "<PERSON><PERSON> mit Kontrollkästchen", "info": "Eine allgemeine Listenstruktur, die von Flutter bereitgestellt wird, hat eine linke und mittlere Struktur, wobei sich am Ende ein CheckBox befindet. An den entsprechenden Positionen können Komponenten eingefügt werden, was eine einfache Anpassung an spezifische Einträge ermöglicht.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Darstellung der CheckBoxListTile", "desc": ["【secondary】: <PERSON><PERSON> Kompo<PERSON>e   【Widget】", "【checkColor】: Farbe des Häkchens   【Color】", "【activeColor】: <PERSON>be des Rahmens bei Auswahl   【Color】", "【title】: Obere mittlere Komponente   【Widget】", "【subtitle】: Untere mittlere Komponente   【Widget】", "【onChanged】: Auswahlereignis   【Function(bool)】"]}, {"file": "node2_select.dart", "name": "Auswahleffekt der CheckBoxListTile", "desc": ["【selected】: Ob ausgewählt   【bool】"]}, {"file": "node3_dense.dart", "name": "Dichte Eigenschaft der CheckBoxListTile", "desc": ["【dense】: Ob dicht angeordnet   【bool】"]}]}