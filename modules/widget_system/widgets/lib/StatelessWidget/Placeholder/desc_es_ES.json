{"id": 36, "name": "Placeholder", "localName": "Componente de marcador de posición", "info": "Un componente de marcador de posición con un rectángulo y una cruz, que puede especificar propiedades como color, grosor de línea, ancho y alto.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Propiedades básicas de Placeholder", "desc": ["【color】: Color   【Color】", "【strokeWidth】: <PERSON><PERSON><PERSON> de línea   【double】"]}, {"file": "node2_fallback.dart", "name": "Propiedades de fallback de Placeholder", "desc": ["Cuando no hay restricciones de ancho y alto en el área, el ancho y alto del componente de marcador de posición.", "【fallbackHeight】: Alto   【double】", "【fallbackWidth】: <PERSON><PERSON>   【double】"]}]}