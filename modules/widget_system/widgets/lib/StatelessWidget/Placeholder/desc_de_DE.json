{"id": 36, "name": "Placeholder", "localName": "Platzhalter-Komponente", "info": "Eine Platzhalter-Komponente mit einem Rechteck und einem Kreuz, bei der Farbe, Linienstärke, Breite, Höhe usw. angegeben werden können.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Eigenschaften des Platzhalters", "desc": ["【color】: Farbe   【Color】", "【strokeWidth】: Linienstärke   【double】"]}, {"file": "node2_fallback.dart", "name": "Fallback-Eigenschaften des Platzhalters", "desc": ["<PERSON><PERSON> der Bereich keine Breiten- und Höhenbeschränkungen hat, die Breite und Höhe der Platzhalter-Komponente.", "【fallbackHeight】: Höhe   【double】", "【fallbackWidth】: Breite   【double】"]}]}