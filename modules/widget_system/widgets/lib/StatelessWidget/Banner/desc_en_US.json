{"id": 5, "name": "Banner", "localName": "Badge Component", "info": "A component for displaying badges. It can accommodate one child component, allows for the selection of orientation to add badges and informational text, and can set colors.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Used to display a badge", "desc": ["【message】: The text message to display 【String】", "【location】: Position*4 【BannerLocation】", "【color】: Badge color 【Color】", "【child】: Child 【Widget】", "【textStyle】: Text style 【TextStyle】"]}]}