{"id": 154, "name": "Drawer", "localName": "Barra de deslizamento", "info": "Geralmente usado para os atributos draw e endDraw no Scaffold como barras de deslizamento esquerda e direita, pode conter um componente filho e especificar a profundidade da sombra.", "lever": 2, "family": 0, "linkIds": [64, 155], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do Drawer", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【elevation】 : Profundidade da sombra  【double】"]}]}