{"id": 203, "name": "OrientationBuilder", "localName": "Constructor de Orientación", "info": "Puede devolver una llamada para determinar si el componente padre es horizontal o vertical, y puede construir diferentes componentes secundarios en consecuencia.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de OrientationBuilder", "desc": ["【builder】: Constructor de componentes de orientación   【OrientationWidgetBuilder】"]}]}