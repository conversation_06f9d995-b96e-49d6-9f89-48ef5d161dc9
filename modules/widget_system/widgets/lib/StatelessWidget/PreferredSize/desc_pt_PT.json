{"id": 204, "name": "PreferredSize", "localName": "<PERSON><PERSON><PERSON>", "info": "Implementa a interface PreferredSizeWidget, pode acomodar um componente filho, define o tamanho preferido e não impõe quaisquer restrições ao seu componente filho.", "lever": 2, "family": 0, "linkIds": [57, 64], "nodes": [{"file": "node1_base.dart", "name": "Ajustar a altura do AppBar com PreferredSize", "desc": ["【preferredSize】 : ta<PERSON>ho   【Size】"]}, {"file": "node2_adapter.dart", "name": "Conversão e uso do PreferredSize", "desc": ["【PreferredSize converte um componente comum em PreferredSizeWidget"]}]}