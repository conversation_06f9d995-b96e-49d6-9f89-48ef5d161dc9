{"id": 204, "name": "PreferredSize", "localName": "優先サイズ", "info": "PreferredSizeWidgetインターフェースを実装しており、子コンポーネントを収容し、優先サイズを設定しますが、子コンポーネントに制約を課しません。", "lever": 2, "family": 0, "linkIds": [57, 64], "nodes": [{"file": "node1_base.dart", "name": "PreferredSizeでAppBarの高さを調整", "desc": ["【preferredSize】 : サイズ   【Size】"]}, {"file": "node2_adapter.dart", "name": "PreferredSizeの変換と使用", "desc": ["【PreferredSizeは通常のコンポーネントをPreferredSizeWidgetに変換します"]}]}