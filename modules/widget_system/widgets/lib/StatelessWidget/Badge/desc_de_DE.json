{"id": 258, "name": "Badge", "localName": "Kennzeichnungskomponente", "info": "Badge ist eine Komponente im Material-Stil, die eine Markierung über einem Kindelement hinzufügen kann. Beim Aufbau wird die Stack-Komponente für die Überlagerung verwendet.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node_1.dart", "name": "<PERSON>er<PERSON><PERSON><PERSON> von Badge-Punktmarkierungen", "desc": ["【backgroundColor】 : Hintergrundfarbe   【Color?】", "【isLabelVisible】 : Sichtbarkeit der Markierung  【bool】", "【smallSize】 : Durchmesser ohne Label   【double?】", "【child】 : Kindelement   【Widget?】"]}, {"file": "node_2.dart", "name": "Badge-Label-Markierung", "desc": ["【label】 : Titelkomponente   【Widget?】", "【textStyle】 : Titeltextstil  【TextStyle?】", "【textColor】 : Titeltextfarbe  【Color?】", "【padding】 : Titelabstand  【EdgeInsetsGeometry?】", "【largeSize】 : <PERSON><PERSON><PERSON> mit Label   【double?】,"]}, {"file": "node_3.dart", "name": "<PERSON><PERSON><PERSON><PERSON> von Badge", "desc": ["【offset】 : Versatz der Markierung  【Offset?】", "【alignment】 : Titelversatz   【AlignmentDirectional?】"]}]}