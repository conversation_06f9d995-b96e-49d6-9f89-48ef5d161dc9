{"id": 258, "name": "Badge", "localName": "Компонент метки", "info": "Badge — это компонент в стиле Material, который может добавлять маркеры поверх дочернего элемента, при построении он зависит от компонента Stack для наложения.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node_1.dart", "name": "Использование круглой метки Badge", "desc": ["【backgroundColor】 : Цвет фона   【Color?】", "【isLabelVisible】 : Видимость метки  【bool】", "【smallSize】 : Диаметр без метки   【double?】", "【child】 : Доче<PERSON>ний компонент   【Widget?】"]}, {"file": "node_2.dart", "name": "Метка Badge с текстом", "desc": ["【label】 : Компонент заголовка   【Widget?】", "【textStyle】 : Цвет заголовка  【TextStyle?】", "【textColor】 : Стиль заголовка  【Color?】", "【padding】 : Отступы заголовка  【EdgeInsetsGeometry?】", "【largeSize】 : Высота с меткой   【double?】,"]}, {"file": "node_3.dart", "name": "Смещение Badge", "desc": ["【offset】 : Смещение метки  【Offset?】", "【alignment】 : Смещение заголовка   【AlignmentDirectional?】"]}]}