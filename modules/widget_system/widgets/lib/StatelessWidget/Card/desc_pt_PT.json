{"id": 3, "name": "Card", "localName": "Componente de Cartão", "info": "Implementado com base em componentes Material, usado para transformar um único componente em um cartão. E fazê-lo ter um efeito de sombra, pode adicionar margem externa e também pode personalizar a forma do cartão.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "O Card pode transformar um componente em um cartão", "desc": ["【elevation】 : Profundidade da sombra  【double】", "【margin】: Margem externa   【double】", "【color】: Cor   【Color】", "【child】: <PERSON><PERSON><PERSON>   【Widget】"]}, {"file": "node2_shape.dart", "name": "Pode-se alcançar o efeito de corte através da propriedade shape", "desc": ["【shape】 : Forma  【ShapeBorder】", "【margin】: Margem externa   【double】", "【color】: Cor   【Color】", "【child】: <PERSON><PERSON><PERSON>   【Widget】"]}]}