{"id": 3, "name": "Card", "localName": "Componente Carta", "info": "Implementato basandosi sui componenti Material, utilizzato per trasformare un singolo componente in una carta. Con effetti di ombreggiatura, può includere margini esterni e consente la personalizzazione della forma della carta.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Card può trasformare un componente in una carta", "desc": ["【elevation】 : profondità dell'ombra  【double】", "【margin】: margine esterno   【double】", "【color】: colore   【Color】", "【child】: figlio   【Widget】"]}, {"file": "node2_shape.dart", "name": "È possibile ottenere effetti di ritaglio attraverso la proprietà shape", "desc": ["【shape】 : forma  【ShapeBorder】", "【margin】: margine esterno   【double】", "【color】: colore   【Color】", "【child】: figlio   【Widget】"]}]}