{"id": 159, "name": "PositionedDirectional", "localName": "Posicionamiento direccional", "info": "Funciona igual que el componente Positioned, pero con nombres de propiedades diferentes. Solo se puede usar en Stack, y permite colocar un componente con precisión especificando las distancias desde la parte superior, inferior, izquierda y derecha.", "lever": 3, "family": 0, "linkIds": [108, 122], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de PositionedDirectional", "desc": ["【child】 : Componente   【Widget】", "【top】 : Distancia desde la parte superior del padre   【double】", "【end】 : <PERSON><PERSON><PERSON> desde la derecha del padre   【double】", "【start】 : Distancia desde la izquierda del padre   【double】", "【bottom】 : Distancia desde la parte inferior del padre   【double】"]}]}