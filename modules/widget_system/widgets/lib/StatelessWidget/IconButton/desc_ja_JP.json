{"id": 30, "name": "IconButton", "localName": "アイコンボタン", "info": "クリック可能なアイコンボタンで、アイコン情報、パディング、サイズ、色などを指定でき、クリックイベントを受け取ります。", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "IconButtonプロパティ", "desc": ["【icon】: アイコンコンポーネント   【Widget】", "【tooltip】: 長押し時のヒントテキスト   【String】", "【highlightColor】: 長押し時のハイライト色   【Color】", "【splashColor】: 波紋色   【Color】", "【onPressed】: クリックイベント   【Function】"]}]}