{"id": 30, "name": "IconButton", "localName": "Кнопка с иконкой", "info": "Кликабельная кнопка с иконкой, можно указать информацию об иконке, внутренние отступы, размер, цвет и т.д., принимает событие клика.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Свойства IconButton", "desc": ["【icon】: Компонент иконки   【Widget】", "【tooltip】: Текст подсказки при долгом нажатии   【String】", "【highlightColor】: Цвет подсветки при долгом нажатии   【Color】", "【splashColor】: Цвет эффекта волны   【Color】", "【onPressed】: Событие клика   【Function】"]}]}