{"id": 221, "name": "DraggableScrollableActuator", "localName": "Drag and Slide Resetter", "info": "It can notify the descendant DraggableScrollableSheet to reset its position to the initial state.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage", "desc": ["【child】: Child component   【Widget】", "Use DraggableScrollableActuator.reset(context) to reset the position of the descendant DraggableScrollableSheet to the initial position."]}]}