{"id": 210, "name": "PageStorage", "localName": "Seitenspeicher", "info": "Kann den Seitenstatus speichern und den Status beim Wechseln der Seite beibehalten. Im Quellcode wird es in ScrollView, PageView, ExpansionTile usw. verwendet.", "lever": 3, "family": 0, "linkIds": [52, 165], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von PageS<PERSON>age", "desc": ["【bucket】 : Speicherbereich   【PageStorageBucket】", "【child】: Untergeordnete Komponente   【Widget】", "Beim Initialisieren der Komponente beim Wechseln der Oberfläche wird der Status nicht zurückgesetzt. Wie bei CountWidget muss die untergeordnete Komponente beim Initialisieren den Status aus dem Speicher lesen und beim Ändern des Status den Status in den Speicher schreiben. Außerdem ist PageStorage bereits in MaterialApp integriert, aber Si<PERSON> können auch ein PageStorage erstellen."]}]}