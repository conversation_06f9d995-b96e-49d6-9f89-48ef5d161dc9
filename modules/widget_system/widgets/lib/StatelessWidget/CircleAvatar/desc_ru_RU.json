{"id": 9, "name": "CircleAvatar", "localName": "Круглый компонент", "info": "Может превратить изображение в круг и разместить компонент в центре. Можно указать радиус, цвет переднего плана, цвет фона и т.д.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Поведение CircleAvatar", "desc": ["【radius】 : радиус  【double】", "【backgroundImage】 : ресурс изображения  【ImageProvider】", "【foregroundColor】: цвет переднего плана   【Color】", "【backgroundColor】: цвет фона   【Color】", "【minRadius】: минимальный радиус   【double】", "【maxRadius】: максимальный радиус   【double】", "【child】: дочерний компонент   【Child】"]}]}