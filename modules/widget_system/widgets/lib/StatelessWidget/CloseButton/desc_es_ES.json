{"id": 32, "name": "CloseButton", "localName": "Botón de c<PERSON>rar", "info": "Un IconButton con función de cerrar, el ícono de cerrar no se puede cambiar.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Evento de clic de CloseButton", "desc": ["【onPressed】: Evento de clic  【VoidCallback?】", "【style】: <PERSON><PERSON><PERSON> del botón   【ButtonStyle?】", "【color】: Color   【Color】", "<PERSON>uando onPressed es nulo, al hacer clic se cerrará la interfaz actual."]}]}