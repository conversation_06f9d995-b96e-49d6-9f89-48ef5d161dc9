{"id": 220, "name": "NotificationListener", "localName": "Écouteur de notifications", "info": "Vous pouvez spécifier le sous-générique T de Notification pour écouter les changements de ce type. Flutter intègre de nombreuses notifications de défilement, bien sûr, vous pouvez également personnaliser les notifications pour les écouter.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Écouter OverscrollIndicatorNotification", "desc": ["Cette notification est rappelée uniquement lors du défilement vers le haut ou le bas, en utilisant la propriété leading pour déterminer si c'est le haut ou le bas. De plus, en utilisant notification#disallowGlow(), vous pouvez supprimer l'ombre bleue du défilement en haut et en bas."]}, {"file": "node2_update.dart", "name": "Écouter ScrollUpdateNotification", "desc": ["Pendant le défilement, les données de défilement sont rappelées, vous pouvez obtenir de nombreuses données pour les manipuler."]}]}