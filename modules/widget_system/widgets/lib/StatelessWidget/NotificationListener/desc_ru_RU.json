{"id": 220, "name": "NotificationListener", "localName": "Слушатель уведомлений", "info": "Можно указать подтип T для Notification, чтобы отслеживать изменения этого типа. В Flutter встроено множество уведомлений о прокрутке, конечно, вы также можете настроить свои собственные уведомления для отслеживания.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Отслеживание OverscrollIndicatorNotification", "desc": ["Это уведомление вызывается только при прокрутке до самого верха или низа, и можно определить, верх это или низ, с помощью свойства leading. Кроме того, с помощью notification#disallowGlow() можно убрать синюю тень при прокрутке вверху и внизу."]}, {"file": "node2_update.dart", "name": "Отслеживание ScrollUpdateNotification", "desc": ["В процессе прокрутки данные о прокрутке передаются обратно, и вы можете получить множество данных для работы."]}]}