{"id": 360, "name": "RawMagnifier", "localName": "Исходная лупа", "info": "Лу<PERSON>а, которая может увеличивать любой компонент под ней, можно настроить такие свойства, как форма декора, размер, смещение и т.д.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Простое использование RawMagnifier", "desc": ["【size】 : Размер  【Size】", "【magnificationScale】 : Увеличение  【double】", "【focalPointOffset】: Смещение увеличенного содержимого   【Offset】", "【decoration】: Обнаружение декора   【MagnifierDecoration】", "【child】: Доче<PERSON>ний компонент   【Widget?】,"]}, {"file": "node2_shape.dart", "name": "Пользовательская форма декора RawMagnifier", "desc": ["Здесь пользовательская форма декора в виде пятиконечной звезды"]}]}