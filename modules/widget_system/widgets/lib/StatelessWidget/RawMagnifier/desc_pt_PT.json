{"id": 360, "name": "RawMagnifier", "localName": "Lupa Original", "info": "Uma lupa que pode ampliar qualquer componente abaixo, com propriedades configuráveis como forma de decoração, tamanho, deslocamento, etc.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso simples do RawMagnifier", "desc": ["【size】 : <PERSON><PERSON><PERSON>  【Size】", "【magnificationScale】 : Escala de ampliação  【double】", "【focalPointOffset】: Deslocamento do conteúdo ampliado   【Offset】", "【decoration】: Decoração descoberta   【MagnifierDecoration】", "【child】: Component<PERSON> filho   【Widget?】,"]}, {"file": "node2_shape.dart", "name": "Forma de decoração personalizada do RawMagnifier", "desc": ["Aqui, personaliza-se a forma de decoração em estrela de cinco pontas"]}]}