{"id": 4, "name": "FlutterLogo", "localName": "Icône Flutter", "info": "Utilisé pour afficher le composant d'icône Flutter. Permet de définir des informations telles que la couleur, la taille, le mode d'affichage, etc. C'est un composant très simple.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisé pour afficher un FlutterLogo", "desc": ["【size】 : Taille  【double】", "【colors】: Couleur   【MaterialColor】"]}, {"file": "node2_style.dart", "name": "Style utilisé pour afficher du texte", "desc": ["【style】 : Style - 3 types d'énumération  【FlutterLogoStyle】", "【textColor】: <PERSON>uleur du texte   【Color】"]}]}