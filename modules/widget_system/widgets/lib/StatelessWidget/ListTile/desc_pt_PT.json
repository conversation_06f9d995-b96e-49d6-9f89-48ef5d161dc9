{"id": 16, "name": "ListTile", "localName": "Azulejo de Lista", "info": "Uma estrutura de item de lista genérica fornecida pelo Flutter, com uma estrutura de esquerda, centro e direita. Componentes podem ser inseridos nas posições correspondentes, o que pode lidar facilmente com itens específicos.", "lever": 3, "family": 0, "linkIds": [162, 334], "nodes": [{"file": "node1_base.dart", "name": "A apresentação básica do ListTile é a seguinte", "desc": ["【leading】: Componente esquerdo   【Widget】", "【title】: Componente superior central   【Widget】", "【subtitle】: Componente inferior central   【Widget】", "【trailing】: Componente final   【Widget】", "【contentPadding】: Preenchimento interno   【EdgeInsetsGeometry】", "【onLongPress】: Evento de clique   【Function()】"]}, {"file": "node2_select.dart", "name": "Efeito de seleção e evento de pressionamento longo do ListTile", "desc": ["【selected】: Se está selecionado   【bool】", "【onTap】: Evento de clique   【Function()】"]}, {"file": "node3_dense.dart", "name": "Propriedade de densidade do ListTile", "desc": ["【dense】: Se <PERSON> denso   【bool】"]}]}