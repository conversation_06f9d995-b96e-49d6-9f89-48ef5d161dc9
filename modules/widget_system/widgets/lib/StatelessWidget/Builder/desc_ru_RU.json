{"id": 202, "name": "Builder", "localName": "Конструктор", "info": "Компонент, который не влияет на занимаемое пространство дочерних компонентов и не имеет визуального отображения. Его единственная цель - предоставить контекст для соответствующего элемента текущего компонента.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Использование Builder", "desc": ["【builder】 : Конструктор компонента   【WidgetBuilder】", "Использование метода `XXX.of(context)` для получения объекта состояния определенного класса в одном и том же классе может привести к ошибке `задержки контекста`. Используйте Builder для решения этой проблемы."]}]}