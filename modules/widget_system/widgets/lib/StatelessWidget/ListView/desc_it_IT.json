{"id": 162, "name": "ListView", "localName": "Componente Lista", "info": "Leader nella visualizzazione delle liste, contiene più componenti figli, può essere costruito con builder, separated, custom, ecc. Ha proprietà come padding, reverse, controller di scorrimento, ecc.", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "Uso base di ListView", "desc": ["【children】 : Lista dei componenti figli   【List<Widget>】", "【padding】 : Padding interno  【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "Scorrimento orizzontale di ListView", "desc": ["【scrollDirection】 : Direzione di scorrimento   【Axis】", "【reverse】 : Scorrimento inverso   【bool】", "【shrinkWrap】 : Avvolgere quando non ci sono confini  【bool】"]}, {"file": "node3_builder.dart", "name": "Costruzione di ListView.builder", "desc": ["【itemCount】 : Numero di elementi   【int】", "【itemBuilder】 : Costruttore di elementi   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "Costruzione di ListView.separated", "desc": ["【separatorBuilder】 : Costruttore di elementi   【IndexedWidgetBuilder】"]}]}