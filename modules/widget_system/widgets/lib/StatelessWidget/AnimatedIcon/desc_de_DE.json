{"id": 125, "name": "AnimatedIcon", "localName": "Animiertes Symbol", "info": "Verwenden Sie die Symboldaten von AnimatedIcons, um Symbole mit einem Animationscontroller zu animieren. Größe, Farbe usw. können angegeben werden.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AnimatedIcon", "desc": ["【icon】 : Animationssymboldaten   【AnimatedIcons】", "【size】 : Größe  【double】", "【color】 : Farbe  【Color】", "【progress】 : Animation   【Animation<double>】"]}]}