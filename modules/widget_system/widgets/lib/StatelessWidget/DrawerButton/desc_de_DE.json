{"id": 276, "name": "<PERSON>er<PERSON><PERSON><PERSON>", "localName": "Schubladenknopf", "info": "Ein linker Schubladensymbolknopf, der das Symbol mit DrawerButtonIcon anzeigt. Das Standardklickereignis kann die linke Schublade öffnen.", "lever": 1, "family": 0, "linkIds": [273, 361], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>", "desc": ["【onPressed】: Klickereignis 【VoidCallback?】", "【style】: Schaltflächenstil 【ButtonStyle?】", "<PERSON><PERSON> on<PERSON><PERSON> leer ist, wird beim Klicken die linke Schublade geöffnet."]}]}