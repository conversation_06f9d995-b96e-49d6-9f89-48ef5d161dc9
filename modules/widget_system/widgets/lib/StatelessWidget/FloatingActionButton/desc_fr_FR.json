{"id": 28, "name": "FloatingActionButton", "localName": "Bouton flottant", "info": "Le bouton flottant, généralement utilisé dans Scaffold, peut être placé à un endroit spécifique. Il peut contenir un composant enfant, recevoir des clics, et définir des couleurs, des formes, etc.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Événement de clic du FloatingActionButton", "desc": ["【child】: <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【tooltip】: Texte d'aide lors d'un appui long   【String】", "【backgroundColor】: <PERSON>uleur de fond   【Color】", "【foregroundColor】: Couleur de premier plan   【Color】", "【elevation】: Profondeur de l'ombre   【double】", "【onPressed】: Événement de clic   【Function】"]}, {"file": "node2_mini.dart", "name": "Propriété mini", "desc": ["【mini】: Est-ce un mini bouton   【bool】"]}, {"file": "node3_shape.dart", "name": "Propriété de forme", "desc": ["【shape】: Forme   【ShapeBorder】"]}]}