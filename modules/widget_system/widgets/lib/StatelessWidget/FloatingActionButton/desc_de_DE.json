{"id": 28, "name": "FloatingActionButton", "localName": "Schwebende Schaltfläche", "info": "Schwebende Schaltfläche, die normalerweise in Scaffold verwendet wird und an einer bestimmten Position platziert werden kann. Kann ein Unterelement enthalten, empfäng<PERSON> Klicks, und kann Farbe, Form usw. definieren.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "FloatingAction<PERSON><PERSON><PERSON>", "desc": ["【child】: Unterelement   【Widget】", "【tooltip】: Tooltip-Text bei langem Drücken   【String】", "【backgroundColor】: Hintergrundfarbe   【Color】", "【foregroundColor】: Vordergrundfarbe   【Color】", "【elevation】: Sc<PERSON>tentiefe   【double】", "【onPressed】: Klickereignis   【Function】"]}, {"file": "node2_mini.dart", "name": "mini Eigenschaft", "desc": ["【mini】: Ist es mini   【bool】"]}, {"file": "node3_shape.dart", "name": "shape Eigenschaft", "desc": ["【shape】: Form   【ShapeBorder】"]}]}