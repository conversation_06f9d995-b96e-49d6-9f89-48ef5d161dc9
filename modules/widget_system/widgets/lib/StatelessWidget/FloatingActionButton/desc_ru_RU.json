{"id": 28, "name": "FloatingActionButton", "localName": "Плавающая кнопка", "info": "Плавающая кнопка, обычно используется в Scaffold, может быть размещена в определенном месте. Может содержать один дочерний компонент, принимает клики, может определять цвет, форму и т.д.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Событие нажатия FloatingActionButton", "desc": ["【child】: До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【tooltip】: Текст подсказки при долгом нажатии   【String】", "【backgroundColor】: Цвет фона   【Color】", "【foregroundColor】: Цвет переднего плана   【Color】", "【elevation】: Г<PERSON><PERSON><PERSON><PERSON>на тени   【double】", "【onPressed】: Событие нажатия   【Function】"]}, {"file": "node2_mini.dart", "name": "Свойство mini", "desc": ["【mini】: Является ли мини   【bool】"]}, {"file": "node3_shape.dart", "name": "Свойство shape", "desc": ["【shape】: Форма   【ShapeBorder】"]}]}