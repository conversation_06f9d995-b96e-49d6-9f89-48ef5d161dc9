{"id": 134, "name": "DayPicker", "localName": "<PERSON><PERSON><PERSON>", "info": "Componente de seleção de data, pode especificar a data atual, a data selecionada, o mês exibido, etc., e recebe eventos de seleção de data.", "lever": 3, "family": 0, "deprecated": -1, "linkIds": [135, 136], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do DayPicker", "desc": ["【selectedDate】 : Data selecionada   【DateTime】", "【currentDate】 : Data atual   【DateTime】", "【firstDate】 : Limite da data mais antiga   【DateTime】", "【lastDate】 : Limite da data mais recente   【DateTime】", "【displayedMonth】 : Mês atualmente exibido   【DateTime】", "【onChanged】 : Callback de clique  【Function(DateTime)】", "    ", "import 'package:flutter/material.dart';", "", "class CustomDayPicker extends StatelessWidget{", "", "  const CustomDayPicker({Key? key) : super(key: key);", "", "  final String info =", "      'O DayPicker, o seletor de data, saiu de cena no Flutter 3.0. O substituto é o CalendarDatePicker, o seletor de calendário.';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", "   final DateTime _date = DateTime.now();", "  ", "   @override", "   Widget build(BuildContext context) {", "     return SizedBox(", "       height: 350,", "       child: <PERSON><PERSON><PERSON>(", "           selectedDate: _date,", "           currentDate: DateTime.now(),", "           onChanged: (date)=> setState(() => _date = date),", "           firstDate:  DateTime(2018),", "           lastDate: DateTime(2030),", "           displayedMonth: DateTime.now()", "       ),"]}]}