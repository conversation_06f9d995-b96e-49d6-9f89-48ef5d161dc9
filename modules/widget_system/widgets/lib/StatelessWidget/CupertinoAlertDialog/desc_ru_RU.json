{"id": 129, "name": "CupertinoAlertDialog", "localName": "Диалоговое окно iOS", "info": "Универсальная структура диалогового окна в стиле iOS, позволяющая указать компоненты в верхней, средней и нижней частях.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование CupertinoActionSheetAction", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【isDefaultAction】 : Выбрано ли по умолчанию  【bool】", "【onPressed】 : Событие нажатия  【Function()】"]}]}