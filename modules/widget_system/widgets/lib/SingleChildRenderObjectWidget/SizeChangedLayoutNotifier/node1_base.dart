import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2020/9/21
/// contact me <NAME_EMAIL>


class SizeChangedLayoutNotifierDemo extends StatefulWidget {
  const SizeChangedLayoutNotifierDemo({Key? key}) : super(key: key);

  @override
  _SizeChangedLayoutNotifierDemoState createState() => _SizeChangedLayoutNotifierDemoState();
}

class _SizeChangedLayoutNotifierDemoState extends State<SizeChangedLayoutNotifierDemo> {
  @override
  Widget build(BuildContext context) {
    return NotificationListener<SizeChangedLayoutNotification>(
      onNotification: _onNotification,
      child: const ChangeableBox(),
    );
  }

  bool _onNotification(SizeChangedLayoutNotification notification) {
    print('---------SizeChangedLayoutNotification------');
    return false;
  }
}

class ChangeableBox extends StatefulWidget {
  const ChangeableBox({Key? key}) : super(key: key);

  @override
  _ChangeableBoxState createState() => _ChangeableBoxState();
}

class _ChangeableBoxState extends State<ChangeableBox> {
  double width = 40;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizeChangedLayoutNotifier(
          child: Container(
            width: width,
            height: 100,
            color: Colors.blue,
          ),
        ),
        Slider(
          max: 200,
          min: 20,
          divisions: 10,
          value: width,
          onChanged: _changeWidth,
        )
      ],
    );
  }

  void _changeWidth(double value) {
    setState(() {
      width = value;
    });
  }
}
