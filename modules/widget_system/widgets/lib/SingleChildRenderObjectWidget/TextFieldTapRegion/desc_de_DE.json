{"id": 281, "name": "TextFieldTapRegion", "localName": "Eingabefeld-Klickbereich", "info": "TapRegion mit der groupId EditableText, der den Klickbereich anderer Komponenten als Teil des Eingabefelds behandelt.", "lever": 3, "family": 2, "linkIds": [280, 54, 245], "nodes": [{"file": "node1.dart", "name": "Klicks innerhalb und außerhalb der Komponente überwachen", "desc": ["Im Be<PERSON>piel wird der Fokus des Eingabefelds nicht aufgehoben, wenn auf das Plus- oder Minuszeichen geklickt wird, und die Tastatur kann weiterhin verwendet werden.", "【enabled】 : Verfügbar   【bool】", "【onTapOutside】 : <PERSON><PERSON><PERSON><PERSON><PERSON> von Klicks außerhalb   【TapRegionCallback?】", "【onTapInside】 : <PERSON><PERSON><PERSON><PERSON><PERSON> von Klicks innerhalb   【TapRegionCallback?】", "【groupId】 : Gruppenkennung des Klickbereichs   【Object?】"]}]}