{"id": 295, "name": "AbsorbPointer", "localName": "클릭 흡수", "info": "자식 위젯을 포함하며, ignoring 속성을 지정하여 자식이 제스처 이벤트를 무시할지 여부를 결정할 수 있습니다. 그 자체는 이벤트를 받습니다.", "lever": 4, "family": 2, "linkIds": [146, 149, 150, 292], "nodes": [{"file": "node1_base.dart", "name": "AbsorbPointer 기본 사용법", "desc": ["【child】 : 자식 위젯   【Widget】", "【absorbing】 : 이벤트를 흡수할지 여부   【bool】", "아래와 같이, Switch가 선택되면 absorbing이 true가 되어 버튼 이벤트가 흡수되어 클릭할 수 없게 됩니다."]}]}