{"id": 277, "name": "ShaderMask", "localName": "<PERSON>r-<PERSON><PERSON>", "info": "Kann ein Kind aufnehmen und das Kind durch einen Shader einfärben, wobe<PERSON> der Mischmodus angegeben werden kann. Wird häufig für die Farbverlaufsverarbeitung von Komponenten verwendet.", "lever": 4, "family": 2, "linkIds": [88, 38], "nodes": [{"file": "node1_radial.dart", "name": "<PERSON><PERSON><PERSON>", "desc": ["【child】 : Kindkomponente   【Widget】", "【shaderCallback】 : <PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>   【ShaderCallback】", "【blendMode】 : Mischmodus   【BlendMode】", "    Erstellt einen radialen Farbverlaufsshader durch RadialGradient#createShader."]}, {"file": "node2_linear.dart", "name": "<PERSON>are<PERSON>", "desc": ["Erstellt einen linearen Farbverlaufsshader durch LinearGradient#createShader", "Weitere Informationen zu Shadern finden Sie im 【Zeichnungsalbum】"]}]}