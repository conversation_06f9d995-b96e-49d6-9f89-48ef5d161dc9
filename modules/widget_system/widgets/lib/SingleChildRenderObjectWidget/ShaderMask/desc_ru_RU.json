{"id": 277, "name": "ShaderMask", "localName": "Маска шейдера", "info": "Может содержать один дочерний элемент и применять к нему шейдер, можно указать режим смешивания. Обычно используется для градиентной обработки компонентов.", "lever": 4, "family": 2, "linkIds": [88, 38], "nodes": [{"file": "node1_radial.dart", "name": "Радиальный градиент", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【shaderCallback】 : Ко<PERSON><PERSON><PERSON><PERSON>к шейдера   【ShaderCallback】", "【blendMode】 : Режим смешивания   【BlendMode】", "    Создание радиального градиентного шейдера через RadialGradient#createShader."]}, {"file": "node2_linear.dart", "name": "Линейный градиент", "desc": ["Создание линейного градиентного шейдера через LinearGradient#createShader", "Подробнее о шейдерах см. в 【Альбоме рисования】"]}]}