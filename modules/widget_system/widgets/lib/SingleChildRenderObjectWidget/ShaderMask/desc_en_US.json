{"id": 277, "name": "ShaderMask", "localName": "Shader Mask", "info": "Can accommodate a child and apply shading to the child through a shader, with the ability to specify blending modes. Commonly used for gradient processing of components.", "lever": 4, "family": 2, "linkIds": [88, 38], "nodes": [{"file": "node1_radial.dart", "name": "Ra<PERSON> Shading", "desc": ["【child】: Child component   【Widget】", "【shaderCallback】: Shader callback   【ShaderCallback】", "【blendMode】: Blending mode   【BlendMode】", "    Create a radial gradient shader through RadialGradient#createShader."]}, {"file": "node2_linear.dart", "name": "Linear Gradient Shading", "desc": ["Create a linear gradient shader through LinearGradient#createShader", "For more information on shaders, see 【Drawing Album】"]}]}