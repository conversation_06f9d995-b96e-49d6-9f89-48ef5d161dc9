{"id": 277, "name": "ShaderMask", "localName": "着色器遮罩", "info": "可容纳一个孩子，并通过着色器来对孩子进行着色，可指定混色模式。通常用于组件渐变色处理。", "lever": 4, "family": 2, "linkIds": [88, 38], "nodes": [{"file": "node1_radial.dart", "name": "径向渐变着色", "desc": ["【child】 : 孩子组件   【Widget】", "【shaderCallback】 : 着色器回调   【ShaderCallback】", "【blendMode】 : 混色模式   【BlendMode】", "    通过RadialGradient#createShader创建径向渐变着色器。"]}, {"file": "node2_linear.dart", "name": "线性渐变着色", "desc": ["通过LinearGradient#createShader创建线性渐变着色器", "着色器相关知识详见【绘制专辑】"]}]}