{"id": 78, "name": "Transform", "localName": "変換", "info": "子コンポーネントを収容でき、4*4の変換マトリックスを使用して子コンポーネントを変換できます。", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_skew.dart", "name": "スキュー変換skew", "desc": ["スキューxはR0C1の数値で制御され、入力はラジアン値で、スキュー角度を表します", "スキューyはR1C0の数値で制御され、入力はラジアン値で、スキュー角度を表します"]}, {"file": "node2_translation.dart", "name": "平行移動変換translationValues", "desc": ["平行移動xはR0C3の数値で制御され、入力は数値で、平行移動の長さを表します", "平行移動yはR1C3の数値で制御され、入力は数値で、平行移動の長さを表します", "平行移動zはR2C3の数値で制御され、入力は数値で、平行移動の長さを表します"]}, {"file": "node3_scale.dart", "name": "スケール変換diagonal3Values", "desc": ["スケールxはR0C0の数値で制御され、入力は数値で、スケールの比率を表します", "スケールyはR1C2の数値で制御され、入力は数値で、スケールの比率を表します", "スケールzはR2C2の数値で制御され、入力は数値で、スケールの比率を表します"]}, {"file": "node4_rotate.dart", "name": "回転変換rotation", "desc": ["x回転はR1C1、R1C2、R2C1、R2C2で制御され、入力はラジアンを表します", "y回転はR0C0、R0C2、R2C0、R2C2で制御され、入力はラジアンを表します", "z回転はR0C0、R0C1、R1C0、R1C1で制御されます"]}, {"file": "node5_perspective.dart", "name": "遠近法変換rotation", "desc": ["遠近法はR3C1、R3C2、R3C3で制御されます"]}]}