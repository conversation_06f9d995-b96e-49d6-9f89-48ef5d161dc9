{"id": 78, "name": "Transform", "localName": "변환", "info": "하위 컴포넌트를 수용할 수 있으며, 4*4 변환 행렬을 통해 하위 컴포넌트를 변환할 수 있습니다.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_skew.dart", "name": "기울임 변환 skew", "desc": ["x 기울임은 R0C1 숫자로 제어되며, 입력은 라디안 값으로 기울임 각도를 나타냅니다.", "y 기울임은 R1C0 숫자로 제어되며, 입력은 라디안 값으로 기울임 각도를 나타냅니다."]}, {"file": "node2_translation.dart", "name": "이동 변환 translationValues", "desc": ["x 이동은 R0C3 숫자로 제어되며, 입력은 숫자 값으로 이동 길이를 나타냅니다.", "y 이동은 R1C3 숫자로 제어되며, 입력은 숫자 값으로 이동 길이를 나타냅니다.", "z 이동은 R2C3 숫자로 제어되며, 입력은 숫자 값으로 이동 길이를 나타냅니다."]}, {"file": "node3_scale.dart", "name": "크기 조절 변환 diagonal3Values", "desc": ["x 크기 조절은 R0C0 숫자로 제어되며, 입력은 숫자 값으로 크기 비율을 나타냅니다.", "y 크기 조절은 R1C2 숫자로 제어되며, 입력은 숫자 값으로 크기 비율을 나타냅니다.", "z 크기 조절은 R2C2 숫자로 제어되며, 입력은 숫자 값으로 크기 비율을 나타냅니다."]}, {"file": "node4_rotate.dart", "name": "회전 변환 rotation", "desc": ["x 회전은 R1C1, R1C2, R2C1, R2C2로 제어되며, 입력은 라디안 값을 나타냅니다.", "y 회전은 R0C0, R0C2, R2C0, R2C2로 제어되며, 입력은 라디안 값을 나타냅니다.", "z 회전은 R0C0, R0C1, R1C0, R1C1로 제어됩니다."]}, {"file": "node5_perspective.dart", "name": "원근 변환 rotation", "desc": ["R3C1, R3C2, R3C3로 원근을 제어합니다."]}]}