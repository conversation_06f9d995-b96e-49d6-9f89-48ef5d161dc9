{"id": 73, "name": "Opacity", "localName": "Trasparenza", "info": "<PERSON><PERSON>ò contenere un componente figlio, trasformandone la trasparenza in un valore di opacità, dove l'opacità è compresa tra 0 e 1.", "lever": 3, "family": 2, "linkIds": [89, 118], "nodes": [{"file": "node1_base.dart", "name": "Uso base di Opacity", "desc": ["【child】 : Componente figlio   【Widget】", "【opacity】 : Trasparenza 0~1   【double】"]}]}