{"id": 285, "name": "CustomSingleChildLayout", "localName": "Layout de Filho Único Personalizado", "info": "Pode acomodar um componente filho e especificar uma classe de proxy para organizar o componente filho. A classe de proxy pode obter a área do contêiner pai e o tamanho da área do componente filho, bem como as restrições da área.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do CustomSingleChildLayout", "desc": ["【delegate】 : proxy   【SingleChildLayoutDelegate】"]}, {"file": "node2_offset.dart", "name": "Uso de Deslocamento do CustomSingleChildLayout", "desc": ["Pode utilizar a capacidade de deslocamento do proxy para posicionar o componente filho com deslocamento."]}]}