{"id": 285, "name": "CustomSingleChildLayout", "localName": "Universal Single Child Layout", "info": "Can accommodate one child component and specify a delegate class to arrange the child component. The delegate class can obtain the parent container area, the child component's area size, and the area constraints.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of CustomSingleChildLayout", "desc": ["【delegate】 : delegate   【SingleChildLayoutDelegate】"]}, {"file": "node2_offset.dart", "name": "Offset Usage of CustomSingleChildLayout", "desc": ["The offset capability of the delegate can be used to position the child component with an offset."]}]}