{"id": 285, "name": "CustomSingleChildLayout", "localName": "Disposition personnalisée à un seul enfant", "info": "Peut contenir un seul composant enfant et spécifie une classe déléguée pour organiser le composant enfant. La classe déléguée peut obtenir la zone du conteneur parent et la taille de la zone du composant enfant, ainsi que les contraintes de zone.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de CustomSingleChildLayout", "desc": ["【delegate】 : <PERSON><PERSON><PERSON><PERSON><PERSON>   【SingleChildLayoutDelegate】"]}, {"file": "node2_offset.dart", "name": "Utilisation du décalage de CustomSingleChildLayout", "desc": ["Peut utiliser la capacité de décalage du délégué pour positionner le composant enfant avec un décalage."]}]}