{"id": 265, "name": "CompositedTransformFollower", "localName": "Suiveur de transformation composite", "info": "Généralement utilisé conjointement avec le composant CompositedTransformTarget, permet à l'Overlay de suivre la transformation de la cible.", "lever": 3, "family": 2, "linkIds": [266, 182], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de CompositedTransformFollower", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【link】 : Lien   【LayerLink】", "【offset】 : Décalage   【Offset】", "【targetAnchor】 : Point d'ancrage de la cible   【Alignment】", "【followerAnchor】 : Point d'ancrage du suiveur   【Alignment】", "【showWhenUnlinked】 : Afficher si non lié   【bool】"]}]}