{"id": 265, "name": "CompositedTransformFollower", "localName": "합성 변형 팔로워", "info": "일반적으로 CompositedTransformTarget 컴포넌트와 함께 사용되어 Overlay가 대상 변형을 따라가도록 합니다.", "lever": 3, "family": 2, "linkIds": [266, 182], "nodes": [{"file": "node1_base.dart", "name": "CompositedTransformFollower 사용", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【link】 : 링크   【LayerLink】", "【offset】 : 오프셋   【Offset】", "【targetAnchor】 : 타겟 앵커   【Alignment】", "【followerAnchor】 : 팔로워 앵커   【Alignment】", "【showWhenUnlinked】 : 링크되지 않을 때 표시 여부   【bool】"]}]}