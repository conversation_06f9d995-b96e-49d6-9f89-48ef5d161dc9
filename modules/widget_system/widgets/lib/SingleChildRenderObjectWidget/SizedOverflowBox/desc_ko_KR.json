{"id": 84, "name": "SizedOverflowBox", "localName": "크기 오버플로우 박스", "info": "하위 컴포넌트를 포함할 수 있으며, 하위 컴포넌트가 상위 컴포넌트 영역을 벗어날 수 있습니다. size 속성을 통해 하위 컴포넌트를 오프셋할 수 있으며, alignment 속성을 가지고 있습니다.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "SizedOverflowBox 기본 사용", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【size】 : 크기 오프셋   【Size】", "【alignment】 : 정렬 방식   【AlignmentGeometry】"]}]}