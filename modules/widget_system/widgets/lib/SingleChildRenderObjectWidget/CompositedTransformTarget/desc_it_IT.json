{"id": 266, "name": "CompositedTransformTarget", "localName": "Obiettivo di trasformazione composita", "info": "Generalmente utilizzato insieme al componente CompositedTransformFollower, consente all'Overlay di seguire le trasformazioni dell'obiettivo.", "lever": 3, "family": 2, "linkIds": [265, 182], "nodes": [{"file": "node1_base.dart", "name": "Utilizzo di CompositedTransformTarget", "desc": ["【child】 : Componente figlio   【Widget】", "【link】 : Collegamento   【LayerLink】"]}]}