{"id": 82, "name": "FractionallySizedBox", "localName": "分率ボックス", "info": "子コンポーネントを収容し、幅と高さの分率を指定し、子コンポーネントの領域を親コンテナの幅と高さ×各分率に制限し、および配置方法alignmentを指定します。", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "FractionallySizedBoxの基本使用", "desc": ["【child】 : 子コンポーネント   【Widget】", "【widthFactor】 : 幅分率   【double】", "【heightFactor】 : 高さ分率   【double】", "【alignment】 : 配置方法   【AlignmentGeometry】"]}]}