{"id": 82, "name": "FractionallySizedBox", "localName": "Caixa de Tamanho Fracionado", "info": "Pode conter um componente filho, especifica a taxa de largura e altura, limitando a área do componente filho à largura e altura do contêiner pai * cada taxa, e o alinhamento.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do FractionallySizedBox", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【widthFactor】 : Taxa de largura   【double】", "【heightFactor】 : Taxa de altura   【double】", "【alignment】 : Alinhamento   【AlignmentGeometry】"]}]}