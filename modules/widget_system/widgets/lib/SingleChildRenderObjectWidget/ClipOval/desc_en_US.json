{"id": 66, "name": "ClipOval", "localName": "Oval Clipping", "info": "Can contain a child component and clip it into an ellipse with the width and height as the major and minor axes.", "lever": 3, "family": 2, "linkIds": [67, 68, 69], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of ClipOval", "desc": ["【child】: Child component   【Widget】", "【clipBehavior】: Clipping behavior   【Clip】", "【clipper】: Clipper   【CustomClipper<RRect>】"]}]}