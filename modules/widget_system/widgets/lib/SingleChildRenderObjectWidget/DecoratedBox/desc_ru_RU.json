{"id": 70, "name": "DecoratedBox", "localName": "Декорированный блок", "info": "Может содержать один дочерний компонент и декорировать его. Основное свойство - decoration, которое позволяет настроить границы, градиенты, тени, фоновые изображения и т.д.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование DecoratedBox", "desc": ["【decoration】 : Объект декора   【Decoration】", "【position】 : Пере<PERSON><PERSON>ий план (слева)/Задний план (справа)   【DecorationPosition】"]}, {"file": "node2_image.dart", "name": "Форма и изображение декора в DecoratedBox", "desc": ["【shape】 : Форма   【BoxShape】", "【image】 : Фоновое изображение   【DecorationImage】,"]}, {"file": "node3_border.dart", "name": "Декорирование границ в DecoratedBox", "desc": ["【border】 : Граница   【BoxBorder】,"]}, {"file": "node4_shape.dart", "name": "Декорирование формы в DecoratedBox", "desc": ["С помощью объекта ShapeDecoration можно указать форму границы,"]}, {"file": "node5_line.dart", "name": "Декорирование нижней линии в DecoratedBox", "desc": ["С помощью объекта UnderlineTabIndicator можно указать нижнюю линию,"]}, {"file": "node6_flutterLogo.dart", "name": "Декорирование FlutterLogoDecoration", "desc": ["С помощью объекта FlutterLogoDecoration можно указать декорирование иконкой Flutter (не имеет особого значения),"]}]}