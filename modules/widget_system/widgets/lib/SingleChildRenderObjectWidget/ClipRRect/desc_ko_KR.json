{"id": 68, "name": "ClipRRect", "localName": "둥근 사각형 클리핑", "info": "하위 위젯을 포함하고 둥근 사각형으로 클리핑할 수 있습니다. borderRadius를 모서리 반지름으로 지정합니다.", "lever": 3, "family": 2, "linkIds": [66, 67, 69], "nodes": [{"file": "node1_base.dart", "name": "ClipRRect 기본 사용법", "desc": ["【child】 : 하위 위젯   【Widget】", "【borderRadius】 : 모서리 반지름   【BorderRadius】", "【clipBehavior】 : 클리핑 동작   【Clip】", "【clipper】 : 클리퍼   【CustomClipper<Rect>】"]}]}