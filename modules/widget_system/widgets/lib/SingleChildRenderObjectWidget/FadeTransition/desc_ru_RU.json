{"id": 89, "name": "FadeTransition", "localName": "Прозрачное преобразование", "info": "Может содержать один дочерний компонент и выполнять анимацию изменения прозрачности, требует предоставления аниматора opacity.", "lever": 3, "family": 2, "linkIds": [73, 118], "nodes": [{"file": "node1_base.dart", "name": "Основное использование FadeTransition", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【opacity】 : Анимация   【Animation<double>】"]}]}