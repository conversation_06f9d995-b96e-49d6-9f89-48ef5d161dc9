{"id": 297, "name": "IntrinsicWidth", "localName": "Larghezza intrinseca", "info": "Un componente che regola le dimensioni dei suoi elementi figli in base alla larghezza intrinseca degli elementi figli, risolve molti problemi di layout, ma è relativamente costoso.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di IntrinsicWidth", "desc": ["【child】 : Componente figlio   【Widget】", "Come nell'esempio: la larghezza superiore è variabile, quella centrale è fissa, quella inferiore prende il valore più alto tra le prime due."]}]}