{"id": 297, "name": "IntrinsicWidth", "localName": "Intrinsic Width", "info": "A component that adjusts the size of its child elements based on their intrinsic width, which can solve many layout issues but is relatively expensive.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of IntrinsicWidth", "desc": ["【child】: Child component 【Widget】", "As shown in the example: the width above can be changed, the width in the middle is fixed, and the width below takes the highest value of the former two."]}]}