{"id": 85, "name": "Align", "localName": "Компонент выравнивания", "info": "Может содержать один дочерний компонент, который можно расположить в любом указанном месте ширины и высоты родительского компонента с помощью alignment.", "lever": 5, "family": 2, "linkIds": [1, 86, 111, 120], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Align", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【alignment】 : Способ выравнивания   【AlignmentGeometry】"]}, {"file": "node2_other.dart", "name": "Другие способы использования Align", "desc": ["Поскольку объект Alignment может указывать положение в долях ширины и высоты родительского контейнера", "Можно использовать Align для реализации сложных требований к расположению, например, для изменения положения по указанному математическому уравнению"]}]}