{"id": 85, "name": "Align", "localName": "정렬 컴포넌트", "info": "하나의 자식 컴포넌트를 수용할 수 있으며, alignment를 통해 자식 컴포넌트를 부모 컴포넌트의 너비와 높이의 지정된 비율 위치에 배치할 수 있습니다.", "lever": 5, "family": 2, "linkIds": [1, 86, 111, 120], "nodes": [{"file": "node1_base.dart", "name": "Align 기본 사용법", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【alignment】 : 정렬 방식   【AlignmentGeometry】"]}, {"file": "node2_other.dart", "name": "Align 기타 사용법", "desc": ["Alignment 객체는 부모 컨테이너에서 너비와 높이의 비율 위치를 지정할 수 있으므로", "Align을 사용하여 복잡한 배치 요구 사항을 구현할 수 있습니다. 예를 들어 지정된 수학 방정식에 따라 위치를 변경하는 것과 같은 요구 사항을 구현할 수 있습니다."]}]}