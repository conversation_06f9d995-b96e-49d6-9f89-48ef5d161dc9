{"id": 74, "name": "Padding", "localName": "Componente de Margem", "info": "Pode acomodar um componente filho, adicionando o seu próprio preenchimento interno para limitar o espaço ocupado pelo componente filho, a propriedade central é o padding.", "lever": 4, "family": 2, "linkIds": [1, 191], "nodes": [{"file": "node1_all.dart", "name": "Padding com Margens Iguais em Todos os Lados", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【padding】 : Margens internas   【EdgeInsetsGeometry】\"", "EdgeInsets.all é usado para definir margens iguais em todos os lados"]}, {"file": "node2_only.dart", "name": "Padding com Margens Individuais", "desc": ["EdgeInsets.only é usado para definir margens específicas"]}, {"file": "node3_symmetric.dart", "name": "Padding com Margens Direcionais", "desc": ["EdgeInsets.symmetric é usado para definir margens horizontais e verticais"]}]}