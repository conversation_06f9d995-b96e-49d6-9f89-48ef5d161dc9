{"id": 69, "name": "<PERSON><PERSON><PERSON><PERSON>", "localName": "Recorte de Caminho", "info": "Pode conter um componente filho e recortá-lo de acordo com o caminho especificado. Permite personalizar a forma do caminho, sendo um componente de recorte muito flexível.", "lever": 5, "family": 2, "linkIds": [66, 67, 68], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do ClipPath", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON>   【Widget】", "【clipBehavior】 : Comportamento de Recorte   【Clip】", "【clipper】 : Recortador   【CustomClipper<Path>】"]}]}