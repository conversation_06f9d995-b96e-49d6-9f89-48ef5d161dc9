{"id": 287, "name": "LayoutBuilder", "localName": "Layout Constructor", "info": "Can detect the size of the parent container and complete custom layouts based on the size information of the parent container. It is a very practical layout component.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Understanding of LayoutBuilder", "desc": ["【builder】: Layout Constructor   【LayoutWidgetBuilder】"]}, {"file": "node2_fit.dart", "name": "Adaptive Layout of LayoutBuilder", "desc": ["Can design component display based on the size of the area.", "For example, displaying different layout structures in different width areas.", "After all, it is not easy to obtain the parent component area in many places, and using LayoutBuilder will be very refreshing."]}, {"file": "node3_expend.dart", "name": "Expanded Use of LayoutBuilder", "desc": ["Use TextPainter to detect the number of lines of text to implement expand or collapse functionality."]}]}