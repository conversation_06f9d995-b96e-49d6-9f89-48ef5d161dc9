{"id": 287, "name": "LayoutBuilder", "localName": "Con<PERSON><PERSON><PERSON>", "info": "Pode detectar o tamanho da área do contêiner pai e completar o layout personalizado de acordo com as informações de dimensão do contêiner pai. É um componente de layout muito útil.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Conhecimento Básico do LayoutBuilder", "desc": ["【builder】 : Construtor de Layout   【LayoutWidgetBuilder】"]}, {"file": "node2_fit.dart", "name": "Adaptação de Layout do LayoutBuilder", "desc": ["Pode projetar a exibição dos componentes de acordo com o tamanho da área.", "Por exemplo, exibir diferentes estruturas de layout em áreas de diferentes larguras.", "Afinal, em muitos lugares não é fácil obter a área do componente pai, usar o LayoutBuilder será muito conveniente."]}, {"file": "node3_expend.dart", "name": "Uso Expandido do LayoutBuilder", "desc": ["Usar TextPainter para detectar o número de linhas de texto e implementar a função de expandir ou recolher."]}]}