{"id": 76, "name": "SizedBox", "localName": "Caixa de Tamanho Fi<PERSON>o", "info": "Pode conter um componente filho, limitando a área do componente filho especificando a largura e altura.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do SizedBox", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【width】 : <PERSON><PERSON><PERSON>   【double】", "【height】 : Altura   【double】"]}]}