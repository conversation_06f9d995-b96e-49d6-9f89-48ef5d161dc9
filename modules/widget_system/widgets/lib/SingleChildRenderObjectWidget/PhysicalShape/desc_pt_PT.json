{"id": 279, "name": "PhysicalShape", "localName": "Forma Física", "info": "Permite que os componentes filhos sejam cortados de acordo com o caminho, e pode especificar a cor de fundo, profundidade da sombra, cor da sombra, comportamento de corte.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do PhysicalShape", "desc": ["【clipper】: Cortador   【CustomClipper<Path>】", "【clipBehavior】: Comportamento de Corte   【Clip】", "【child】: <PERSON><PERSON><PERSON><PERSON>   【Widget】", "【elevation】: Profundidade da Sombra   【double】", "【shadowColor】: <PERSON><PERSON> da Sombra   【Color】", "【color】: Cor   【Color】"]}]}