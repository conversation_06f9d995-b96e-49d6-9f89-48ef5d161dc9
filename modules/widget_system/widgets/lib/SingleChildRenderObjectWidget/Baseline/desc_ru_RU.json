{"id": 75, "name": "Baseline", "localName": "Базовый компонент", "info": "Может содержать один дочерний компонент, контролируя положение дочернего компонента через управление высотой базовой линии. Обычно используется для текстовых компонентов.", "lever": 2, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Baseline", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【baseline】 : Позиция базовой линии   【double】", "【baselineType】 : Тип базовой линии   【TextBaseline】"]}]}