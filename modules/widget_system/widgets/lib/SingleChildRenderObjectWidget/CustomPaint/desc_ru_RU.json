{"id": 166, "name": "CustomPaint", "localName": "Компонент рисования", "info": "Рисование с помощью CustomPainter позволяет реализовать сложные пользовательские компоненты рисования, являясь ключевым элементом пользовательских компонентов во Flutter.", "lever": 5, "family": 2, "linkIds": [], "nodes": [{"file": "node1_clock.dart", "name": "Рисование линий с помощью CustomPaint", "desc": ["【painter】 : рисовальщик   【CustomPainter】"]}, {"file": "node2_bezier.dart", "name": "Рисование кривых Безье с помощью CustomPaint", "desc": ["Flutter также поддерживает сложное рисование, такое как кривые Безье."]}]}