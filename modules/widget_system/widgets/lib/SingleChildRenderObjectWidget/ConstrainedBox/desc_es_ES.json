{"id": 80, "name": "ConstrainedBox", "localName": "Caja con restricciones", "info": "<PERSON><PERSON>e contener un componente hijo, limitando el área del componente hijo especificando el ancho y alto máximo y mínimo.", "lever": 3, "family": 2, "linkIds": [1, 79, 81], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de BoxConstraints", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【minWidth】 : <PERSON><PERSON> m<PERSON>   【double】", "【minHeight】 : <PERSON><PERSON> mínima   【double】", "【maxHeight】 : <PERSON><PERSON> máxima   【double】", "【maxWidth】 : <PERSON><PERSON> m<PERSON>xi<PERSON>   【double】"]}]}