{"id": 298, "name": "IntrinsicHeight", "localName": "Altura intrínseca", "info": "Un componente que ajusta el tamaño de sus hijos según la altura intrínseca de los elementos hijos, puede resolver muchos problemas de diseño, pero es relativamente costoso.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de IntrinsicHeight", "desc": ["【child】 : Widget hijo   【Widget】", "Como en el ejemplo: la altura del lado izquierdo puede variar, la altura del centro es fija, y la altura del lado derecho toma el valor más alto de los dos anteriores."]}]}