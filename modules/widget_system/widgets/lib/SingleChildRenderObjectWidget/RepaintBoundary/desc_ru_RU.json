{"id": 264, "name": "RepaintBoundary", "localName": "Граница перерисовки", "info": "Создает отдельный список отображения для дочерних компонентов, повышая производительность. Используется в компонентах, таких как TextField, DrawerController, Scrollbar, Sliver и других в исходном коде.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование RepaintBoundary", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "Например, в приведенном выше примере отображения, даже если shouldRepaint равен false, метод paint будет постоянно выполняться во время прокрутки. Использование RepaintBoundary позволяет избежать ненужной перерисовки."]}, {"file": "node2_save.dart", "name": "Сохранение Widget в виде изображения", "desc": ["С помощью RenderRepaintBoundary можно получить информацию об изображении дочернего компонента и сохранить его в виде файла изображения."]}]}