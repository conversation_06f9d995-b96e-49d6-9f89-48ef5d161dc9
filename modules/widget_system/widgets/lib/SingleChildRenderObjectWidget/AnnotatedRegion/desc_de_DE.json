{"id": 288, "name": "AnnotatedRegion", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Es gibt einen generischen Typ, der im Quellcode nur in app_bar und nav_bar verwendet wird, um den Zustand und das Styling der Navigationsleiste zu ändern. Der generische Typ ist normalerweise SystemUiOverlayStyle.", "lever": 2, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnnotatedRegion ändert Zustand und Stil", "desc": ["【value】 : Wert   【T】", "【sized】 : Gibt die Größe an   【bool】", "【child】 : Untergeordnete Komponente   【Widget】"]}]}