{"id": 278, "name": "<PERSON>drop<PERSON><PERSON><PERSON>", "localName": "Background Filter", "info": "Can hold a child and apply a blur filter to the background. The background blur effect of the component can be achieved through Stack.", "lever": 4, "family": 2, "linkIds": [88, 97, 67], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of BackdropFilter", "desc": ["【child】: Child component 【Widget】", "【filter】: Filter 【ImageFilter】", "ImageFilter.blur can achieve Gaussian blur by specifying the x and y blur factors."]}]}