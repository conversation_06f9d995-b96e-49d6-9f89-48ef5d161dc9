{"id": 278, "name": "<PERSON>drop<PERSON><PERSON><PERSON>", "localName": "Filtre d'arrière-plan", "info": "Peut contenir un enfant et appliquer un filtre de flou à l'arrière-plan. Le flou de l'arrière-plan peut être réalisé via Stack pour obtenir l'effet de flou du composant.", "lever": 4, "family": 2, "linkIds": [88, 97, 67], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de BackdropFilter", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【filter】 : Filtre   【ImageFilter】", "ImageFilter.blur peut réaliser un flou gaussien en spécifiant les facteurs de flou x et y."]}]}