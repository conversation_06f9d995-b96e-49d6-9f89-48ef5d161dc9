{"id": 341, "name": "CustomMultiChildLayout", "localName": "Disposition multi-enfants universelle", "info": "Utilise une classe déléguée pour contrôler la disposition d'un ensemble de composants enfants. Les composants enfants doivent être identifiés à l'aide du composant LayoutId.", "lever": 4, "family": 3, "linkIds": [315, 285], "nodes": [{"file": "node_01.dart", "name": "Utilisation de base de CustomMultiChildLayout", "desc": ["【children】 : Ensemble de composants enfants   【List<Widget>】", "【delegate】 : <PERSON><PERSON><PERSON><PERSON><PERSON> de disposition   【MultiChildLayoutDelegate】"]}]}