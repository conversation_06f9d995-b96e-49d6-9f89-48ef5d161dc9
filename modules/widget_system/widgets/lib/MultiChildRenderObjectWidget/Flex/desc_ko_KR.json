{"id": 94, "name": "Flex", "localName": "유연한 레이아웃", "info": "Row와 Column의 상위 클래스, Flutter에서 가장 강력한 레이아웃 방식. 여러 컴포넌트를 수용할 수 있으며, Spacer, Expended, Flexible 컴포넌트와 함께 사용하여 유연한 레이아웃을 구성할 수 있습니다.", "lever": 5, "family": 3, "linkIds": [95, 96, 106, 107, 109], "nodes": [{"file": "node_01.dart", "name": "Flex의 배치 방향", "desc": ["【children】 : 컴포넌트 리스트   【List<Widget>】", "【direction】 : 방향   【Axis】"]}, {"file": "node_02.dart", "name": "Flex 주축 정렬 방식", "desc": ["【mainAxisAlignment】 : 주축 정렬   【MainAxisAlignment】"]}, {"file": "node_03.dart", "name": "Flex 교차축 정렬 방식", "desc": ["【crossAxisAlignment】 : 교차축 정렬   【CrossAxisAlignment】"]}, {"file": "node_04.dart", "name": "Flex 수직 방향 순서", "desc": ["【verticalDirection】 : 수직 방향 순서   【VerticalDirection】"]}, {"file": "node_05.dart", "name": "Flex 수평 방향 순서", "desc": ["【textDirection】 : 수평 방향 순서   【TextDirection】"]}]}