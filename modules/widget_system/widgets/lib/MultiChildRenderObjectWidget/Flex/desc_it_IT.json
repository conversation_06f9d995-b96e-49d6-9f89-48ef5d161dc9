{"id": 94, "name": "Flex", "localName": "Layout Flessibile", "info": "Classe genitore di Row e Column, il metodo di layout più potente in Flutter. Può contenere più componenti e può essere utilizzato con i componenti Spacer, Expended e Flexible per un layout flessibile", "lever": 5, "family": 3, "linkIds": [95, 96, 106, 107, 109], "nodes": [{"file": "node_01.dart", "name": "Direzione di disposizione di Flex", "desc": ["【children】 : Lista dei componenti   【List<Widget>】", "【direction】 : <PERSON><PERSON><PERSON>   【Axis】"]}, {"file": "node_02.dart", "name": "Allineamento dell'asse principale di Flex", "desc": ["【mainAxisAlignment】 : Allineamento dell'asse principale   【MainAxisAlignment】"]}, {"file": "node_03.dart", "name": "Allineamento dell'asse trasversale di Flex", "desc": ["【crossAxisAlignment】 : Allineamento dell'asse trasversale   【CrossAxisAlignment】"]}, {"file": "node_04.dart", "name": "Ordine della direzione verticale di Flex", "desc": ["【verticalDirection】 : Ordine della direzione verticale   【VerticalDirection】"]}, {"file": "node_05.dart", "name": "Ordine della direzione orizzontale di Flex", "desc": ["【textDirection】 : Ordine della direzione orizzontale   【TextDirection】"]}]}