{"id": 94, "name": "Flex", "localName": "Diseño Flexible", "info": "Clase padre de Row y Column, el método de diseño más potente en Flutter. Puede contener múltiples componentes y se puede usar con los componentes Spacer, Expended y Flexible para un diseño flexible.", "lever": 5, "family": 3, "linkIds": [95, 96, 106, 107, 109], "nodes": [{"file": "node_01.dart", "name": "Dirección de disposición de Flex", "desc": ["【children】 : Lista de componentes   【List<Widget>】", "【direction】 : Dirección   【Axis】"]}, {"file": "node_02.dart", "name": "Alineación del eje principal de Flex", "desc": ["【mainAxisAlignment】 : Alineación del eje principal   【MainAxisAlignment】"]}, {"file": "node_03.dart", "name": "Alineación del eje transversal de Flex", "desc": ["【crossAxisAlignment】 : Alineación del eje transversal   【CrossAxisAlignment】"]}, {"file": "node_04.dart", "name": "Orden de dirección vertical de Flex", "desc": ["【verticalDirection】 : Orden de dirección vertical   【VerticalDirection】"]}, {"file": "node_05.dart", "name": "Orden de dirección horizontal de Flex", "desc": ["【textDirection】 : Orden de dirección horizontal   【TextDirection】"]}]}