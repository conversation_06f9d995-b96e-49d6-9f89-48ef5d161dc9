{"id": 94, "name": "Flex", "localName": "Disposition flexible", "info": "Classe parente de Row et Column, la méthode de disposition la plus puissante dans Flutter. Peut contenir plusieurs composants, peut être utilisé avec les composants Spacer, Expended, Flexible pour une disposition flexible", "lever": 5, "family": 3, "linkIds": [95, 96, 106, 107, 109], "nodes": [{"file": "node_01.dart", "name": "Direction de disposition de Flex", "desc": ["【children】 : Liste des composants   【List<Widget>】", "【direction】 : Direction   【Axis】"]}, {"file": "node_02.dart", "name": "Alignement de l'axe principal de Flex", "desc": ["【mainAxisAlignment】 : Alignement de l'axe principal   【MainAxisAlignment】"]}, {"file": "node_03.dart", "name": "Alignement de l'axe transversal de Flex", "desc": ["【crossAxisAlignment】 : Alignement de l'axe transversal   【CrossAxisAlignment】"]}, {"file": "node_04.dart", "name": "Ordre de direction vertical de Flex", "desc": ["【verticalDirection】 : Ordre de direction vertical   【VerticalDirection】"]}, {"file": "node_05.dart", "name": "Ordre de direction horizontale de Flex", "desc": ["【textDirection】 : Ordre de direction horizontale   【TextDirection】"]}]}