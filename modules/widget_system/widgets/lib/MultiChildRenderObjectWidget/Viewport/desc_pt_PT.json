{"id": 340, "name": "Viewport", "localName": "Componente de Viewport", "info": "Normalmente usado para fornecer uma viewport para uma vista deslizante, construindo apenas as partes exibidas e pré-carregadas. Pode especificar o comprimento do pré-carregamento, o eixo de deslizamento, etc. É um dos componentes principais de implementação do ScrollView, geralmente não é usado diretamente.", "lever": 1, "family": 3, "linkIds": [253, 349], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do Viewport", "desc": ["【offset】 : *Deslocamento da viewport   【ViewportOffset】", "【cacheExtentStyle】: Tipo de pré-carregamento   【CacheExtentStyle】", "【cacheExtent】: Quantidade de pré-carregamento   【double】", "【axisDirection】: Direção de deslizamento   【AxisDirection】", "【slivers】: Conjunto de componentes Sliver filhos   【List<Widget>】", "【anchor】: <PERSON><PERSON> de ancoragem    【double】", "Pode executar este código para verificar a construção do ColorItem, as 128 barras de cores não são construídas todas de uma vez."]}]}