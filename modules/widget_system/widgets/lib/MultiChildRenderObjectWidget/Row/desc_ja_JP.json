{"id": 95, "name": "Row", "localName": "行レイアウト", "info": "レイアウト方向が横向きのFlexレイアウトで、複数のコンポーネントを収容できます。他の属性はすべて同じです。詳細はFlexを参照してください。", "lever": 4, "family": 3, "linkIds": [94, 96], "nodes": [{"file": "node1_base.dart", "name": "Rowの基本使用", "desc": ["【children】 : コンポーネントリスト   【List<Widget>】", "【mainAxisAlignment】 : 主軸の整列   【MainAxisAlignment】", "【crossAxisAlignment】 : 交差軸の整列   【CrossAxisAlignment】", "【textBaseline】 : テキストベースライン   【TextBaseline】", "【verticalDirection】 : 垂直方向   【VerticalDirection】", "【mainAxisSize】 : 主軸のサイズ   【MainAxisSize】"]}]}