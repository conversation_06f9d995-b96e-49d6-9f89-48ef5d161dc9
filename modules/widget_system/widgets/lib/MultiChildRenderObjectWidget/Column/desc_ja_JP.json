{"id": 96, "name": "Column", "localName": "列レイアウト", "info": "縦方向に配置するFlexレイアウトで、複数のコンポーネントを収容できます。他の属性はすべて同じです。詳細はFlexを参照してください。", "lever": 4, "family": 3, "linkIds": [94, 95], "nodes": [{"file": "node_01.dart", "name": "Columnの基本的な使用法", "desc": ["【children】 : コンポーネントリスト   【List<Widget>】", "【mainAxisAlignment】 : 主軸の整列   【MainAxisAlignment】", "【crossAxisAlignment】 : 交差軸の整列   【CrossAxisAlignment】", "【textBaseline】 : テキストベースライン   【TextBaseline】", "【verticalDirection】 : 垂直方向   【VerticalDirection】", "【mainAxisSize】 : 主軸サイズ   【MainAxisSize】"]}]}