{"id": 96, "name": "Column", "localName": "列布局", "info": "排布方向为竖向的Flex布局，可容纳多个组件。其他属性全部一致，详见Flex。", "lever": 4, "family": 3, "linkIds": [94, 95], "nodes": [{"file": "node_01.dart", "name": "Column基本使用", "desc": ["【children】 : 组件列表   【List<Widget>】", "【mainAxisAlignment】 : 主轴对齐   【MainAxisAlignment】", "【crossAxisAlignment】 : 交叉轴对齐   【CrossAxisAlignment】", "【textBaseline】 : 文字基线   【TextBaseline】", "【verticalDirection】 : 竖直方向   【VerticalDirection】", "【mainAxisSize】 : 主轴尺寸   【MainAxisSize】"]}]}