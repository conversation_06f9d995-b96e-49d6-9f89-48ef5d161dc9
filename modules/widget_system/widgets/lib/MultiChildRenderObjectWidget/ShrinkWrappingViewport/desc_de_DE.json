{"id": 343, "name": "ShrinkWrappingViewport", "localName": "Schrumpfende Umhüllungsansicht", "info": "Beziehung zur shrinkWrap-Eigenschaft von ScrollView. Die ShrinkWrappingViewport passt ihre Größe auf der Hauptachse an ihre untergeordneten Knoten an und wird verwendet, wenn keine Randbeschränkungen vorhanden sind.", "lever": 1, "family": 3, "linkIds": [349, 162, 163], "nodes": [{"file": "node1_base.dart", "name": "Einführung in NestedScrollViewViewport", "desc": ["【offset】 : *Versatz   【ViewportOffset】", "【axisDirection】 : Achsenrichtung   【AxisDirection】", "【crossAxisDirection】 : Kreuzachsenrichtung   【AxisDirection】", "【slivers】 : Untergeordnete Komponenten   【List<Widget>】", "【clipBehavior】 : Beschneidungsverhalten   【Clip】"]}]}