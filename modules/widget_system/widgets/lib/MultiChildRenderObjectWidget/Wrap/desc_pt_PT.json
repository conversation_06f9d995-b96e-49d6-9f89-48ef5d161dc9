{"id": 98, "name": "Wrap", "localName": "Layout de Embalagem", "info": "Pode acomodar vários componentes, dispostos sequencialmente na direção especificada, e pode facilmente lidar com o espaçamento entre os filhos. Quando ultrapassa o limite, pode quebrar automaticamente a linha. Possui métodos de alinhamento para o eixo principal e o eixo cruzado, sendo bastante flexível.", "lever": 5, "family": 3, "linkIds": [94, 95], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do Wrap", "desc": ["【children】 : Lista de componentes   【List<Widget>】", "【spacing】 : Espaçamento entre itens no eixo principal   【double】", "【runSpacing】 : Espaçamento entre itens no eixo cruzado   【double】", "【direction】 : Alinhamento do eixo principal   【Axis】"]}, {"file": "node2_alignment.dart", "name": "Propriedade alignment do Wrap", "desc": ["【alignment】 : Alinhamento do eixo principal   【WrapAlignment】"]}, {"file": "node3_crossAxisAlignment.dart", "name": "Propriedade crossAxisAlignment do Wrap", "desc": ["【crossAxisAlignment】 : Alinhamento do eixo cruzado   【CrossAxisAlignment】"]}, {"file": "node4_textDirection.dart", "name": "Propriedade textDirection do Wrap", "desc": ["【textDirection】 : Direção do texto   【TextDirection】"]}, {"file": "node5_verticalDirection.dart", "name": "Propriedade verticalDirection do Wrap", "desc": ["【verticalDirection】 : Direção vertical  【VerticalDirection】"]}]}