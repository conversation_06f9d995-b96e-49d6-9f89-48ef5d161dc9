{"id": 98, "name": "Wrap", "localName": "래핑 레이아웃", "info": "여러 컴포넌트를 수용할 수 있으며, 지정된 방향으로 순차적으로 배치할 수 있습니다. 자식 간의 간격을 쉽게 처리할 수 있으며, 경계를 벗어날 경우 자동으로 줄 바꿈이 가능합니다. 주축과 교차축 정렬 방식을 가지고 있어 유연성이 높습니다.", "lever": 5, "family": 3, "linkIds": [94, 95], "nodes": [{"file": "node1_base.dart", "name": "Wrap의 기본 사용법", "desc": ["【children】 : 컴포넌트 리스트   【List<Widget>】", "【spacing】 : 주축 항목 간격   【double】", "【runSpacing】 : 교차축 항목 간격   【double】", "【direction】 : 주축 정렬   【Axis】"]}, {"file": "node2_alignment.dart", "name": "Wrap의 alignment 속성", "desc": ["【alignment】 : 주축 정렬   【WrapAlignment】"]}, {"file": "node3_crossAxisAlignment.dart", "name": "Wrap의 crossAxisAlignment 속성", "desc": ["【crossAxisAlignment】 : 교차축 정렬   【CrossAxisAlignment】"]}, {"file": "node4_textDirection.dart", "name": "Wrap의 textDirection 속성", "desc": ["【textDirection】 : 텍스트 방향   【TextDirection】"]}, {"file": "node5_verticalDirection.dart", "name": "Wrap의 verticalDirection 속성", "desc": ["【verticalDirection】 : 수직 방향  【VerticalDirection】"]}]}