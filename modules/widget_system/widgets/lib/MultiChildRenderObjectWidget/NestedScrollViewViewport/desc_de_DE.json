{"id": 344, "name": "NestedScrollViewViewport", "localName": "Verschachtelter Scroll-Viewport", "info": "Ein in NestedScrollView verwendeter Viewport, der ein SliverOverlapAbsorberHandle enthält und es benachrichtigt, wenn der Viewport sein Layout neu berechnen muss. Zum Beispiel, wenn es gescrollt wird.", "lever": 1, "family": 3, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Einführung in NestedScrollViewViewport", "desc": ["【offset】 : *Versatz   【ViewportOffset】", "【handle】 : *Handler   【SliverOverlapAbsorberHandle】", "【axisDirection】 : Achsenrichtung   【AxisDirection】", "【crossAxisDirection】 : Querachsenrichtung   【AxisDirection】", "【slivers】 : Unterkomponenten   【List<Widget>】", "【clipBehavior】 : Abschneideverhalten   【Clip】", "【anchor】 : <PERSON><PERSON><PERSON><PERSON><PERSON>   【double】"]}]}