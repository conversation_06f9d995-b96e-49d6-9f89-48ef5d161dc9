{"id": 344, "name": "NestedScrollViewViewport", "localName": "Vista scorrevole annidata", "info": "Vista utilizzata in NestedScrollView, che detiene SliverOverlapAbsorberHandle e lo notifica quando la vista necessita di ricalcolare il layout. Ad esempio, quando viene scorsa.", "lever": 1, "family": 3, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introduzione a NestedScrollViewViewport", "desc": ["【offset】 : *offset   【ViewportOffset】", "【handle】 : *gestore   【SliverOverlapAbsorberHandle】", "【axisDirection】 : direzione dell'asse   【AxisDirection】", "【crossAxisDirection】 : direzione dell'asse incrociato   【AxisDirection】", "【slivers】 : componenti figli   【List<Widget>】", "【clipBehavior】 : comportamento di ritaglio   【Clip】", "【anchor】 : punto di ancoraggio   【double】"]}]}