{"id": 344, "name": "NestedScrollViewViewport", "localName": "Nested Scroll Viewport", "info": "A viewport used in NestedScrollView, which holds a SliverOverlapAbsorberHandle and notifies it when the viewport needs to recalculate its layout. For example, when scrolling it.", "lever": 1, "family": 3, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introduction to NestedScrollViewViewport", "desc": ["【offset】 : *Offset   【ViewportOffset】", "【handle】 : *Handle   【SliverOverlapAbsorberHandle】", "【axisDirection】 : Axis Direction   【AxisDirection】", "【crossAxisDirection】 : Cross Axis Direction   【AxisDirection】", "【slivers】 : Child Components   【List<Widget>】", "【clipBehavior】 : C<PERSON> Behavior   【Clip】", "【anchor】 : <PERSON><PERSON>   【double】"]}]}