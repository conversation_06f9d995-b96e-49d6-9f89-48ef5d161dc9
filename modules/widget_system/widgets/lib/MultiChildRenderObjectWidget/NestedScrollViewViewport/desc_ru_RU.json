{"id": 344, "name": "NestedScrollViewViewport", "localName": "Вложенный скроллируемый вид", "info": "Вид, используемый в NestedScrollView, который содержит SliverOverlapAbsorberHandle и уведомляет его, когда вид требует пересчета макета. Например, при его прокрутке.", "lever": 1, "family": 3, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Описание NestedScrollViewViewport", "desc": ["【offset】 : *смещение   【ViewportOffset】", "【handle】 : *обработчик   【SliverOverlapAbsorberHandle】", "【axisDirection】 : направление оси   【AxisDirection】", "【crossAxisDirection】 : направление поперечной оси   【AxisDirection】", "【slivers】 : дочерние компоненты   【List<Widget>】", "【clipBehavior】 : поведение обрезки   【Clip】", "【anchor】 : якорь   【double】"]}]}