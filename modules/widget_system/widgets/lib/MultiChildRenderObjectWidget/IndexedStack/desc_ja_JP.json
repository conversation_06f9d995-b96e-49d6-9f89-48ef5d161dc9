{"id": 161, "name": "IndexedStack", "localName": "インデックススタック", "info": "Stackコンポーネントのサブクラスで、複数のコンポーネントをスタックし、indexを指定して表示するコンポーネントを指定できます。それ以外のコンポーネントは非表示になります。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "IndexedStackの基本使用", "desc": ["【children】 : 子コンポーネントリスト   【Lis<Widget>】", "【alignment】 : 配置方法   【AlignmentGeometry】", "【index】 : 現在表示されているコンポーネント  【int】"]}]}