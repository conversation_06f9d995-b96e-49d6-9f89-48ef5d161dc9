{"id": 97, "name": "<PERSON><PERSON>", "localName": "Стек", "info": "Может содержать несколько компонентов, располагая дочерние компоненты в стеке, последний сверху. Имеет свойство alignment, которое можно использовать вместе с компонентом Positioned для точного позиционирования.", "lever": 5, "family": 3, "linkIds": [94, 95, 161], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Stack", "desc": ["【children】: С<PERSON>и<PERSON><PERSON><PERSON> компонентов   【List<Widget>】", "【textDirection】: Направление расположения детей   【MainAxisAlignment】", "【alignment】: Способ выравнивания   【AlignmentGeometry】", "【overflow】: Режим переполнения   【Overflow】", "【fit】: Режим адаптации   【StackFit】"]}, {"file": "node2_positioned.dart", "name": "Использование Stack и Positioned вместе", "desc": ["Компонент Positioned может использоваться только в Stack, позволяя точно размещать компонент, задавая расстояния сверху, слева, справа и снизу."]}]}