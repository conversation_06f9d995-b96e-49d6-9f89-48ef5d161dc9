{"id": 97, "name": "<PERSON><PERSON>", "localName": "스택 레이아웃", "info": "여러 컴포넌트를 수용할 수 있으며, 자식 컴포넌트를 겹쳐서 배치합니다. 후자가 위에 위치합니다. alignment 속성을 가지고 있으며, Positioned 컴포넌트와 함께 사용하여 정확한 위치를 지정할 수 있습니다.", "lever": 5, "family": 3, "linkIds": [94, 95, 161], "nodes": [{"file": "node1_base.dart", "name": "Stack 기본 사용법", "desc": ["【children】 : 컴포넌트 리스트   【List<Widget>】", "【textDirection】 : 자식 배치 방향   【MainAxisAlignment】", "【alignment】 : 정렬 방식   【AlignmentGeometry】", "【overflow】 : 오버플로우 모드   【Overflow】", "【fit】 : 적응 모드   【StackFit】"]}, {"file": "node2_positioned.dart", "name": "Stack과 Positioned 함께 사용", "desc": ["Positioned 컴포넌트는 Stack에서만 사용할 수 있으며, 특정 컴포넌트의 위치를 정확히 배치하기 위해 상하좌우 거리를 지정할 수 있습니다."]}]}