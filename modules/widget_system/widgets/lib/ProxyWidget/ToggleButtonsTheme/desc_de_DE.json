{"id": 332, "name": "ToggleButtonsTheme", "localName": "Schaltflächenstil", "info": "Wird hauptsächlich verwendet, um Standardattribute für nachfolgende ToggleButtons-Komponenten festzulegen. Es kann auch verwendet werden, um die Standardattribute von ToggleButtons abzurufen.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>ButtonsTheme", "desc": ["Mit ToggleButtonsThemeData können Standardstile wie Rahmenstil, Farbe, Dekoration usw. für nachfolgende ToggleButtons-Komponenten festgelegt werden. Mit ToggleButtonsTheme.of können auch die Themendaten von ToggleButtons abgerufen werden."]}]}