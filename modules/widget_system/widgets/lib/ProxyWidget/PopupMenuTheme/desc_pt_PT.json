{"id": 330, "name": "PopupMenuTheme", "localName": "Tema do Menu Pop-up", "info": "Principalmente utilizado para definir propriedades padrão uniformes para os componentes PopupMenuButton descendentes, também é possível obter as propriedades padrão do PopupMenu através deste componente.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do PopupMenuTheme", "desc": ["Pode especificar as propriedades de dados do PopupMenuThemeData para definir o estilo padrão para os componentes PopupMenuButton descendentes, como forma, profundidade de sombra, cor, estilo de texto, etc. Também é possível usar PopupMenuTheme.of para obter os dados do tema do PopupMenu."]}]}