{"id": 330, "name": "PopupMenuTheme", "localName": "Тема всплывающего меню", "info": "Основное использование для установки стандартных свойств для компонентов PopupMenuButton потомков, также можно получить свойства стандартного PopupMenu через этот компонент.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование PopupMenuTheme", "desc": ["Можно указать атрибуты данных PopupMenuThemeData для установки стандартных стилей для компонентов PopupMenuButton потомков, таких как форма, глубина тени, цвет, стиль текста и т.д. Также можно использовать PopupMenuTheme.of для получения данных темы PopupMenu."]}]}