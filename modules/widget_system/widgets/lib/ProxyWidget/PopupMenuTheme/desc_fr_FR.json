{"id": 330, "name": "PopupMenuTheme", "localName": "Thème du menu contextuel", "info": "Principalement utilisé pour définir des propriétés par défaut unifiées pour les composants PopupMenuButton descendants. Il est également possible d'obtenir les propriétés par défaut de PopupMenu via ce composant.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de PopupMenuTheme", "desc": ["Vous pouvez spécifier les propriétés de données de PopupMenuThemeData pour définir des styles par défaut pour les composants PopupMenuButton descendants, tels que la forme, la profondeur de l'ombre, la couleur, le style de texte, etc. Vous pouvez également utiliser PopupMenuTheme.of pour obtenir les données de thème de PopupMenu."]}]}