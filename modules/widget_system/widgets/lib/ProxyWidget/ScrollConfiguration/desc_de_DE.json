{"id": 180, "name": "ScrollConfiguration", "localName": "iOS-Menüschaltfläche", "info": "Muss eine scrollbare Komponente umschließen und durch das Verhalten-Attribut den Scroll-Effekt steuern, kann den blauen Schatten beim Scrollen entfernen usw.", "lever": 3, "family": 5, "linkIds": [162, 163, 164], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von ScrollConfiguration", "desc": ["【child】: Untergeordnete Komponente 【Widget】", "【behavior】: Scrollverhalten 【ScrollBehavior】", "    <PERSON>nn <PERSON>Configuration verwenden, um den blauen Schatten in ListView zu entfernen"]}]}