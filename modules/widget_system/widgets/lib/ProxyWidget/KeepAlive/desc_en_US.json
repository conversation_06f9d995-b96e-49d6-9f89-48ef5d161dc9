{"id": 316, "name": "KeepAlive", "localName": "Keep Alive", "info": "Whether the state of the child needs to be kept alive in a lazy-loaded list. It is the underlying implementation of AutomaticKeepAlive and is generally not used alone.", "lever": 1, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introduction to KeepAlive", "desc": ["【child】: *Child component   【Widget】", "【keepAlive】: *Whether to keep alive   【bool】", "In the Flutter framework layer, it is only used in AutomaticKeepAlive. The source code also mentions that it is rarely used alone. This example demonstrates the state keep-alive of ListView items."]}]}