{"id": 316, "name": "KeepAlive", "localName": "Maintenir en vie", "info": "Dans une liste chargée de manière paresseuse, faut-il maintenir l'état de l'enfant en vie. C'est l'implémentation sous-jacente de AutomaticKeepAlive, généralement pas utilisé seul.", "lever": 1, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introduction à KeepAlive", "desc": ["【child】 : *composant enfant   【Widget】", "【keepAlive】 : *maintenir en vie   【bool】", "Dans la couche framework de Flutter, il est uniquement utilisé dans AutomaticKeepAlive, et le code source indique qu'il est rarement utilisé seul. Cet exemple montre la maintenance de l'état des éléments de ListView."]}]}