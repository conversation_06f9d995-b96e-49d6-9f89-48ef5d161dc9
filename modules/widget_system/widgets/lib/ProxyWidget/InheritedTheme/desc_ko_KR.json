{"id": 345, "name": "InheritedTheme", "localName": "전통 테마", "info": "추상 클래스이며, 많은 XXXTheme 관련 하위 클래스가 있어 색상, 텍스트 스타일 등의 속성을 정의하고 하위 트리에서 이러한 속성을 공유합니다.", "lever": 1, "family": 5, "linkIds": [324, 326, 328, 329], "nodes": [{"file": "node1_base.dart", "name": "InheritedTheme 소개", "desc": ["InheritedTheme.capture는 상위 테마를 캡처하여 CapturedThemes 객체를 가져올 수 있으며, 이 객체의 wrap 메서드를 통해 캡처한 테마를 라우터 간에 사용할 수 있습니다.", "    ", "", "class InheritedThemeDemo extends StatelessWidget {", "  const InheritedThemeDemo({Key? key) : super(key: key);", "", "", "  @override", "  Widget build(BuildContext context) {", "    return const DefaultTextStyle(", "      style: TextStyle(fontSize: 24, color: Colors.blue),", "      child: <PERSON><PERSON><PERSON>(),", "    );", "  ", "", "", "class TestBody extends StatelessWidget {", "  const TestBody({Key? key) : super(key: key);", "", "  @override", "  Widget build(BuildContext context) {", "", "    return GestureDetector(", "        onTap: () => _toNextPage(context),", "        child: Container(", "            height: 60,", "            margin: const EdgeInsets.only(left: 40,right: 40),", "            alignment: Alignment.center,", "            color: Theme.of(context).primaryColor.withOpacity(0.1),", "            child: const Text('InheritedTheme')));", "  ", "", "  void _toNextPage(BuildContext context) {", "     final NavigatorState navigator = Navigator.of(context);", "     final CapturedThemes themes =", "     InheritedTheme.capture(from: context, to: navigator.context);", "    ", "     Navigator.of(context).push(", "       MaterialPageRoute(", "         builder: (BuildContext _) {", "           return themes.wrap(Container(", "             alignment: Alignment.center,", "             color: Colors.white,", "             child: Text('Flutter Unit'),"]}]}