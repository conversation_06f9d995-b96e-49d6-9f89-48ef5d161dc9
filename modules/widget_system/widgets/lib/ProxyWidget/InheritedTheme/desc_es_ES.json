{"id": 345, "name": "InheritedTheme", "localName": "<PERSON><PERSON>", "info": "Es una clase abstracta con muchas subclases relacionadas con XXXTheme, utilizadas para definir propiedades como colores, estilos de texto, etc., y compartir estas propiedades en el subárbol.", "lever": 1, "family": 5, "linkIds": [324, 326, 328, 329], "nodes": [{"file": "node1_base.dart", "name": "Introducción a InheritedTheme", "desc": ["InheritedTheme.capture puede capturar el tema superior y obtener un objeto CapturedThemes, a través del cual el método wrap puede usar el tema capturado entre rutas.", "    ", "", "class InheritedThemeDemo extends StatelessWidget {", "  const InheritedThemeDemo({Key? key) : super(key: key);", "", "", "  @override", "  Widget build(BuildContext context) {", "    return const DefaultTextStyle(", "      style: TextStyle(fontSize: 24, color: Colors.blue),", "      child: <PERSON><PERSON><PERSON>(),", "    );", "  ", "", "", "class TestBody extends StatelessWidget {", "  const TestBody({Key? key) : super(key: key);", "", "  @override", "  Widget build(BuildContext context) {", "", "    return GestureDetector(", "        onTap: () => _toNextPage(context),", "        child: Container(", "            height: 60,", "            margin: const EdgeInsets.only(left: 40,right: 40),", "            alignment: Alignment.center,", "            color: Theme.of(context).primaryColor.withOpacity(0.1),", "            child: const Text('InheritedTheme')));", "  ", "", "  void _toNextPage(BuildContext context) {", "     final NavigatorState navigator = Navigator.of(context);", "     final CapturedThemes themes =", "     InheritedTheme.capture(from: context, to: navigator.context);", "    ", "     Navigator.of(context).push(", "       MaterialPageRoute(", "         builder: (BuildContext _) {", "           return themes.wrap(Container(", "             alignment: Alignment.center,", "             color: Colors.white,", "             child: Text('Flutter Unit'),"]}]}