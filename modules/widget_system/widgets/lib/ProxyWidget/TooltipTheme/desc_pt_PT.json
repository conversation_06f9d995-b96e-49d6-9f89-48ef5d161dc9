{"id": 333, "name": "TooltipTheme", "localName": "Tema de Dica", "info": "Principalmente utilizado para definir propriedades padrão unificadas para componentes Tooltip descendentes, também é possível obter as propriedades do TooltipTheme padrão através deste componente.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do TooltipTheme", "desc": ["Pode especificar as propriedades de dados do TooltipThemeData para definir o estilo padrão para componentes Tooltip descendentes, como decoração, estilo de texto, duração de exibição, margens, etc. Também é possível usar TooltipTheme.of para obter as propriedades temáticas do Tooltip."]}]}