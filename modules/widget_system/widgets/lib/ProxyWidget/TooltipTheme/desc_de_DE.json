{"id": 333, "name": "TooltipTheme", "localName": "Tooltip-Thema", "info": "Wird hauptsäch<PERSON> verwendet, um Standardattribute für nachfolgende Tooltip-Komponenten festzulegen. Es kann auch verwendet werden, um die Attribute des standardmäßigen Tooltip-Themas abzurufen.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von Tooltip<PERSON>heme", "desc": ["Kann die TooltipThemeData-Datenattribute festlegen, um Standardstile für nachfolgende Tooltip-Komponenten zu definieren, wie Dekoration, Textstil, Anzeigedauer, Randabstand usw. Es kann auch TooltipTheme.of verwenden, um die Themenattribute des Tooltips abzurufen."]}]}