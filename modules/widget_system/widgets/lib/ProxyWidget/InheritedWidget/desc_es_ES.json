{"id": 346, "name": "InheritedWidget", "localName": "Componente Heredado", "info": "Esta clase es una clase abstracta que permite almacenar datos en este contexto y compartirlos en los contextos de sus nodos subsiguientes. Tiene muchas clases de implementación, incluyendo varios componentes temáticos, MediaQuery, etc.", "lever": 4, "family": 5, "linkIds": [167, 319, 328, 324, 331], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de Column", "desc": ["【children】 : Lista de componentes   【List<Widget>】", "【mainAxisAlignment】 : Alineación del eje principal   【MainAxisAlignment】", "【crossAxisAlignment】 : Alineación del eje transversal   【CrossAxisAlignment】", "【textBaseline】 : L<PERSON>ea base del texto   【TextBaseline】", "【verticalDirection】 : Dirección vertical   【VerticalDirection】", "【mainAxisSize】 : <PERSON><PERSON><PERSON> del eje principal   【MainAxisSize】"]}, {"file": "node2_use.dart", "name": "<PERSON><PERSON><PERSON><PERSON> de InheritedWidget", "desc": ["A continuación se muestra un proyecto de contador de colores que demuestra el uso del componente InheritedWidget."]}]}