{"id": 346, "name": "InheritedWidget", "localName": "Componente Herdado", "info": "Esta classe é abstrata e tem a função de armazenar dados no contexto atual e compartilhar esses dados nos contextos dos nós subsequentes. Existem muitas classes de implementação, incluindo vários componentes temáticos, MediaQuery, etc.", "lever": 4, "family": 5, "linkIds": [167, 319, 328, 324, 331], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do Column", "desc": ["【children】 : Lista de componentes   【List<Widget>】", "【mainAxisAlignment】 : Alinhamento do eixo principal   【MainAxisAlignment】", "【crossAxisAlignment】 : Alinhamento do eixo cruzado   【CrossAxisAlignment】", "【textBaseline】 : <PERSON><PERSON> de base do texto   【TextBaseline】", "【verticalDirection】 : Direção vertical   【VerticalDirection】", "【mainAxisSize】 : <PERSON><PERSON><PERSON> do eixo principal   【MainAxisSize】"]}, {"file": "node2_use.dart", "name": "Exemplo de InheritedWidget", "desc": ["Abaixo está um projeto de contador de cores que demonstra o uso do componente InheritedWidget."]}]}