{"id": 109, "name": "Flexible", "localName": "Componente Flessibile", "info": "<PERSON><PERSON>ò essere utilizzato solo nei layout Row, Column e Flex, può annidare figli per espandere lo spazio rimanente, e può anche specificare il tipo di adattamento.", "lever": 3, "family": 5, "linkIds": [94, 106], "nodes": [{"file": "node1_base.dart", "name": "Uso Base di Flexible", "desc": ["【child】 : <PERSON><PERSON><PERSON>   【Widget】", "【fit】 : Modalità di adattamento*2   【FlexFit】", "【flex】 : Percentuale di allocazione dello spazio rimanente   【int】"]}]}