{"id": 109, "name": "Flexible", "localName": "Гибкий компонент", "info": "Может использоваться только в макетах Row, Column и Flex, может вкладывать дочерние элементы для расширения занимаемого пространства с использованием оставшегося пространства, также можно указать тип адаптации.", "lever": 3, "family": 5, "linkIds": [94, 106], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Flexible", "desc": ["【child】 : До<PERSON><PERSON><PERSON><PERSON>ий элемент   【Widget】", "【fit】 : Режим адаптации*2   【FlexFit】", "【flex】 : Доля распределения оставшегося пространства   【int】"]}]}