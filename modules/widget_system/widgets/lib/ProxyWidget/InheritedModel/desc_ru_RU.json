{"id": 336, "name": "InheritedModel", "localName": "Общая модель", "info": "Это абстрактный подкласс InheritedWidget, который передает данные в поддерево через context и управляет гранулярностью уведомлений о зависимостях через аспект Aspect.", "lever": 4, "family": 5, "linkIds": [346], "nodes": [{"file": "node1.dart", "name": "Использование InheritedModel", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "Ниже приведен пример пользовательского InheritedModel, который реализует обмен данными в поддереве; а также определяет два аспекта: цвет и значение, чтобы управлять гранулярностью уведомлений о зависимостях."]}]}