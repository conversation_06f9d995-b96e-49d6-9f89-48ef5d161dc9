{"id": 334, "name": "ListTileTheme", "localName": "ListTile 테마", "info": "주로 후손 ListTile 컴포넌트에 대한 기본 속성을 통일적으로 설정하는 데 사용되며, 이 컴포넌트를 통해 기본 ListTile의 속성을 얻을 수도 있습니다.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "ListTileTheme 기본 사용", "desc": ["ListTileThemeData 데이터 속성을 지정하여 【후손】 ListTile 컴포넌트에 대한 기본 스타일(스타일, 색상, 장식, 여백 등)을 설정할 수 있습니다. ListTileTheme.of를 사용하여 ListTile의 테마 속성을 얻을 수도 있습니다."]}]}