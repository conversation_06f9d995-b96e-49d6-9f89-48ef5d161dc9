{"id": 319, "name": "Directionality", "localName": "Directionality", "info": "Set the property value uniformly for components with the textDirection attribute for descendants, or get the default textDirection attribute through Directionality.of(context).", "lever": 2, "family": 5, "linkIds": [2, 94], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Directionality", "desc": ["【textDirection】: Text arrangement direction   【TextDirection】", "【child】: Child component   【Widget】"]}]}