{"id": 328, "name": "ChipTheme", "localName": "칩 스타일", "info": "주로 후손의 Chip 타입 컴포넌트에 기본 속성을 통일적으로 설정하기 위해 사용되며, 이 컴포넌트를 통해 기본 Chip의 속성을 가져올 수도 있습니다.", "lever": 3, "family": 5, "linkIds": [11, 153, 12, 13, 14, 15], "nodes": [{"file": "node1_base.dart", "name": "ChipTheme 기본 사용", "desc": ["ChipThemeData 데이터 속성을 지정하여 【후손】의 Chip 타입 컴포넌트에 기본 스타일을 설정할 수 있습니다. 속성은 Chip 속성과 유사하며, 그림자, 색상, 여백, 모양, 텍스트 스타일 등이 포함됩니다. 또한 ChipTheme.of를 사용하여 Chip의 테마 데이터를 가져올 수도 있습니다."]}]}