{"id": 108, "name": "Positioned", "localName": "Componente de posicionamiento", "info": "Solo se puede usar en Stack, permite especificar las distancias superior, i<PERSON><PERSON><PERSON>a, derecha e inferior para colocar un componente con precisión.", "lever": 3, "family": 5, "linkIds": [97, 159, 121], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de Positioned", "desc": ["【child】 : Componente   【Widget】", "【top】 : Distancia al borde superior del padre   【double】", "【right】 : <PERSON><PERSON><PERSON> al borde derecho del padre   【double】", "【left】 : Distancia al borde izquierdo del padre   【double】", "【bottom】 : Distancia al borde inferior del padre   【double】"]}]}