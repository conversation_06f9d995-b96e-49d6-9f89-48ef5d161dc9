{"id": 325, "name": "IconTheme", "localName": "Icon Style", "info": "Can accommodate one child, specifying the default style for the descendant icons. Commonly used to unify the styles of multiple identical icons, avoiding setting them one by one.", "lever": 3, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "IconTheme Usage", "desc": ["You can obtain the icon theme data through IconTheme.of, or set the default style for the icon components of IconTheme【descendants】, including color, opacity, and size."]}]}