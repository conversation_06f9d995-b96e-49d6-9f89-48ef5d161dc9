{"id": 335, "name": "PrimaryScrollController", "localName": "Contrôleur de défilement initial", "info": "C'est une sous-classe d'InheritedWidget qui fournit un objet ScrollController par défaut aux vues défilables dans le sous-arbre via le contexte.", "lever": 1, "family": 5, "linkIds": [349, 344, 164], "nodes": [{"file": "node1_base.dart", "name": "Introduction de PrimaryScrollController", "desc": ["【controller】 : Contr<PERSON><PERSON>ur de défilement   【ScrollController】", "【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】"]}]}