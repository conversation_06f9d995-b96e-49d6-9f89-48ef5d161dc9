{"id": 335, "name": "PrimaryScrollController", "localName": "Controlador de Desplazamiento Primario", "info": "Es una subclase de InheritedWidget que proporciona un objeto ScrollController predeterminado a las vistas desplazables en el subárbol a través del contexto.", "lever": 1, "family": 5, "linkIds": [349, 344, 164], "nodes": [{"file": "node1_base.dart", "name": "Introducción a PrimaryScrollController", "desc": ["【controller】 : Controlador de desplazamiento   【ScrollController】", "【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】"]}]}