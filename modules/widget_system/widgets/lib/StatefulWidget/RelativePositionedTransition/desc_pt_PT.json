{"id": 115, "name": "RelativePositionedTransition", "localName": "Transição de Posição Retangular", "info": "Subclasse de AnimatedWidget, usa um animador do tipo Rect para permitir que os componentes filhos façam uma animação de transição entre dois objetos Rect.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico de RelativePositionedTransition", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【size】 : Deslocamento esquerdo e superior   【Size】", "【rect】 : Animação   【Animation<Rect>】", "    O componente PositionedTransition só funciona dentro de uma Stack"]}]}