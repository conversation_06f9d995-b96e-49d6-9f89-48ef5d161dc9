{"id": 173, "name": "StreamBuilder", "localName": "Потоковый конструктор", "info": "Можно указать объект stream, который может отслеживать состояние асинхронного выполнения и строить различные интерфейсы в конструкторе в зависимости от состояния.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование StreamBuilder", "desc": ["【stream】 : Дочерний компонент   【Stream<T>】", "【initialData】 : Начальные данные   【T】", "【builder】 : Событи<PERSON> клика  【AsyncWidgetBuilder<T>】"]}]}