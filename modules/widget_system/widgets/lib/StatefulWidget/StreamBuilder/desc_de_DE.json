{"id": 173, "name": "StreamBuilder", "localName": "Stream-Builder", "info": "Kann ein Stream-Objekt angeben, kann den Status der asynchronen Ausführung überwachen und im Builder verschiedene Benutzeroberflächen basierend auf dem Status erstellen.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von StreamBuilder", "desc": ["【stream】 : Unterkomponente   【Stream<T>】", "【initialData】 : Initiale Daten   【T】", "【builder】 : Klickereignis  【AsyncWidgetBuilder<T>】"]}]}