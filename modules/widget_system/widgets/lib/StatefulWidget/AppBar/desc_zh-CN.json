{"id": 57, "name": "AppBar", "localName": "应用头栏", "info": "一个应用顶部栏的通用结构，可在指定的部位放置相应的组件，常用于Scaffold组件中。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AppBar基本使用", "desc": ["【leading】 : 左侧组件   【Widget】", "【title】 : 中间组件   【Widget】", "【actions】 : 右侧组件   【List<Widget>】", "【elevation】 : 影深   【double】", "【shape】 : 形状   【ShapeBorder】", "【backgroundColor】 : 影深   【背景色】", "【centerTitle】 : 中间是否居中   【bool】"]}, {"file": "node2_tab.dart", "name": "AppBar与TabBar、TabBarView联用", "desc": ["【bottom】 : 底部组件   【PreferredSizeWidget】"]}]}