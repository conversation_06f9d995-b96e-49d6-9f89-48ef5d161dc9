{"id": 200, "name": "Stepper", "localName": "Componente de Passos", "info": "Componente de passos, que permite especificar operações passo a passo, pode personalizar o conteúdo dos passos, os botões de confirmação e retorno, bem como a direção do arranjo dos passos.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do Stepper", "desc": ["【steps】 : Lista de passos   【List<Step>】", "【currentStep】 : <PERSON><PERSON> atual   【double】", "【onStepTapped】 : Callback de clique   【ValueChanged<int>】", "【onStepCancel】 : Callback de passo anterior  【VoidCallback】", "【controlsBuilder】 : Construtor de controlador  【ControlsWidgetBuilder】"]}, {"file": "node2_type.dart", "name": "Direção do Stepper", "desc": ["【type】 : Direção   【StepperType】"]}]}