{"id": 124, "name": "AnimatedDefaultTextStyle", "localName": "Animation de conteneur", "info": "Permet aux composants texte enfants d'effectuer une animation TextStyle (style de texte), peut spécifier la durée et la courbe, et dispose d'un événement de fin d'animation.", "lever": 3, "family": 1, "linkIds": [114, 324], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base d'AnimatedDefaultTextStyle", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【duration】 : Du<PERSON>e de l'animation   【Duration】", "【onEnd】 : Rappel de fin d'animation   【Function()】", "【curve】 : Courbe d'animation   【Duration】", "【textAlign】 : Alignement du texte  【TextAlign】", "【softWrap】 : Encapsulage  【bool】", "【maxLines】 : Nombre maximum de lignes  【int】", "【overflow】 : Mode de débordement  【TextOverflow】", "【style】 : Style de texte   【TextStyle】"]}]}