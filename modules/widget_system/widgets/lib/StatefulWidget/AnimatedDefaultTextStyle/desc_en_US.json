{"id": 124, "name": "AnimatedDefaultTextStyle", "localName": "Container Animation", "info": "Allows child text components to perform TextStyle (text style) animations, with specified duration and curve, and has an animation end event.", "lever": 3, "family": 1, "linkIds": [114, 324], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of AnimatedDefaultTextStyle", "desc": ["【child】: Child component 【Widget】", "【duration】: Animation duration 【Duration】", "【onEnd】: Animation end callback 【Function()】", "【curve】: Animation curve 【Duration】", "【textAlign】: Text alignment 【TextAlign】", "【softWrap】: Whether to wrap 【bool】", "【maxLines】: Maximum number of lines 【int】", "【overflow】: Overflow mode 【TextOverflow】", "【style】: Text style 【TextStyle】"]}]}