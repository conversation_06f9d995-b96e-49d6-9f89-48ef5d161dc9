{"id": 45, "name": "Radio", "localName": "Botón de selección", "info": "Debido a los botones circulares en estados seleccionados y no seleccionados, múltiples Radio pueden satisfacer necesidades de selección única o múltiple según la lógica. Se puede especificar el color y recibir una devolución de llamada para cambios de estado.", "lever": 4, "family": 1, "linkIds": [19, 240], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de Radio", "desc": ["【value】 : <PERSON>or del botón de selección   【T】", "【groupValue】 : Valor de coincidencia actual   【T】", "【activeColor】 : Color activo   【Color】", "【onChanged】 : Devolución de llamada al cambiar   【Function(T)】"]}]}