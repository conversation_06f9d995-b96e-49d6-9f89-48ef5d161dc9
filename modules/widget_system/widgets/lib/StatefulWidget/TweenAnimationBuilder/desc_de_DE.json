{"id": 226, "name": "TweenAnimationBuilder", "localName": "Tween-Animation Builder", "info": "Verwendet den Tween-Interpolator, um Animationen für verwandte Eigenschaften zu erstellen, und führt über den Builder lokale Builds durch, um den Aktualisierungsbereich zu reduzieren. Benötigt keinen benutzerdefinierten Animator, kann <PERSON>s<PERSON>uer, Kurve und Endrückruf angeben.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TweenAnimationBuilder Anwendungsbeispiel", "desc": ["【tween】 : *Interpolator   【Tween<T>】", "【duration】 : *Dauer   【Duration】", "【builder】 : *Builder   【ValueWidgetBuilder<T>】", "【curve】 : Animationskurve   【Curve】", "【onEnd】 : Endrückruf   【VoidCallback】", "【child】 : Kindkomponente   【Widget】"]}]}