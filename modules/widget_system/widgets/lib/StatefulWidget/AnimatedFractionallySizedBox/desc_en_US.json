{"id": 260, "name": "AnimatedFractionallySizedBox", "localName": "Animated Fractionally Sized Box", "info": "An animated version of FractionallySizedBox, which automatically and smoothly transitions to the target size and position of the child component within the specified duration when the widthFactor or heightFactor changes, or when the alignment changes.", "lever": 3, "family": 1, "linkIds": [82, 120, 123, 121], "nodes": [{"file": "node1.dart", "name": "Animation Transition Effect", "desc": ["In this case, the heightFactor, widthFactor, and alignment parameters are modified when clicked to view the animation effect.", "【child】 : Child component   【Widget】", "【duration】 : Animation duration   【Duration】", "【onEnd】 : Animation end callback   【Function()】", "【alignment】 : Animation transformation center   【Alignment】", "【curve】 : Animation curve   【Duration】", "【heightFactor】 : Height factor   【double?】", "【widthFactor】 : Width factor   【double?】", "【turns】 : Rotation amount   【double】"]}]}