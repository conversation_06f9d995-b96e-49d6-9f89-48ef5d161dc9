{"id": 44, "name": "RangeSlider", "localName": "範囲スライダー", "info": "範囲スライダーコンポーネントは、2点のドラッグをサポートし、その間の範囲を取得します。色、セグメント数、表示されるラベルを指定でき、進捗変化のコールバックを受け取ります。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RangeSlider基本使用", "desc": ["【values】 : 数値   【RangeValues】", "【min】 : 最小値   【double】", "【max】 : 最大値   【double】", "【divisions】 : セグメント数   【int】", "【label】 : ヒントバブルテキスト   【String】", "【activeColor】 : アクティブカラー   【Color】", "【inactiveColor】 : 非アクティブカラー   【Color】", "【onChangeStart】 : スライド開始時のリスナー   【Function(RangeValues)】", "【onChangeEnd】 : スライド終了時のリスナー   【Function(RangeValues)】", "【onChanged】 : 変更時のコールバック   【Function(RangeValues)】"]}]}