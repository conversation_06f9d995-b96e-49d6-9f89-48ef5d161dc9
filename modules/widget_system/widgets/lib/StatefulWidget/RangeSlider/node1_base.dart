import 'package:flutter/material.dart';
/// create by 张风捷特烈 on 2020-03-29
/// contact me <NAME_EMAIL>

class CustomRangeSlider extends StatefulWidget {
  const CustomRangeSlider({Key? key}) : super(key: key);

  @override
  _CustomRangeSliderState createState() => _CustomRangeSliderState();
}

class _CustomRangeSliderState extends State<CustomRangeSlider> {
  RangeValues _rangeValues = const RangeValues(90, 270);

  @override
  Widget build(BuildContext context) {
    return RangeSlider(
        values: _rangeValues,
        divisions: 180,
        min: 0.0,
        max: 360.0,
        labels: RangeLabels(_rangeValues.start.toStringAsFixed(1),
            _rangeValues.end.toStringAsFixed(1)),
        activeColor: Colors.orangeAccent,
        inactiveColor: Colors.green.withAlpha(99),
        onChangeStart: (value) {
          print('开始滑动:$value');
        },
        onChangeEnd: (value) {
          print('滑动结束:$value');
        },
        onChanged: (value) {
          setState(() {
            _rangeValues = value;
          });
        });
  }
}
