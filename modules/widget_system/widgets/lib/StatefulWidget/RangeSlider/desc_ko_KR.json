{"id": 44, "name": "RangeSlider", "localName": "범위 슬라이더", "info": "범위 슬라이더 컴포넌트, 두 점 드래그를 지원하여 그 사이의 범위를 얻을 수 있습니다. 색상, 분할 수 및 표시된 라벨을 지정할 수 있으며, 진행 변화 콜백을 받습니다.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RangeSlider 기본 사용", "desc": ["【values】 : 값   【RangeValues】", "【min】 : 최소값   【double】", "【max】 : 최대값   【double】", "【divisions】 : 분할 수   【int】", "【label】 : 툴팁 텍스트   【String】", "【activeColor】 : 활성화 색상   【Color】", "【inactiveColor】 : 비활성화 색상   【Color】", "【onChangeStart】 : 슬라이드 시작 시 리스너   【Function(RangeValues)】", "【onChangeEnd】 : 슬라이드 종료 시 리스너   【Function(RangeValues)】", "【onChanged】 : 변경 시 콜백   【Function(RangeValues)】"]}]}