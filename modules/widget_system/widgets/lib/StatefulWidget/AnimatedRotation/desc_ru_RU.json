{"id": 259, "name": "AnimatedRotation", "localName": "Анимация вращения", "info": "Когда заданное количество вращения изменяется, дочерний компонент может автоматически настраиваться относительно значения вращения, и есть анимационные изменения между предыдущим и текущим значениями.", "lever": 3, "family": 1, "linkIds": [247, 249], "nodes": [{"file": "node1.dart", "name": "Эффект анимации вращения", "desc": ["В этом примере, перетащите для регулировки параметра turns, чтобы увидеть эффект анимации. Угол вращения: turns*2*pi", "【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【duration】 : Длительность анимации   【Duration】", "【onEnd】 : Обратный вызов по завершении анимации   【Function()】", "【alignment】 : Центр преобразования анимации   【Alignment】", "【curve】 : Кривая анимации   【Duration】", "【filterQuality】 : Качество фильтра   【FilterQuality】", "【turns】 : Количество вращения   【double】"]}]}