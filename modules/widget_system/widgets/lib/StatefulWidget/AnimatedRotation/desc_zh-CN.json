{"id": 259, "name": "AnimatedRotation", "localName": "旋转动画", "info": "给定的旋转量发生变化时，子组件可以自动调整相对于旋转值，且前后值有动画变化。", "lever": 3, "family": 1, "linkIds": [247, 249], "nodes": [{"file": "node1.dart", "name": "旋转动画效果", "desc": ["该案例中，拖拽调节 turns 参数，查看动画运动效果。旋转角度为: turns*2*pi", "【child】 : 孩子组件   【Widget】", "【duration】 : 动画时长   【Duration】", "【onEnd】 : 动画结束回调   【Function()】", "【alignment】 : 动画变换中心   【Alignment】", "【curve】 : 动画曲线   【Duration】", "【filterQuality】 : 滤镜质量   【FilterQuality】", "【turns】 : 旋转量   【double】"]}]}