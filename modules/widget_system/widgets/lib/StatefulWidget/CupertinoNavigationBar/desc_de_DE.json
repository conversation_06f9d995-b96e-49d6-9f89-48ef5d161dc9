{"id": 62, "name": "CupertinoNavigationBar", "localName": "iOS-Navigation", "info": "Eine allgemeine Struktur für eine iOS-ähnliche Anwendungsleiste oben, die es ermöglicht, entsprechende Komponenten an bestimmten Stellen zu platzieren. Attribute wie Hintergrundfarbe, Abstände und Rahmen können angegeben werden.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von CupertinoNavigationBar", "desc": ["【leading】: <PERSON><PERSON>   【Widget】", "【middle】: <PERSON><PERSON><PERSON>   【Widget】", "【trailing】: <PERSON><PERSON><PERSON>mpo<PERSON>   【Widget】", "【backgroundColor】: Hintergrundfarbe   【Color】", "【padding】: Innenabstand   【EdgeInsetsDirectional】", "【border】: <PERSON><PERSON><PERSON>   【Border】"]}]}