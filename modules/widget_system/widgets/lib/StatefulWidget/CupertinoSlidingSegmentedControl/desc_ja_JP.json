{"id": 256, "name": "CupertinoSlidingSegmentedControl", "localName": "iOSスライドタブ", "info": "iOSスタイルのスライドタブで、クリックやスライドでの切り替えをサポートします。タブの色、背景色、余白などの属性を指定できます。", "lever": 3, "family": 1, "linkIds": [33, 262], "nodes": [{"file": "node1_base.dart", "name": "iOSスライドタブの基本使用", "desc": ["【children】 : コンポーネントMap   【Map<T, Widget>】", "【onValueChanged】 : 値変更コールバック   【ValueChanged<T>】", "【groupValue】 : 選択値   【T】", "【thumbColor】 : 選択色   【Color】", "【backgroundColor】 : 背景色   【Color】", "【padding】 : 内側の余白   【EdgeInsetsGeometry】"]}]}