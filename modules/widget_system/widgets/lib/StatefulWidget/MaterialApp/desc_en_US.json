{"id": 65, "name": "MaterialApp", "localName": "Material Application", "info": "The top-level component of a Material application, including properties such as route generator, theme, language, home page, etc.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of MaterialApp", "desc": ["【theme】 : Theme   【ThemeData】", "【title】 : Taskbar Title   【String】", "【debugShowCheckedModeBanner】 : Enable Badge   【bool】", "【showPerformanceOverlay】 : Enable Performance Overlay   【bool】", "【debugShowMaterialGrid】 : Enable Grid   【bool】", "【onGenerateRoute】 : Route Generator   【RouteFactory】", "【home】 : Home Page   【Widget】"]}]}