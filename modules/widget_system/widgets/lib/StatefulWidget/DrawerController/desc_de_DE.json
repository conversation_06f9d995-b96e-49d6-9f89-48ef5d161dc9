{"id": 257, "name": "Drawer<PERSON><PERSON><PERSON><PERSON>", "localName": "iOS-Schiebe-Tab", "info": "Bietet Interaktionsverhalten für die Drawer-Komponente und wird selten verwendet. Es gibt Anwendungsfälle im Quellcode der Scaffold-Komponente.", "lever": 3, "family": 1, "linkIds": [154, 64], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von DrawerController", "desc": ["【drawerCallback】 : Ereignisrückruf   【DrawerCallback】", "【enableOpenDragGesture】 : Ob seitliches Aufziehen aktiviert ist   【bool】", "【alignment】 : Ausrichtung   【DrawerAlignment】", "【scrimColor】 : Hintergrundfarbe   【Color】", "【child】 : Drawer-<PERSON><PERSON><PERSON><PERSON>   【Widget】"]}]}