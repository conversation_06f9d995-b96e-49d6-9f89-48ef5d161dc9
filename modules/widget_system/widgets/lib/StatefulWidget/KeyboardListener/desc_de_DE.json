{"id": 284, "name": "KeyboardListener", "localName": "<PERSON><PERSON><PERSON>-Listener", "info": "Nachdem der Fokus erhalten wurde, wird über onKeyEvent das Tastaturereignis überwacht.", "lever": 5, "family": 1, "linkIds": [282, 283], "nodes": [{"file": "node1.dart", "name": "Tastaturereignisse überwachen", "desc": ["Im Beispiel erhält der Bereich durch Klicken den Fokus, und durch Drücken der Tastatur können die ausgelösten Ereignisse angezeigt werden.", "【focusNode】 : Fokus   【FocusNode】", "【autofocus】 : Automatischer Fokus   【bool】", "【includeSemantics】 : Semantik einschließen   【bool】", "【onKeyEvent】 : Tastaturrückrufereignis   【ValueChanged<KeyEvent>?】", "【child】 : Untergeordnete Komponente   【Widget】"]}]}