{"id": 137, "name": "CupertinoDatePicker", "localName": "iOS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Hochwertige Roll-Da<PERSON><PERSON><PERSON>hl, die den Auswahltyp, Datumsbereich usw. angeben kann und Datumsauswahlereignisse empfängt.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von CupertinoDatePicker", "desc": ["【initialDateTime】 : Anfangsdatum   【DateTime】", "【minimumYear】 : Mindestjahr   【int】", "【maximumYear】 : <PERSON><PERSON><PERSON>tjahr   【int】", "【onDateTimeChanged】 : Klick-Rück<PERSON><PERSON>  【Function(DateTime)】", "【minuteInterval】 : Minutenintervall  【int】", "【use24hFormat】 : Ist 24-Stunden-Format  【bool】", "【backgroundColor】 : Hintergrundfarbe  【Color】", "【mode】 : Modus*3  【CupertinoDatePickerMode】"]}]}