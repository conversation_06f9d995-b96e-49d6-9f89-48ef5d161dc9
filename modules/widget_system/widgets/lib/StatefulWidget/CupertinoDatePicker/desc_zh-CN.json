{"id": 137, "name": "CupertinoDatePicker", "localName": "iOS日期选择器", "info": "高大上的滑滚日期选择器，可指定选择的类型、日期范围等，接收日期选中事件。", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoDatePicker基本使用", "desc": ["【initialDateTime】 : 初始日期   【DateTime】", "【minimumYear】 : 最小年份   【int】", "【maximumYear】 : 最大年份   【int】", "【onDateTimeChanged】 : 点击回调  【Function(DateTime)】", "【minuteInterval】 : 分钟间隔  【int】", "【use24hFormat】 : 是否是24小时制  【bool】", "【backgroundColor】 : 背景色  【Color】", "【mode】 : 模式*3  【CupertinoDatePickerMode】"]}]}