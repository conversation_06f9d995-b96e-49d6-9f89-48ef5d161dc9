{"id": 261, "name": "MergeableMaterial", "localName": "Объединяемый материал", "info": "Используется для отображения списка MergeableMaterialItem, включая MaterialSlice (основная часть) и MaterialGap (разделитель).", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование MergeableMaterial", "desc": ["【elevation】 : Г<PERSON><PERSON><PERSON><PERSON>на тени   【double】", "【hasDividers】 : Наличие разделителей   【bool】", "【dividerColor】 : Цвет разделителя   【Color】", "【mainAxis】 : Ось   【Axis】", "【children】 : Набор дочерних компонентов   【List<MergeableMaterialItem>】"]}]}