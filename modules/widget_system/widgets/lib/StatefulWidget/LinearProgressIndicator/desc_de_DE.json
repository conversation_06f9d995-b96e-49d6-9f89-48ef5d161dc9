{"id": 47, "name": "LinearProgressIndicator", "localName": "Horizontaler Fortschritt", "info": "Eine lineare Fortschrittsanzeige, bei der Eigenschaften wie Farbe und Fortschritt angegeben werden können. Wenn der Wert null ist, dreht sie sich ununterbrochen.", "lever": 3, "family": 1, "linkIds": [46, 48], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von LinearProgressIndicator", "desc": ["【value】 : Fort<PERSON>ritt   【double】", "【backgroundColor】 : Hintergrundfarbe   【Color】", "【valueColor】 : Fortschrittsfarbe   【Animation<Color>】", "    <PERSON><PERSON> der Wert null ist, wird er kontinuierlich wiederholt"]}]}