{"id": 165, "name": "<PERSON><PERSON><PERSON><PERSON>", "localName": "<PERSON><PERSON><PERSON>", "info": "Acomoda várias páginas de componentes, permite alternar entre elas deslizando, pode especificar a direção do deslize, se é reverso, controlador de deslize e outras propriedades.", "lever": 5, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do PageView", "desc": ["【children】 : Lista de componentes filhos   【List<Widget>】", "【onPageChanged】 : Evento de clique  【ValueChanged<int>】"]}, {"file": "node2_direction.dart", "name": "Direção de deslize do PageView", "desc": ["【scrollDirection】 : Direção de deslize   【Axis】", "【reverse】 : Se é reverso  【bool】"]}, {"file": "node3_controller.dart", "name": "Uso simples do controlador do PageView", "desc": ["【controller】 : Controlador de página   【PageController】"]}]}