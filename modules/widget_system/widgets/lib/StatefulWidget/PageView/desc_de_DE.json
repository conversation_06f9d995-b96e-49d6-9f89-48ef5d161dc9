{"id": 165, "name": "<PERSON><PERSON><PERSON><PERSON>", "localName": "Swipe-Seite", "info": "Enth<PERSON>lt mehrere Komponentenseiten, zwischen denen durch Wischen gewechselt werden kann. Es können Attribute wie Wischrichtung, ob umgekehrt, Wischcontroller usw. angegeben werden.", "lever": 5, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>", "desc": ["【children】 : Liste der Unterkomponenten   【List<Widget>】", "【onPageChanged】 : Klickereignis  【ValueChanged<int>】"]}, {"file": "node2_direction.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "desc": ["【scrollDirection】 : Wischrichtung   【Axis】", "【reverse】 : Ob umgekehrt  【bool】"]}, {"file": "node3_controller.dart", "name": "Einfache Verwendung des PageView-Controllers", "desc": ["【controller】 : Seitencontroller   【PageController】"]}]}