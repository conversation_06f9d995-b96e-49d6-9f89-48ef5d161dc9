{"id": 242, "name": "StatefulBuilder", "localName": "Constructeur d'état", "info": "Nécessite de passer la propriété builder pour construire le composant. Dan<PERSON> le builder, vous pouvez utiliser StateSetter pour modifier l'état du sous-composant, ce qui permet de créer un composant à rafraîchissement local sans avoir à créer une classe.", "lever": 3, "family": 1, "linkIds": [202, 203, 280, 255], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de StatefulBuilder", "desc": ["【builder】 : Constructeur de composant   【StatefulWidgetBuilder】"]}]}