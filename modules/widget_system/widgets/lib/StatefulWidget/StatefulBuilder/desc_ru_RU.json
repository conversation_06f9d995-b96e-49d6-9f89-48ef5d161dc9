{"id": 242, "name": "StatefulBuilder", "localName": "Конструктор состояния", "info": "Необходимо передать свойство builder для создания компонента. В builder можно использовать StateSetter для изменения состояния дочернего компонента, что позволяет реализовать локальное обновление компонента без создания класса.", "lever": 3, "family": 1, "linkIds": [202, 203, 280, 255], "nodes": [{"file": "node1_base.dart", "name": "Основное использование StatefulBuilder", "desc": ["【builder】 : Конструктор компонента   【StatefulWidgetBuilder】"]}]}