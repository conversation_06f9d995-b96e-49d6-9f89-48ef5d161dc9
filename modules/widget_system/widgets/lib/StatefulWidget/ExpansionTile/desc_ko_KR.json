{"id": 52, "name": "ExpansionTile", "localName": "확장 타일", "info": "일반적인 확장 바, 지정된 부분에 컴포넌트를 배치할 수 있으며, 클릭 시 아래 컴포넌트를 접거나 펼칠 수 있습니다. 접힐 때 이벤트를 수신합니다.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "ExpansionTile 기본 사용", "desc": ["【children】 : 확장 내용   【List<Widget>】", "【leading】 : 헤더 왼쪽 컴포넌트   【Widget】", "【title】 : 헤더 중앙 컴포넌트   【Widget】", "【trailing】 : 헤더 오른쪽 컴포넌트   【Widget】", "【backgroundColor】 : 배경색   【Color】", "【onExpansionChanged】 : 접힘 이벤트   【Function(bool)】", "【initiallyExpanded】 : 초기 상태에서 펼쳐져 있는지 여부   【bool】"]}]}