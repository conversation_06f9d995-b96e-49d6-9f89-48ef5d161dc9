{"id": 52, "name": "ExpansionTile", "localName": "Erweiterungs-Kachel", "info": "Ein allgemeines Erweiterungsfeld, das Komponenten an einer bestimmten Stelle platzieren kann. Beim <PERSON>en werden die darunter liegenden Komponenten ein- und ausgeklappt. Empfängt Ereignisse beim Einklappen.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von ExpansionTile", "desc": ["【children】 : <PERSON><PERSON><PERSON><PERSON>ungsinhalt   【List<Widget>】", "【leading】 : <PERSON><PERSON> Kopfkomponente   【Widget】", "【title】 : <PERSON><PERSON><PERSON>   【Widget】", "【trailing】 : <PERSON><PERSON><PERSON> Kopfkomponente   【Widget】", "【backgroundColor】 : Hintergrundfarbe   【Color】", "【onExpansionChanged】 : Einklapp-Ereignis   【Function(bool)】", "【initiallyExpanded】 : <PERSON><PERSON>   【bool】"]}]}