{"id": 252, "name": "DraggableScrollableSheet", "localName": "Перетаскиваемый и прокручиваемый лист", "info": "Перетаскиваемый и прокручиваемый лист, который может указывать максимальное, минимальное и начальное деление в текущем диапазоне прокрутки. Конструктор builder должен возвращать прокручиваемый компонент.", "lever": 2, "family": 1, "linkIds": [221, 142], "nodes": [{"file": "node1_base.dart", "name": "Основное использование DraggableScrollableSheet", "desc": ["【initialChildSize】 : Начальное деление   【double】", "【minChildSize】 : Минимальное деление   【double】", "【maxChildSize】 : Максимальное деление   【double】", "【builder】 : Конструктор прокручиваемого компонента   【ScrollableWidgetBuilder】", "【expand】 : Расширять ли   【bool】"]}]}