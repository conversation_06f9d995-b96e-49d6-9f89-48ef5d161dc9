{"id": 250, "name": "GlowingOverscrollIndicator", "localName": "Индикатор прокрутки за пределы", "info": "Дети - это прокручиваемый список, индикаторный эффект при прокрутке до верха и низа, можно указать цвет, не имеет большого значения. Это эффект прокрутки по умолчанию для систем Android и Fuchsia.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование", "desc": ["【showLeading】 : Действует ли на голову   【bool】", "【showTrailing】 : Действует ли на дно   【bool】", "【axisDirection】 : Направление оси   【AxisDirection】", "【color】 : Цвет   【Color】", "【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】"]}]}