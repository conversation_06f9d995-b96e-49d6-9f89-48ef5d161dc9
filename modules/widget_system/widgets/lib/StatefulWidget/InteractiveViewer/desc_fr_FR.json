{"id": 351, "name": "InteractiveViewer", "localName": "Vue interactive", "info": "Principalement pour encapsuler les interactions gestuelles telles que le déplacement et le zoom, simplifier l'utilisation, et permettre de spécifier les limites de déplacement, les ratios de zoom, l'écoute des gestes, etc.", "lever": 4, "family": 1, "linkIds": [147, 146, 78], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base d'InteractiveViewer", "desc": ["【alignPanAxis】 : Glisser le long de l'axe   【bool】", "【boundaryMargin】 : <PERSON><PERSON> de la limite   【EdgeInsets】", "【panEnabled】 : Peut être déplacé   【bool】", "【scaleEnabled】 : Peut être zoomé   【bool】", "【maxScale】 : Facteur de zoom maximum   【double】", "【minScale】 : Facteur de zoom minimum   【double】", "【onInteractionEnd】 : Rappel de fin d'interaction   【GestureScaleEndCallback】", "【onInteractionStart】 : Rappel de début d'interaction   【GestureScaleStartCallback】", "【onInteractionUpdate】 : <PERSON><PERSON> de mise à jour d'interaction   【GestureScaleUpdateCallback】", "【child】 : <PERSON><PERSON><PERSON> du curseur   【Widget】"]}, {"file": "node2_constrained.dart", "name": "Test de la propriété constrained", "desc": ["【constrained】 :  Contraint   【bool】"]}, {"file": "node3_controller.dart", "name": "Utilisation du contrôleur de transformation", "desc": ["【transformationController】 : Contrôleur de transformation   【TransformationController】"]}]}