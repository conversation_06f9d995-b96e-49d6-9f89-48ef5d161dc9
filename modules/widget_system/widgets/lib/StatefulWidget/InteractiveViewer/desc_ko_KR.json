{"id": 351, "name": "InteractiveViewer", "localName": "인터랙티브 뷰어", "info": "주로 이동, 확대/축소 등의 제스처 인터랙션을 캡슐화하여 사용을 단순화하며, 이동 경계, 확대/축소 비율, 제스처 리스너 등을 지정할 수 있습니다.", "lever": 4, "family": 1, "linkIds": [147, 146, 78], "nodes": [{"file": "node1_base.dart", "name": "InteractiveViewer 기본 사용", "desc": ["【alignPanAxis】 : 축을 따라 드래그   【bool】", "【boundaryMargin】 : 경계 여백   【EdgeInsets】", "【panEnabled】 : 이동 가능 여부   【bool】", "【scaleEnabled】 : 확대/축소 가능 여부   【bool】", "【maxScale】 : 최대 확대 배율   【double】", "【minScale】 : 최소 축소 배율   【double】", "【onInteractionEnd】 : 인터랙션 종료 콜백   【GestureScaleEndCallback】", "【onInteractionStart】 : 인터랙션 시작 콜백   【GestureScaleStartCallback】", "【onInteractionUpdate】 : 인터랙션 업데이트 콜백   【GestureScaleUpdateCallback】", "【child】 : 커서 색상   【Widget】"]}, {"file": "node2_constrained.dart", "name": "constrained 속성 테스트", "desc": ["【constrained】 :  제약이 있는  【bool】"]}, {"file": "node3_controller.dart", "name": "변환 컨트롤러 사용", "desc": ["【transformationController】 : 변환 컨트롤러   【TransformationController】"]}]}