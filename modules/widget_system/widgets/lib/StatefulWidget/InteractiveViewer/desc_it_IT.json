{"id": 351, "name": "InteractiveViewer", "localName": "Visualizzatore Interattivo", "info": "Principalmente incapsula le interazioni gestuali come lo spostamento e lo zoom, semplificando l'uso, è possibile specificare i limiti di movimento, la scala di zoom, il monitoraggio dei gesti, ecc.", "lever": 4, "family": 1, "linkIds": [147, 146, 78], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di InteractiveViewer", "desc": ["【alignPanAxis】 : <PERSON><PERSON><PERSON><PERSON> lungo l'asse   【bool】", "【boundaryMargin】 : <PERSON><PERSON><PERSON> del bordo   【EdgeInsets】", "【panEnabled】 : Se è possibile traslare   【bool】", "【scaleEnabled】 : Se è possibile ridimensionare   【bool】", "【maxScale】 : <PERSON><PERSON> di ingrandimento massimo   【double】", "【minScale】 : Fattore di riduzione minimo   【double】", "【onInteractionEnd】 : Callback di fine interazione   【GestureScaleEndCallback】", "【onInteractionStart】 : Callback di inizio interazione   【GestureScaleStartCallback】", "【onInteractionUpdate】 : Callback di aggiornamento interazione   【GestureScaleUpdateCallback】", "【child】 : <PERSON>e del cursore   【Widget】"]}, {"file": "node2_constrained.dart", "name": "Test della proprietà constrained", "desc": ["【constrained】 :  <PERSON><PERSON><PERSON>  【bool】"]}, {"file": "node3_controller.dart", "name": "Uso del controller di trasformazione", "desc": ["【transformationController】 : Controller di trasformazione   【TransformationController】"]}]}