{"id": 222, "name": "FormField", "localName": "Campo de formulario", "info": "Un campo de formulario que debe usarse en el componente Form, contiene un campo de tipo genérico T como estado, y las actualizaciones y validaciones del campo activarán las devoluciones de llamada correspondientes.", "lever": 2, "family": 1, "linkIds": [198, 199, 223], "nodes": [{"file": "node1_base.dart", "name": "Introducción a FormField", "desc": ["【builder】 : Con<PERSON><PERSON>ctor de contenido   【FormFieldBuilder<T>】", "【initialValue】 : Valor inicial   【T】", "【validator】 : Función de validación   【FormFieldValidator<String> 】", "【enabled】 : Si está habilitado   【bool】", "【onSaved】 : Devolución de llamada al guardar el formulario  【FormFieldSetter<String>】"]}]}