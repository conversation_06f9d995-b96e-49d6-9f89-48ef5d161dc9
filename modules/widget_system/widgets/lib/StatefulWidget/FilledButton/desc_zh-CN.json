{"id": 359, "name": "FilledButton", "localName": "填充按钮", "info": "一个符合 Material Design 的填充按钮，通过 FilledButtonTheme 设置按钮样式。", "lever": 4, "family": 1, "linkIds": [354, 355, 353], "nodes": [{"file": "node1.dart", "name": "基本使用", "desc": ["【child】 : 按钮内容   【Widget】", "【onPressed】 : 点击事件   【VoidCallback】", "【onHover】 : 长按事件   【ValueChanged<bool>? 】", "【onLongPress】 : 长按事件   【VoidCallback?】", "填充按钮在视觉上仅次于 [FloatingActionButton]，应用于重要的、最终完成流程的操作，例如: 保存、立即加入 或 确认。"]}, {"file": "node2.dart", "name": "色调变体", "desc": ["FilledButton.tonal 是色调填充按钮，视觉上介于 [FilledButton] 和 [OutlinedButton] 之间，适用于需要比轮廓按钮稍强一些的强调但优先级较低的场景。例如 [下一步] 按钮 "]}]}