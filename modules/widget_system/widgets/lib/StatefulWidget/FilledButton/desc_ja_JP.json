{"id": 359, "name": "FilledButton", "localName": "塗りつぶしボタン", "info": "Material Designに準拠した塗りつぶしボタンで、FilledButtonThemeを使用してボタンのスタイルを設定します。", "lever": 4, "family": 1, "linkIds": [354, 355, 353], "nodes": [{"file": "node1.dart", "name": "基本使用", "desc": ["【child】 : ボタンの内容   【Widget】", "【onPressed】 : クリックイベント   【VoidCallback】", "【onHover】 : 長押しイベント   【ValueChanged<bool>? 】", "【onLongPress】 : 長押しイベント   【VoidCallback?】", "塗りつぶしボタンは視覚的に [FloatingActionButton] に次ぐもので、重要な、最終的なプロセスを完了する操作に使用されます。例えば、保存、即時参加、確認などです。"]}, {"file": "node2.dart", "name": "トーンバリアント", "desc": ["FilledButton.tonal はトーン塗りつぶしボタンで、視覚的に [FilledButton] と [OutlinedButton] の中間に位置し、輪郭ボタンよりもやや強い強調が必要だが優先度が低いシナリオに適しています。例えば [次へ] ボタン"]}]}