{"id": 359, "name": "FilledButton", "localName": "<PERSON><PERSON><PERSON>", "info": "Un botón relleno que cumple con Material Design, configurado a través de FilledButtonTheme.", "lever": 4, "family": 1, "linkIds": [354, 355, 353], "nodes": [{"file": "node1.dart", "name": "Uso Básico", "desc": ["【child】 : <PERSON><PERSON><PERSON> del botón   【Widget】", "【onPressed】 : Evento de clic   【VoidCallback】", "【onHover】 : <PERSON>o de mantener presionado   【ValueChanged<bool>? 】", "【onLongPress】 : Evento de mantener presionado   【VoidCallback?】", "El botón relleno es visualmente inferior al [FloatingActionButton], y se utiliza para acciones importantes y finales, como: guardar, unirse ahora o confirmar."]}, {"file": "node2.dart", "name": "<PERSON><PERSON><PERSON>", "desc": ["FilledButton.tonal es un botón relleno tonal, visualmente entre [FilledButton] y [OutlinedButton], adecuado para escenarios que requieren un énfasis ligeramente mayor que el botón de contorno pero con menor prioridad. Por eje<PERSON><PERSON>, el botón [Siguiente]"]}]}