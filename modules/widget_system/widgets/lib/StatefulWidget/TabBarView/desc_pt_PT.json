{"id": 59, "name": "TabBarView", "localName": "Separador de Páginas", "info": "Geralmente usado em conjunto com o TabBar para alcançar o efeito de deslizar páginas. Normalmente não é usado sozinho.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TabBarView precisa ser usado em conjunto com o TabBar", "desc": ["【controller】 : Controlador   【TabController】", "【children】 : <PERSON><PERSON><PERSON><PERSON>   【Cor do indicador】", "【physics】 : Comportamento   【ScrollPhysics】"]}]}