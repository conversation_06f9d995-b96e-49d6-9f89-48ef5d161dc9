{"id": 59, "name": "TabBarView", "localName": "Onglets", "info": "Généralement utilisé avec TabBar pour réaliser un effet de glissement de pages. Il n'est généralement pas utilisé seul.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TabBarView doit être utilisé avec TabBar", "desc": ["【controller】 : Contrôleur   【TabController】", "【children】 : Enfants   【<PERSON><PERSON>ur de l'indicateur】", "【physics】 : Comportement   【ScrollPhysics】"]}]}