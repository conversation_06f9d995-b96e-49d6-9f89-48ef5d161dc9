{"id": 176, "name": "Dismissible", "localName": "슬라이드로 사라짐", "info": "슬라이드 시 하위 컴포넌트를 표시할 수 있으며, 슬라이드 방향과 교차 축의 오프셋을 지정할 수 있습니다. 사라짐 확인 및 사라짐 시 콜백을 받습니다.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Dismissible 기본 사용", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【background】 : 왼쪽 하단  【Widget】", "【secondaryBackground】 : 오른쪽 하단  【Widget】", "【key】 : 키  【Key】", "【confirmDismiss】 : 확인 콜백  【DismissDirectionCallback】", "【onDismissed】 : 사라짐 콜백  【DismissDirectionCallback】,"]}, {"file": "node2_direction.dart", "name": "Dismissible 기본 사용", "desc": ["【direction】 : 방향   【DismissDirection】", "【crossAxisEndOffset】 : 오프셋  【double】,"]}]}