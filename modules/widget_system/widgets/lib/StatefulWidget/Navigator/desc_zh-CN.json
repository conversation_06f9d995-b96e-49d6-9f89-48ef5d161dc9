{"id": 232, "name": "Navigator", "localName": "导航器", "info": "Navigator用堆栈规则管理一组子组件,可以将子组件切入弹出及监听出入栈事件。MaterialApp路由管理的本源就是使用了Navigator。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Navigator基本用法", "desc": ["【initialRoute】 : 最初显示路由   【String】", "【onGenerateRoute】 : 路由生成器   【RouteFactory】", "【observers】 : 路由监听器   【List<NavigatorObserver>】", "【onPopPage】 : 出栈回调   【PopPageCallback】"]}]}