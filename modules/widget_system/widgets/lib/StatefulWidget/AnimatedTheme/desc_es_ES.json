{"id": 224, "name": "AnimatedTheme", "localName": "<PERSON><PERSON>", "info": "Componente con efectos de animación cuando cambia el tema, esencialmente es una combinación del componente Theme y animaciones. Se puede especificar ThemeData, duración de la animación, curva, devolución de llamada al finalizar, etc. Equivale a una versión mejorada del componente Theme.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de AnimatedTheme", "desc": ["【data】 : Datos del tema   【ThemeData】", "【duration】 : Duración de la animación   【Duration】", "【onEnd】 : Devolución de llamada al finalizar la animación   【Function()】", "【curve】 : Curva de la animación   【Duration】", "【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】"]}]}