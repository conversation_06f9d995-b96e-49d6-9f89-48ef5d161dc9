{"id": 237, "name": "CarouselView", "localName": "Karussellansicht", "info": "<PERSON><PERSON> Ka<PERSON>ell-Komponente, die den Material Design-Richtlinien entspricht. Zeigt eine scrollbare Liste von Elementen an, wobei jedes Element basierend auf dem ausgewählten Layout dynamisch angepasst werden kann.", "lever": 4, "family": 1, "linkIds": [253, 340, 160], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von Car<PERSON>iew", "desc": ["【itemExtent】 : Erzwungene Größe in der Hauptachse  【double】", "【shrinkExtent】: Minimale Größe des Elements in der Hauptachse während des Scrollens 【double】", "【scrollDirection】 : Scrollrichtung   【Axis?】", "【children】: Liste der Unterkomponenten 【List<Widget>】"]}, {"file": "node2.dart", "name": "Stilkonfiguration von <PERSON>iew", "desc": ["【padding】 : Innenabstand   【EdgeInsets? 】", "【backgroundColor】 : Hintergrundfarbe   【Color? 】", "【elevation】 : <PERSON><PERSON><PERSON>tie<PERSON>   【double?】", "【shape】 : Form   【ShapeBorder?】", "【controller】 : Controller   【CarouselController?】", "【reverse】 : Umgekehrtes Scrollen   【bool】", "【onTap】 : Klick-Ereignis   【ValueChanged<int>? 】"]}]}