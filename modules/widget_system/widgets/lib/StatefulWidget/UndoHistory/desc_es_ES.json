{"id": 241, "name": "UndoHistory", "localName": "<PERSON><PERSON> de <PERSON>", "info": "Recibe un ValueNotifier para escuchar valores, proporcionando funcionalidad de deshacer/rehacer para ese valor; utilizado en el código fuente del componente TextField.", "lever": 2, "family": 1, "linkIds": [54], "nodes": [{"file": "node1.dart", "name": "Uso de UndoHistory en TextField", "desc": ["En este caso, se controla la funcionalidad de deshacer/rehacer del TextField mediante un botón externo. El parámetro undoController puede recibir un objeto UndoHistoryController, utilizado para controlar y afectar el contenido del texto ingresado."]}]}