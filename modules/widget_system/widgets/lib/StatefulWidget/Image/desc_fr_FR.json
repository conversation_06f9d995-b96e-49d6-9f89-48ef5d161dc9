{"id": 38, "name": "Image", "localName": "Composant d'image", "info": "Utilisé pour afficher une image, qui peut être chargée à partir d'un fichier, de la mémoire, du réseau ou des ressources. V<PERSON> pouvez spécifier le mode d'adaptation, le style, le mode de mélange des couleurs, le mode de répétition, etc.", "lever": 5, "family": 1, "linkIds": [8, 87], "nodes": [{"file": "node1_base.dart", "name": "Peut charger des images à partir de fichiers de ressources et du réseau", "desc": ["Image.asset charge les images de ressources."]}, {"file": "node2_fit.dart", "name": "Mode d'adaptation de l'image", "desc": ["【fit】 : Mode d'adaptation*7 【BoxFit】"]}, {"file": "node3_alignment.dart", "name": "Mode d'alignement de l'image", "desc": ["【alignment】 : Couleur 【AlignmentGeometry】", "    Neuf constantes statiques couramment utilisées de la classe Alignment, mais il est également possible de personnaliser la position"]}, {"file": "node4_colorBlendMode.dart", "name": "Couleur et mode de mélange de l'image", "desc": ["【color】 : Couleur 【Color】", "【colorBlendMode】: Mode de mélange*29 【BlendMode】"]}, {"file": "node5_repeat.dart", "name": "Mode de répétition de l'image", "desc": ["【repeat】 : Mode de répétition*4 【ImageRepeat】"]}, {"file": "node6_centerSlice.dart", "name": "Agrandissement local de l'image", "desc": ["【centerSlice】 : Zone conservée 【Rect】"]}]}