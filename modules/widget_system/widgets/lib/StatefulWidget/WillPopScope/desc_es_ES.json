{"id": 170, "name": "WillPopScope", "localName": "Intercepción de retorno", "info": "Cuando hay un componente WillPopScope en una interfaz, se activará una devolución de llamada al regresar de la página para decidir si se debe regresar. Se puede utilizar en escenarios de confirmación secundaria para salir.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso de WillPopScope", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【onWillPop】 : Devolución de llamada  【WillPopCallback】"]}]}