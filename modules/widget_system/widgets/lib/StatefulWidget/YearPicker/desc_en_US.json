{"id": 136, "name": "YearPicker", "localName": "Year Picker", "info": "A component for selecting years, with a rather modest appearance. It allows specifying the date range for selection, selected dates, etc., and receives each selection event.", "lever": 3, "family": 1, "linkIds": [134, 135], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of YearPicker", "desc": ["【selectedDate】: Selected date   【DateTime】", "【firstDate】: First date limit   【DateTime】", "【lastDate】: Last date limit   【DateTime】", "【onChanged】: Click callback  【Function(DateTime)】"]}]}