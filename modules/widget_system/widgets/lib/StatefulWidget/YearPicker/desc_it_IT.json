{"id": 136, "name": "YearPicker", "localName": "<PERSON><PERSON><PERSON>", "info": "Componente per la selezione dell'anno, dall'aspetto modesto. Consente di specificare l'intervallo di date selezionabili, la data selezionata, ecc., e riceve ogni evento di selezione.", "lever": 3, "family": 1, "linkIds": [134, 135], "nodes": [{"file": "node1_base.dart", "name": "Utilizzo Base di YearPicker", "desc": ["【selectedDate】 : Data selezionata   【DateTime】", "【firstDate】 : Limite data iniziale   【DateTime】", "【lastDate】 : Limite data finale   【DateTime】", "【onChanged】 : Callback al click  【Function(DateTime)】"]}]}