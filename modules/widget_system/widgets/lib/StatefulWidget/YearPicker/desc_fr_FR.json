{"id": 136, "name": "YearPicker", "localName": "<PERSON><PERSON><PERSON><PERSON> d'ann<PERSON>", "info": "Composant de sélection d'année, d'apparence modeste. Permet de spécifier la plage de dates sélectionnables, la date sélectionnée, etc., et reçoit chaque événement de sélection.", "lever": 3, "family": 1, "linkIds": [134, 135], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de YearPicker", "desc": ["【selectedDate】 : Date sélectionnée   【DateTime】", "【firstDate】 : <PERSON><PERSON> de la première date   【DateTime】", "【lastDate】 : <PERSON><PERSON> de la dernière date   【DateTime】", "【onChanged】 : <PERSON><PERSON> au clic  【Function(DateTime)】"]}]}