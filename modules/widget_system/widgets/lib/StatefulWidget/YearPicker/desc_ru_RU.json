{"id": 136, "name": "YearPicker", "localName": "Выбор года", "info": "Компонент для выбора года, выглядит довольно скромно. Можно указать диапазон дат, выбранную дату и т.д., принимает каждое событие выбора", "lever": 3, "family": 1, "linkIds": [134, 135], "nodes": [{"file": "node1_base.dart", "name": "Основное использование YearPicker", "desc": ["【selectedDate】 : Выбранная дата   【DateTime】", "【firstDate】 : Ограничение на самую раннюю дату   【DateTime】", "【lastDate】 : Ограничение на самую позднюю дату   【DateTime】", "【onChanged】 : Обратный вызов при нажатии  【Function(DateTime)】"]}]}