{"id": 227, "name": "AnimatedModalBarrier", "localName": "Анимированный барьер", "info": "Внутренняя реализация зависит от ModalBarrier, функциональность идентична, за исключением того, что этот компонент может принимать анимацию цвета для плавного отображения.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Введение в AnimatedModalBarrier", "desc": ["【dismissible】 : Возврат при клике   【bool】", "【color】 : Цвет   【Animation<Color>】"]}]}