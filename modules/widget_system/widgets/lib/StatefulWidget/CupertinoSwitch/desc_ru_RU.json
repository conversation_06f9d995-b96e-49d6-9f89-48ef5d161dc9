{"id": 41, "name": "CupertinoSwitch", "localName": "iOS переключатель", "info": "Переключатель в стиле iOS, часто используется для переключения настроек, может быть указан цвет, получает обратный вызов при изменении состояния.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование CupertinoSwitch", "desc": ["【value】 : выбрано ли   【double】", "【activeColor】 : цвет активного состояния   【Color】", "【onChanged】 : обратный вызов переключения   【Function(double)】"]}]}