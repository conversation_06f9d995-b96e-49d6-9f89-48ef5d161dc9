{"id": 251, "name": "NestedScrollView", "localName": "嵌套滑动视图", "info": "用于多个视图滑动嵌套处理,可以指定头部、滑动控制器、滑动方向等,其中body必须是可滑动类型的组件。", "lever": 4, "family": 4, "linkIds": [183, 344], "nodes": [{"file": "node1_base.dart", "name": "NestedScrollView基本用法", "desc": ["【controller】 : 滑动控制器   【ScrollController】", "【scrollDirection】 : 滑动方向   【Axis】", "【reverse】 : 是否反向   【bool】", "【physics】 : 滑顶样式   【ScrollPhysics】", "【dragStartBehavior】 : 开始拖动行为   【DragStartBehavior】", "【headerSliverBuilder】 : *头部构造器   【NestedScrollViewHeaderSliversBuilder】", "【body】 : *内容   【Widget】"]}]}