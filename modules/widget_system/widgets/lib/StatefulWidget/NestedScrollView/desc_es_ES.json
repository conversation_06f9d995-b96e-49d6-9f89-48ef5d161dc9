{"id": 251, "name": "NestedScrollView", "localName": "Vista de desplazamiento anidada", "info": "Utilizado para manejar el desplazamiento anidado de múltiples vistas, se puede especificar la cabecera, el controlador de desplazamiento, la dirección de desplazamiento, etc., donde el cuerpo debe ser un componente de tipo desplazable.", "lever": 4, "family": 4, "linkIds": [183, 344], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de NestedScrollView", "desc": ["【controller】 : Controlador de desplazamiento   【ScrollController】", "【scrollDirection】 : Dirección de desplazamiento   【Axis】", "【reverse】 : Invertir   【bool】", "【physics】 : <PERSON><PERSON><PERSON>pla<PERSON>o   【ScrollPhysics】", "【dragStartBehavior】 : Comportamiento de inicio de arrastre   【DragStartBehavior】", "【headerSliverBuilder】 : *Constructor de cabecera   【NestedScrollViewHeaderSliversBuilder】", "【body】 : *Contenido   【Widget】"]}]}