{"id": 253, "name": "Scrollable", "localName": "Componente Scorrevole", "info": "Implementa un modello di interazione per un componente scorrevole, richiede la costruzione di un viewport tramite viewportBuilder. È uno dei componenti principali di ScrollView e generalmente non viene utilizzato direttamente.", "lever": 4, "family": 1, "linkIds": [340, 349], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di Scrollable", "desc": ["【viewportBuilder】 : Costruttore del viewport   【ViewportBuilder】", "【axisDirection】: Direzione di scorrimento   【AxisDirection】", "【controller】: Controller di scorrimento   【ScrollController】", "【dragStartBehavior】: Comportamento di trascinamento   【DragStartBehavior】", "【physics】: Fisica dello scorrimento   【ScrollPhysics】,"]}]}