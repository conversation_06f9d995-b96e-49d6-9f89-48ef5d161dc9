{"id": 120, "name": "AnimatedAlign", "localName": "Allineamento Animato", "info": "Consente ai componenti figli di eseguire animazioni di allineamento (Align), con la possibilità di specificare la durata e la curva, e con un evento di fine animazione.", "lever": 3, "family": 1, "linkIds": [85, 111], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di AnimatedAlign", "desc": ["【child】 : Componente figlio   【Widget】", "【duration】 : Durata dell'animazione   【Duration】", "【onEnd】 : Callback di fine animazione   【Function()】", "【alignment】 : Modalità di allineamento   【AlignmentGeometry】", "【curve】 : Curva dell'animazione   【Duration】", "【padding】 : Spaziatura interna   【EdgeInsetsGeometry】"]}]}