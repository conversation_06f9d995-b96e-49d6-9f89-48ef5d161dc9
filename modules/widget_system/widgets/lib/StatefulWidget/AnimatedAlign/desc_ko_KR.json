{"id": 120, "name": "AnimatedAlign", "localName": "정렬 애니메이션", "info": "자식 위젯이 Align(정렬) 애니메이션을 수행할 수 있게 하며, 지속 시간과 곡선을 지정할 수 있고, 애니메이션 종료 이벤트가 있습니다.", "lever": 3, "family": 1, "linkIds": [85, 111], "nodes": [{"file": "node1_base.dart", "name": "AnimatedAlign 기본 사용", "desc": ["【child】 : 자식 위젯   【Widget】", "【duration】 : 애니메이션 지속 시간   【Duration】", "【onEnd】 : 애니메이션 종료 콜백   【Function()】", "【alignment】 : 정렬 방식   【AlignmentGeometry】", "【curve】 : 애니메이션 곡선   【Duration】", "【padding】 : 내부 여백   【EdgeInsetsGeometry】"]}]}