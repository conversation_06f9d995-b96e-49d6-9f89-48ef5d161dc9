{"id": 111, "name": "AlignTransition", "localName": "Alignment Transition", "info": "A subclass of AnimatedWidget, using an animator of type Alignment to allow child components to transition between two Alignment objects.", "lever": 3, "family": 1, "linkIds": [85, 120], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of AlignTransition", "desc": ["【child】: Child component   【Widget】", "【alignment】: Alignment animation   【Animation<AlignmentGeometry>】"]}]}