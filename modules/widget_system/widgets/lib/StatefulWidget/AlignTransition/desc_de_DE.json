{"id": 111, "name": "AlignTransition", "localName": "Ausrichtungsübergang", "info": "Eine Unterklasse von AnimatedWidget, die einen Animator vom Typ Alignment verwendet, um eine Übergangsanimation zwischen zwei Alignment-Objekten für das untergeordnete Widget zu ermöglichen.", "lever": 3, "family": 1, "linkIds": [85, 120], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AlignTransition", "desc": ["【child】 : Kind-Widget   【Widget】", "【alignment】 : Ausrichtungsanimation   【Animation<AlignmentGeometry>】"]}]}