{"id": 111, "name": "AlignTransition", "localName": "Анимация выравнивания", "info": "Подкласс AnimatedWidget, использующий аниматор типа Alignment для создания переходной анимации между двумя объектами Alignment для дочернего компонента.", "lever": 3, "family": 1, "linkIds": [85, 120], "nodes": [{"file": "node1_base.dart", "name": "Основное использование AlignTransition", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【alignment】 : Анимация выравнивания   【Animation<AlignmentGeometry>】"]}]}