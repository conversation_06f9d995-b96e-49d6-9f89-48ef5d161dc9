{"id": 153, "name": "RawChip", "localName": "Native Chip", "info": "Der Vorfahr aller Chip-Komponenten, der die Fähigkeit besitzt, das Verhalten jedes Chips zu zeigen, unterstützt Ereignisse wie Auswählen, Klicken und Löschen. Weitere Informationen finden Sie unter Chip, FilterChip, ActionChip, InputChip und ChoiceChip.", "lever": 5, "family": 1, "linkIds": [11, 12, 13, 14, 15], "nodes": [{"file": "node1_press.dart", "name": "RawChip Klickeffekt", "desc": ["【label】: <PERSON><PERSON><PERSON>mpo<PERSON>   【Widget】", "【padding】: Innenabstand   【EdgeInsetsGeometry】", "【labelPadding】: Label-Abstand   【EdgeInsetsGeometry】", "【shadowColor】: Schattenfarbe   【Color】", "【avatar】: <PERSON>e Komponente   【Widget】", "【elevation】: Sc<PERSON>tentiefe   【double】", "【pressElevation】: <PERSON><PERSON><PERSON><PERSON><PERSON> beim <PERSON>   【double】", "【onPressed】: Klickereignis  【Function()】"]}, {"file": "node2_select.dart", "name": "RawChip Auswahl- und Lösch-Effekt", "desc": ["【selected】: Ist ausgewählt   【bool】", "【deleteIconColor】: Farbe des Endsymbols   【Color】", "【selectedColor】: Auswahlfarbe   【Color】", "【deleteIcon】: Endkomponente   【Widget】", "【onSelected】: Auswahlereignis   【Function(bool)】", "【onDeleted】: Endereignis  【Function()】"]}]}