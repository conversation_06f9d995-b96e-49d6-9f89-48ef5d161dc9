{"id": 104, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "<PERSON><PERSON> zum Ziehen", "info": "Ein Zielbereich zum Ziehen, der Informationen von der Draggable-Komponente empfangen kann. Kann Rückrufe beim <PERSON> erhalten.", "lever": 4, "family": 1, "linkIds": [103, 105], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von DragTarget", "desc": ["【builder】 : Komponentenkonstruktor   【DragTargetBuilder<T>】", "【onWillAccept】 : <PERSON><PERSON>   【Function(T)】", "【onAccept】 : <PERSON><PERSON><PERSON>   【Function(T)】", "【onLeave】 : <PERSON><PERSON><PERSON> und Loslassen   【Function(T)】"]}]}