{"id": 255, "name": "ValueListenableBuilder", "localName": "Value Listener Builder", "info": "Can listen to a value, and when it changes, the interface can be rebuilt through the builder callback, avoiding the use of setState for refreshing.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of ValueListenableBuilder", "desc": ["【builder】: Component builder   【ValueWidgetBuilder<T>】", "【valueListenable】: Listened value    【ValueListenable<T>】", "【child】: Child component    【Widget】"]}]}