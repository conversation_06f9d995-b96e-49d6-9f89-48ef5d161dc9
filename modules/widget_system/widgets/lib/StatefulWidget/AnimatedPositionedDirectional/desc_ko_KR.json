{"id": 122, "name": "AnimatedPositionedDirectional", "localName": "방향 위치 애니메이션", "info": "자식 구성 요소를 PositionedDirectional(방향 위치) 애니메이션으로 만들 수 있으며, 지속 시간과 곡선을 지정할 수 있고, 애니메이션 종료 이벤트가 있습니다. Stack 내에서만 사용할 수 있습니다.", "lever": 3, "family": 1, "linkIds": [121, 159], "nodes": [{"file": "node1_base.dart", "name": "AnimatedPositionedDirectional 기본 사용", "desc": ["【child】 : 자식 구성 요소   【Widget】", "【duration】 : 애니메이션 지속 시간   【Duration】", "【onEnd】 : 애니메이션 종료 콜백   【Function()】", "【curve】 : 애니메이션 곡선   【Duration】", "【top】 : 부모 상단까지의 거리   【double】", "【end】 : 부모 오른쪽까지의 거리   【double】", "【start】 : 부모 왼쪽까지의 거리   【double】", "【bottom】 : 부모 하단까지의 거리   【double】"]}]}