{"id": 339, "name": "DateRangePickerDialog", "localName": "日期范围", "info": "Material 风格的日期范围选择器，支持日历选择和输入选择。", "lever": 4, "family": 1, "linkIds": [135, 136, 137], "nodes": [{"file": "node1_base.dart", "name": "DateRangePickerDialog 基本使用", "desc": ["【firstDate】 : 最早日期   【DateTime】", "【lastDate】 : 最晚日期   【DateTime】", "【initialDateRange】 : 初始范围   【DateTimeRange?】", "【saveText】 : 保存文字  【String?】"]}, {"file": "node2_diy.dart", "name": "魔改 DateRangePickerDialog", "desc": ["修改 DateRangePickerDialog 源码，使得月份条目显示数值背景。"]}]}