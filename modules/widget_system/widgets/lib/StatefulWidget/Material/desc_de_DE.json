{"id": 160, "name": "Material", "localName": "Materialkomponente", "info": "Der führende Vertreter von Material-Stil-Komponenten, das Herzstück. Kann Attribute wie Farbe, Sc<PERSON>tentiefe, Typ, <PERSON><PERSON><PERSON>farbe, Form usw. festlegen.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von Material", "desc": ["【child】 : Untergeordnete Komponente   【Widget】", "【type】 : Typ   【MaterialType】", "【elevation】 : Sc<PERSON>tentiefe   【double】", "【shadowColor】 : Schattenfarbe   【Color】", "【color】 : Farbe   【Color】"]}, {"file": "node2_shape.dart", "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> von <PERSON>", "desc": ["【shape】 : Form   【ShapeBorder】,"]}]}