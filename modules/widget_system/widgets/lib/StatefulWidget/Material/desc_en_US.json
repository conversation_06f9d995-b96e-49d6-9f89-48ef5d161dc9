{"id": 160, "name": "Material", "localName": "Material Component", "info": "The leader and core of Material style components. You can specify properties such as color, elevation, type, shadow color, shape, etc.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Material", "desc": ["【child】 : Child Component   【Widget】", "【type】 : Type   【MaterialType】", "【elevation】 : Elevation   【double】", "【shadowColor】 : Shadow Color   【Color】", "【color】 : Color   【Color】"]}, {"file": "node2_shape.dart", "name": "Shape Property of Material", "desc": ["【shape】 : Shape   【ShapeBorder】,"]}]}