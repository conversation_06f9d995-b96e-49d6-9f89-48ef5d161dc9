{"id": 160, "name": "Material", "localName": "材料组件", "info": "Material风格组件的领军人物，灵魂核心。可指定颜色、影深、类型、阴影颜色、形状等属性。", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Material基本使用", "desc": ["【child】 : 子组件   【Widget】", "【type】 : 类型   【MaterialType】", "【elevation】 : 影深   【double】", "【shadowColor】 : 阴影颜色   【Color】", "【color】 : 颜色   【Color】"]}, {"file": "node2_shape.dart", "name": "Material的shape属性", "desc": ["【shape】 : 形状   【ShapeBorder】,"]}]}