{"id": 236, "name": "WidgetsApp", "localName": "Приложение виджетов", "info": "Собирает компоненты, необходимые для приложения, такие как маршрутизация, язык, некоторые отладочные переключатели и т.д. Также является основным компонентом для реализации MaterialApp и CupertinoApp.", "lever": 2, "family": 1, "linkIds": [65, 156], "nodes": [{"file": "node1_base.dart", "name": "Основное использование WidgetsApp", "desc": ["【pageRouteBuilder】 : *Конструктор маршрутов   【PageRouteFactory】", "【color】: *Цвет    【Color】", "【debugShowWidgetInspector】: Показывать ли инспектор виджетов   【bool】", "Другие свойства в основном совпадают с MaterialApp, подробнее см. там."]}]}