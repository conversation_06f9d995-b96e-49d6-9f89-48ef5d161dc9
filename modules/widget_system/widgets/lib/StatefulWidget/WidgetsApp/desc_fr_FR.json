{"id": 236, "name": "WidgetsApp", "localName": "Application de widgets", "info": "Rassemble les composants nécessaires pour une application, tels que le routage, la langue, certains commutateurs de débogage, etc. C'est également le composant central pour la mise en œuvre de MaterialApp et CupertinoApp.", "lever": 2, "family": 1, "linkIds": [65, 156], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de WidgetsApp", "desc": ["【pageRouteBuilder】 : *constructeur de route   【PageRouteFactory】", "【color】: *couleur    【Color】", "【debugShowWidgetInspector】: afficher l'inspecteur de widgets   【bool】", "Les autres propriétés sont essentiellement les mêmes que MaterialApp, voir cela pour plus de détails."]}]}