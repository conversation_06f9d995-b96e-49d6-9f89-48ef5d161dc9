{"id": 236, "name": "WidgetsApp", "localName": "위젯 앱", "info": "앱에 필요한 구성 요소, 예를 들어 라우팅, 언어, 일부 디버그 스위치 등을 모아놓은 것입니다. 또한 MaterialApp과 CupertinoApp을 구현하는 핵심 구성 요소입니다.", "lever": 2, "family": 1, "linkIds": [65, 156], "nodes": [{"file": "node1_base.dart", "name": "WidgetsApp 기본 사용법", "desc": ["【pageRouteBuilder】 : *라우트 빌더   【PageRouteFactory】", "【color】: *색상    【Color】", "【debugShowWidgetInspector】: 위젯 인스펙터 표시 여부   【bool】", "기타 속성은 기본적으로 MaterialApp과 동일하므로 자세한 내용은 해당 문서를 참조하세요."]}]}