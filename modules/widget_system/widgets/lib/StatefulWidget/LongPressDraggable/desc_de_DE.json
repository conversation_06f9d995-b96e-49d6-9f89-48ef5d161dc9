{"id": 105, "name": "LongPressDraggable", "localName": "<PERSON><PERSON>", "info": "Lassen Sie die Komponente bei langem Drücken auf der Oberfläche frei ziehen, und speichern Sie Daten eines generischen Typs T. Wird normalerweise in Kombination mit DragTarget verwendet, um den Zieheffekt zu erzielen.", "lever": 4, "family": 1, "linkIds": [103, 104], "nodes": [{"file": "node1_base.dart", "name": "LongPressDraggable mit DragTarget kombinieren", "desc": ["【child】 : Kind   【Widget】", "【feedback】 : <PERSON> be<PERSON>   【Widget】", "【axis】 : <PERSON><PERSON><PERSON> z<PERSON>   【Axis】", "【data】 : Daten   【T】", "【onDragStarted】 : <PERSON><PERSON><PERSON> beginnen   【Function()】", "【onDragEnd】 : <PERSON><PERSON><PERSON>den   【Function(DraggableDetails)】", "【onDragCompleted】 : Z<PERSON>hen abgeschlossen   【Function()】", "【onDraggableCanceled】 : <PERSON><PERSON><PERSON> abbrechen   【Function(Velocity,Offset)】"]}]}