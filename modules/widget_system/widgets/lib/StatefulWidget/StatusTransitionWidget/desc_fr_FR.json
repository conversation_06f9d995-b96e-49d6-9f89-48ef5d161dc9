{"id": 233, "name": "StatusTransitionWidget", "localName": "Widget de transition d'état", "info": "Classe abstraite qui peut déclencher un rafraîchissement en fonction des changements d'état de l'animateur fourni. Il n'y a pas de sous-classes implémentées au niveau du framework Flutter, ni de scénarios d'utilisation, ce qui semble peu utile.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introduction de StatusTransitionWidget", "desc": ["【animation】 : Sous-composant   【Animation<double>】", "<PERSON><PERSON>, ColorStatusTransitionWidget est personnalisé pour être utilisé, construisant différentes couleurs lorsque l'état de l'animateur change."]}]}