{"id": 233, "name": "StatusTransitionWidget", "localName": "ステータス遷移コンポーネント", "info": "抽象クラスで、提供されたアニメーターの状態変化に基づいて更新をトリガーできます。Flutterフレームワーク層には実装されたサブクラスも使用シナリオもなく、あまり役に立たないと感じます。", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "StatusTransitionWidget 紹介", "desc": ["【animation】 : サブコンポーネント   【Animation<double>】", "ここでカスタムColorStatusTransitionWidgetを使用し、アニメーターの状態が変化したときに異なる色を構築します。"]}]}