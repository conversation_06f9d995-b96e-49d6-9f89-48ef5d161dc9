{"id": 262, "name": "CupertinoSegmentedControl", "localName": "iOS многоколоночный переключатель", "info": "iOS-стиль многокнопочной панели, поведение аналогично ToggleButtons, можно указать такие свойства, как внутренние отступы, цвет выделения, цвет границы и т.д.", "lever": 4, "family": 1, "linkIds": [33, 256], "nodes": [{"file": "node1_base.dart", "name": "Основное использование вкладок iOS", "desc": ["【children】 : Ка<PERSON><PERSON><PERSON> компонентов   【Map<T, Widget>】", "【onValueChanged】 : Обратный вызов изменения значения   【ValueChanged<T>】", "【groupValue】 : Выбранное значение   【T】", "【padding】 : Внутренние отступы   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "Цвета CupertinoSegmentedControl", "desc": ["【unselectedColor】 : Цвет невыделенного   【Color】", "【selectedColor】 : Цвет выделенного   【Color<T>】", "【pressedColor】 : Цвет нажатия   【Color】", "【borderColor】 : Цвет границы   【Color】"]}]}