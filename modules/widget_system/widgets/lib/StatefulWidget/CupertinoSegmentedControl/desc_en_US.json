{"id": 262, "name": "CupertinoSegmentedControl", "localName": "iOS Multi-Tab Switch", "info": "iOS-style multi-button bar, similar in behavior to ToggleButtons, with customizable properties such as padding, selected color, border color, etc.", "lever": 4, "family": 1, "linkIds": [33, 256], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of iOS Tabs", "desc": ["【children】: Component Map 【Map<T, Widget>】", "【onValueChanged】: Value Change Callback 【ValueChanged<T>】", "【groupValue】: Selected Value 【T】", "【padding】: Padding 【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "Colors of CupertinoSegmentedControl", "desc": ["【unselectedColor】: Unselected Color 【Color】", "【selectedColor】: Selected Color 【Color<T>】", "【pressedColor】: Pressed Color 【Color】", "【borderColor】: Border Color 【Color】"]}]}