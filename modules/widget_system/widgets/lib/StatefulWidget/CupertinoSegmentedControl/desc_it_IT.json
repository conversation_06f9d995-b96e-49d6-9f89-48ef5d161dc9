{"id": 262, "name": "CupertinoSegmentedControl", "localName": "Interruttore a segmenti iOS", "info": "Barra a pulsanti multipli in stile iOS, simile a ToggleButtons, con proprietà come padding, colore selezionato, colore del bordo, ecc.", "lever": 4, "family": 1, "linkIds": [33, 256], "nodes": [{"file": "node1_base.dart", "name": "Uso di base delle schede iOS", "desc": ["【children】 : Mappa dei componenti   【Map<T, Widget>】", "【onValueChanged】 : Callback per il cambio di valore   【ValueChanged<T>】", "【groupValue】 : <PERSON><PERSON>   【T】", "【padding】 : Padding interno   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "Colori di CupertinoSegmentedControl", "desc": ["【unselectedColor】 : Colore non selezionato   【Color】", "【selectedColor】 : Colore selezionato   【Color<T>】", "【pressedColor】 : Colore premuto   【Color】", "【borderColor】 : Colore del bordo   【Color】"]}]}