{"id": 63, "name": "CupertinoTabBar", "localName": "iOSタブ", "info": "iOSスタイルのTabBarで、通常CupertinoTabScaffoldで使用されます。色、アイコンサイズ、ボーダーなどのデータを指定できます。アイテムのクリックイベントを受け取ります。", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoTabBarの基本使い方", "desc": ["【currentIndex】 : 現在のアクティブインデックス   【Widget】", "【items】 : アイテムコンポーネント   【Widget】", "【backgroundColor】 : 背景色   【Color】", "【inactiveColor】 : 非アクティブ色   【Color】", "【activeColor】 : アクティブ色   【Color】", "【iconSize】 : アイコンサイズ    【double】", "【border】 : ボーダー   【Border】", "【onTap】 : クリックイベント   【Function(int)】"]}]}