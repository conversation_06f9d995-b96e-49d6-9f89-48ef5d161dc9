{"id": 63, "name": "CupertinoTabBar", "localName": "Schede iOS", "info": "Una TabBar in stile iOS, solitamente utilizzata in CupertinoTabScaffold. È possibile specificare colore, dimensione delle icone, bordi, ecc. Riceve gli eventi di click sugli item.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilizzo di base di CupertinoTabBar", "desc": ["【currentIndex】 : Indice attualmente attivo   【Widget】", "【items】 : Componenti degli elementi   【Widget】", "【backgroundColor】 : Colore di sfondo   【Color】", "【inactiveColor】 : Colore inattivo   【Color】", "【activeColor】 : Colore attivo   【Color】", "【iconSize】 : Dimensione dell'icona    【double】", "【border】 : Bordo   【Border】", "【onTap】 : Evento di click   【Function(int)】"]}]}