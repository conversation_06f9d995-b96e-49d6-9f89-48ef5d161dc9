{"id": 39, "name": "Checkbox", "localName": "Casella di controllo", "info": "Componente casella di controllo, comunemente utilizzato per l'alternanza delle configurazioni, può specificare il colore, ricevere callback di cambio di stato e può anche specificare tre stati.", "lever": 4, "family": 1, "linkIds": [326, 17], "nodes": [{"file": "node1_base.dart", "name": "Uso di base della casella di controllo", "desc": ["【value】 : Se è selezionato   【double】", "【checkColor】: Colore del segno di spunta quando selezionato 【Color】", "【activeColor】: Colore all'interno del riquadro quando selezionato 【Color】", "【onChanged】: Evento di cambio di stato 【Function(bool)】,"]}, {"file": "node2_tristate.dart", "name": "Tre stati della casella di controllo", "desc": ["【tristate】 : Se è a tre stati   【double】", "  <PERSON>uan<PERSON> on<PERSON><PERSON>ed,"]}]}