{"id": 150, "name": "InkWell", "localName": "Onda de Água", "info": "Subclasse de InkResponse, com propriedades básicas semelhantes ao InkResponse. Uma área retangular de onda de água, que pode conhecer o raio de curvatura, a forma da borda, etc.", "lever": 4, "family": 1, "linkIds": [149, 152], "nodes": [{"file": "node1_base.dart", "name": "Eventos Básicos do InkWell", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【onTap】 : Evento de clique   【Function()】", "【onDoubleTap】 : Evento de duplo clique   【Function()】", "【onTapCancel】 : Cancelamento de clique   【Function()】", "【onLongPress】 : Evento de pressionar longo   【Function()】"]}, {"file": "node2_color.dart", "name": "Outras Propriedades do InkWell", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【onHighlightChanged】 : Callback de mudança de destaque   【Function(bool)】", "【highlightColor】 : Cor de destaque   【Color】", "【splashColor】 : <PERSON><PERSON> da onda de água   【Color】", "【radius】 : <PERSON><PERSON> da onda   【double】"]}]}