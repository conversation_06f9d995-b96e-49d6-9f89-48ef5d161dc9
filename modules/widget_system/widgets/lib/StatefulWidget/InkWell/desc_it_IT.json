{"id": 150, "name": "InkWell", "localName": "Onda d'a<PERSON>qua", "info": "Sottoclasse di InkResponse, le proprietà di base sono le stesse di InkResponse. Un'area rettangolare con un'effetto onda d'acqua, può conoscere il raggio degli angoli, la forma del bordo, ecc.", "lever": 4, "family": 1, "linkIds": [149, 152], "nodes": [{"file": "node1_base.dart", "name": "Eventi di base di InkWell", "desc": ["【child】 : Componente figlio   【Widget】", "【onTap】 : Evento di clic   【Function()】", "【onDoubleTap】 : Evento di doppio clic   【Function()】", "【onTapCancel】 : Annullamento del clic   【Function()】", "【onLongPress】 : Evento di pressione prolungata   【Function()】"]}, {"file": "node2_color.dart", "name": "Altre proprietà di InkWell", "desc": ["【child】 : Componente figlio   【Widget】", "【onHighlightChanged】 : Callback di cambiamento dell'evidenziazione   【Function(bool)】", "【highlightColor】 : Colore di evidenziazione   【Color】", "【splashColor】 : Colore dell'onda d'acqua   【Color】", "【radius】 : Raggio dell'onda   【double】"]}]}