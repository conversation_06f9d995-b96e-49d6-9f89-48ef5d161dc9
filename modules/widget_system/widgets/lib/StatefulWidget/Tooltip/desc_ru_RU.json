{"id": 50, "name": "<PERSON><PERSON><PERSON>", "localName": "Всплывающая подсказка", "info": "Компонент для отображения всплывающих подсказок, информация отображается при длительном нажатии. Можно указать отступы, время отображения, стиль текста, свойства декорации.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Tooltip", "desc": ["【preferBelow】 : Предпочтительно снизу   【bool】", "【padding】 : Внутренние отступы   【EdgeInsetsGeometry】", "【margin】 : Внешние отступы   【EdgeInsetsGeometry】", "【message】 : Содержание сообщения   【String】", "【showDuration】 : Время отображения   【Duration】", "【waitDuration】 : Время появления при наведении   【Duration】", "【child】 : До<PERSON><PERSON><PERSON><PERSON>ий элемент   【Widget】"]}, {"file": "node2_decoration.dart", "name": "Декорация Tooltip", "desc": ["【decoration】 : Объект декорации 【Decoration】", "【textStyle】 : Стиль текста   【double】"]}]}