{"id": 50, "name": "<PERSON><PERSON><PERSON>", "localName": "Hinweiswerkzeug", "info": "Eine Komponente zur Anzeige von Hinweistexten, die bei längerem Drücken Informationen anzeigt. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>uer, Textstil und Dekorationseigenschaften festlegen.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>", "desc": ["【preferBelow】 : <PERSON><PERSON><PERSON><PERSON> unten   【bool】", "【padding】 : Innenabstand   【EdgeInsetsGeometry】", "【margin】 : Außenabstand   【EdgeInsetsGeometry】", "【message】 : Na<PERSON>richteninhalt   【String】", "【showDuration】 : <PERSON><PERSON>igedauer   【Duration】", "【waitDuration】 : Schwebeerscheinungszeit   【Duration】", "【child】 : Kind   【Widget】"]}, {"file": "node2_decoration.dart", "name": "<PERSON><PERSON><PERSON> <PERSON>", "desc": ["【decoration】 : Dekorationsobjekt 【Decoration】", "【textStyle】 : Textstil   【double】"]}]}