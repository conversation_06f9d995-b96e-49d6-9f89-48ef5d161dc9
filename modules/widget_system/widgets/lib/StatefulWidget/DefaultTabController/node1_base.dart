import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2020/9/21
/// contact me <NAME_EMAIL>


class DefaultTabControllerDemo extends StatelessWidget {
  final List<Tab> tabs = const [
    Tab(text: '青眼白龙'),
    Tab(text: '黑魔术师'),
    Tab(text: '混沌战士'),
  ];

  const DefaultTabControllerDemo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: DefaultTabController(
        length: tabs.length,
        child: Scaffold(
          appBar: AppBar(
            title: const Text("DefaultTabController"),
            bottom: TabBar(
              tabs: tabs,
            ),
          ),
          body: TabBarView(
            children: tabs.map((Tab tab) {
              return Center(
                child: Text(
                  '${tab.text}',
                  style: const TextStyle(fontSize: 20),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}
