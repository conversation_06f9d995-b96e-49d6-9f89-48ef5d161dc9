{"id": 230, "name": "DefaultTabController", "localName": "Controlador de pestañas predeterminado", "info": "Al usar TabBar y TabBarView, se necesita el mismo controlador para controlar las pestañas y las páginas. DefaultTabController proporciona un controlador predeterminado cuando no se especifica uno, simplificando su uso.", "lever": 3, "family": 1, "linkIds": [148, 59], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de DefaultTabController", "desc": ["【length】 : Número de pestañas   【int】", "【initialIndex】 : Índice inicial de la pestaña   【int】", "【child】 : Componente   【Widget】"]}]}