{"id": 135, "name": "MonthPicker", "localName": "月選択器", "info": "月の選択コンポーネントで、前後の月への切り替えを監視します。選択可能な日付範囲や選択された日付を指定でき、日付選択イベントを受け取ります。", "lever": 3, "family": 1, "deprecated": -1, "linkIds": [134, 136], "nodes": [{"file": "node1_base.dart", "name": "MonthPickerの基本使用", "desc": ["【selectedDate】 : 選択された日付   【DateTime】", "【firstDate】 : 最初の日付制限   【DateTime】", "【lastDate】 : 最後の日付制限   【DateTime】", "【onChanged】 : クリックコールバック  【Function(DateTime)】", "    ", "class CustomMonthPicker extends StatelessWidget{", "  const CustomMonthPicker({Key? key) : super(key: key);", "", "  final String info =", "      'MonthPicker 月選択器は Flutter3.0 で歴史の舞台から退場しました。代替として CalendarDatePicker カレンダー選択器が登場しました。';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", "   final DateTime _date = DateTime.now();", "  ", "   @override", "   Widget build(BuildContext context) {", "     return SizedBox(", "       height: 350,", "       child: <PERSON><PERSON><PERSON>(", "         selectedDate: _date,", "         onChanged: (date) => setState(() => _date = date),", "         firstDate: DateTime(2018),", "         lastDate: DateTime(2030),", "       ),"]}]}