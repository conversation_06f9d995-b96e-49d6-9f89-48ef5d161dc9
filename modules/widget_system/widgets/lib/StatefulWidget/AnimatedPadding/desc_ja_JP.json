{"id": 119, "name": "AnimatedPadding", "localName": "パディングアニメーション", "info": "子コンポーネントにPadding（パディング）アニメーションを行わせることができ、時間と曲線を指定でき、アニメーション終了イベントがあります。", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedPaddingの基本的な使用", "desc": ["【child】 : 子コンポーネント   【Widget】", "【duration】 : アニメーション時間   【Duration】", "【onEnd】 : アニメーション終了コールバック   【Function()】", "【curve】 : アニメーション曲線   【Duration】", "【padding】 : パディング   【EdgeInsetsGeometry】"]}]}