{"id": 119, "name": "AnimatedPadding", "localName": "Анимация отступов", "info": "Позволяет дочерним компонентам анимировать отступы (Padding), можно указать длительность и кривую, есть событие завершения анимации.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование AnimatedPadding", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【duration】 : Длительность анимации   【Duration】", "【onEnd】 : Обратный вызов завершения анимации   【Function()】", "【curve】 : Кривая анимации   【Duration】", "【padding】 : Внутренние отступы   【EdgeInsetsGeometry】"]}]}