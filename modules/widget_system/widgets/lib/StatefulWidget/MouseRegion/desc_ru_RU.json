{"id": 293, "name": "MouseRegion", "localName": "Область мыши", "info": "Компонент для отслеживания событий мыши, обычно используется на платформах для настольных компьютеров и веб-платформах, может отслеживать события входа, выхода и перемещения мыши.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование MouseRegion", "desc": ["【onEnter】: Событие входа   【PointerEnterEventListener】", "【onHover】: Событие перемещения    【PointerHoverEventListener】", "【onExit】: Событие выхода    【PointerExitEventListener】"]}]}