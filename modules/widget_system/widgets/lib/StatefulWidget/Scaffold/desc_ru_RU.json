{"id": 64, "name": "Scaffold", "localName": "Леса", "info": "Универсальная структура приложения, включающая верхнюю, нижнюю, левую, правую, центральную части и плавающую кнопку, в соответствующих местах могут размещаться компоненты.", "lever": 4, "family": 1, "linkIds": [57, 60, 61], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Scaffold", "desc": ["【appBar】 : Верхний компонент   【PreferredSizeWidget】", "【bottomNavigationBar】 : Нижний компонент   【Widget】", "【drawer】 : Левый выдвижной компонент   【Widget】", "【endDrawer】 : Правый выдвижной компонент   【Widget】", "【body】 : Основной компонент   【Widget】", "【backgroundColor】 : Цвет фона   【Color】", "【floatingActionButton】 : Плавающая кнопка   【Widget】", "【floatingActionButtonLocation】 : Расположение плавающей кнопки   【FloatingActionButtonLocation】"]}]}