{"id": 282, "name": "Focus", "localName": "포커스 컴포넌트", "info": "[FocusNode]를 관리하는 컴포넌트로, 키보드 포커스가 해당 위젯 및 하위 트리 노드에 부여될 수 있도록 합니다.", "lever": 5, "family": 1, "linkIds": [283, 284], "nodes": [{"file": "node1.dart", "name": "FocusNode 포커스와 포커스 해제", "desc": ["【focusNode】 : 포커스   【FocusNode?】", "【onFocusChange】 : 포커스 변화 리스너   【ValueChanged<bool>?】", "【child】 : 자식 컴포넌트   【Widget】"]}, {"file": "node2.dart", "name": "자동 포커스", "desc": ["【autofocus】 : 자동 포커스 여부   【bool】", "Flutter 프레임워크는 포커스 전환을 위한 단축키를 내장하고 있습니다. 예를 들어, 가로 스크롤 목록에서 ←, →는 각각 이전 항목과 다음 항목에 포커스를 줍니다. Tab 키는 다음 Focus 노드에 포커스를 줄 수 있습니다."]}, {"file": "node3.dart", "name": "키보드 이벤트 콜백", "desc": ["【onKeyEvent】 : 키보드 이벤트 콜백   【FocusOnKeyEventCallback?】"]}]}