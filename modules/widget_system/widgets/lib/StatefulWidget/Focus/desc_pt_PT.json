{"id": 282, "name": "Focus", "localName": "Componente de Foco", "info": "Um componente que gerencia [FocusNode], usado para permitir que o foco do teclado seja atribuído a este widget e aos nós da sua subárvore.", "lever": 5, "family": 1, "linkIds": [283, 284], "nodes": [{"file": "node1.dart", "name": "Foco e Desfoco do FocusNode", "desc": ["【focusNode】 : Foco   【FocusNode?】", "【onFocusChange】 : Monitoramento de mudança de foco   【ValueChanged<bool>?】", "【child】 : Compo<PERSON><PERSON> filho   【Widget】"]}, {"file": "node2.dart", "name": "Foco Automático", "desc": ["【autofocus】 : Foco automático   【bool】", "O framework Flutter possui atalhos de teclado embutidos para alternar o foco, como ← e → em listas de deslizamento horizontal para focar o item anterior e o próximo. A tecla Tab pode focar o próximo nó Focus."]}, {"file": "node3.dart", "name": "Callback de Evento de Teclado", "desc": ["【onKeyEvent】 : Callback de evento de teclado   【FocusOnKeyEventCallback?】"]}]}