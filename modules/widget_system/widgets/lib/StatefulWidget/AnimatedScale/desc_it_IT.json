{"id": 249, "name": "AnimatedScale", "localName": "Animazione di Scala", "info": "Quando la quantità di scala data cambia, il componente figlio può regolarsi automaticamente rispetto al valore di scala, con un'animazione tra i valori precedenti e successivi.", "lever": 3, "family": 1, "linkIds": [120, 247, 201], "nodes": [{"file": "node1.dart", "name": "Effetto di Animazione di Scala", "desc": ["In questo caso, trascina per regolare il parametro scale e osserva l'effetto di movimento dell'animazione.", "【child】 : Componente figlio   【Widget】", "【duration】 : Durata dell'animazione   【Duration】", "【onEnd】 : Callback alla fine dell'animazione   【Function()】", "【curve】 : Curva dell'animazione   【Duration】", "【alignment】 : Centro di trasformazione dell'animazione   【Alignment】", "【filterQuality】 : Qualità del filtro   【FilterQuality】", "【scale】 : Quantità di scala   【double】"]}]}