{"id": 93, "name": "PositionedTransition", "localName": "Transição de Posição", "info": "Só pode ser usado em um<PERSON>ack, pode conter um componente filho, permitindo que ele faça uma animação de posição entre dois retângulos, é necessário fornecer um animador rect.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do PositionedTransition", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【rect】 : Animação   【Animation<RelativeRect>】", "    O componente PositionedTransition só funciona dentro de uma Stack"]}]}