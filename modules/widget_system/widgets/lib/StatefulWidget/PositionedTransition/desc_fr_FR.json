{"id": 93, "name": "PositionedTransition", "localName": "Transition de position", "info": "Ne peut être utilisé que dans une Stack, peut contenir un seul composant enfant, permettant une animation de position entre deux rectangles, nécessite un animateur rect.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de PositionedTransition", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【rect】 : Animation   【Animation<RelativeRect>】", "    Le composant PositionedTransition ne fonctionne que dans une Stack"]}]}