{"id": 103, "name": "Draggable", "localName": "Composant déplaçable", "info": "Permet de déplacer un composant librement sur l'interface, peut contenir des données de type générique T. Généralement utilisé en combinaison avec DragTarget pour réaliser des effets de glisser-déposer.", "lever": 4, "family": 1, "linkIds": [104, 105], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de Draggable", "desc": ["【child】 : enfant   【Widget】", "【feedback】 : enfant pendant le glissement   【Widget】", "【axis】 : axe de glissement   【Axis】"]}, {"file": "node2_data.dart", "name": "Utilisation combinée de Draggable et DragTarget", "desc": ["【data】 : donn<PERSON>   【T】", "【onDragStarted】 : début du glissement   【Function()】", "【onDragEnd】 : fin du glissement   【Function(DraggableDetails)】", "【onDragCompleted】 : glissement terminé   【Function()】", "【onDraggableCanceled】 : glissement annulé   【Function(Velocity,Offset)】", "【onChanged】 : rappel lors du changement   【Function(T)】"]}, {"file": "node3_use.dart", "name": "Autres utilisations de Draggable", "desc": ["Peut traiter certains événements en fonction du glissement. Comme la suppression, la recherche, les boîtes de dialogue, etc."]}]}