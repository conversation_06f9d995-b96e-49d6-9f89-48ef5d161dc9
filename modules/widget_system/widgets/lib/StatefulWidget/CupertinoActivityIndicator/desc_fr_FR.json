{"id": 48, "name": "CupertinoActivityIndicator", "localName": "Indicateur iOS", "info": "Composant d'affichage de chargement de style iOS, peut spécifier le rayon et s'il tourne.", "lever": 2, "family": 1, "linkIds": [46, 47], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de CupertinoActivityIndicator", "desc": ["【animating】 : Si l'animation de chargement est active   【bool】", "【radius】 : Rayon   【double】"]}]}