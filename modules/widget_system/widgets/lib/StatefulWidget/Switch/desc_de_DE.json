{"id": 40, "name": "Switch", "localName": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, häufig für Konfigurationswechsel verwendet, kann die Farbe des kleinen Kreises, Bilder, Farbe der Gleitschiene usw. angeben, empfängt Rückrufe bei Zustandsänderungen.", "lever": 4, "family": 1, "linkIds": [41, 18], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>", "desc": ["【inactiveThumbColor】 : Farbe des nicht ausgewählten kleinen Kreises   【Color】", "【inactiveTrackColor】 : Farbe der nicht ausgewählten Gleitschiene   【Color】", "【activeColor】 : Farbe des ausgewählten kleinen Kreises   【Color】", "【activeTrackColor】 : Farbe der ausgewählten Gleitschiene   【Color】", "【onChanged】 : Umschalt-Rückruf   【Function(double)】", "<PERSON><PERSON> onChanged werden drei Zustände zurückgerufen: true, null, false"]}, {"file": "node2_image.dart", "name": "Switch Bild", "desc": ["【inactiveThumbImage】 : Bild des nicht ausgewählten kleinen Kreises   【ImageProvider】", "【activeThumbImage】 : Bild des ausgewählten kleinen Kreises   【ImageProvider】"]}]}