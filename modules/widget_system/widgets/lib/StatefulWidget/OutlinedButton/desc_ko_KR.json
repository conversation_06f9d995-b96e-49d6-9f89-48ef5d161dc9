{"id": 355, "name": "OutlinedButton", "localName": "테두리 버튼", "info": "Material 스타일의 테두리 버튼으로, OutlineButton과 유사하게 동작합니다. 스타일을 통해 테두리, 색상, 그림자 등의 속성을 변경할 수 있습니다.", "lever": 3, "family": 1, "linkIds": [353, 27, 354], "nodes": [{"file": "node1_base.dart", "name": "OutlinedButton 기본 사용법", "desc": ["【child】 : 버튼 내용   【Widget】", "【onPressed】 : 클릭 이벤트   【VoidCallback】", "【onLongPress】 : 길게 누르기 이벤트   【VoidCallback】"]}, {"file": "node2_style.dart", "name": "OutlinedButton 스타일", "desc": ["【style】 : 버튼 스타일   【ButtonStyle】", "【focusNode】 : 포커스   【FocusNode】", "【clipBehavior】 : 클리핑 동작   【Clip】", "【autofocus】 : 자동 포커스   【bool】"]}]}