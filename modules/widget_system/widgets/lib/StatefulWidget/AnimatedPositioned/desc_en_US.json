{"id": 121, "name": "AnimatedPositioned", "localName": "Positioning Animation", "info": "Allows child components to perform Positioned (positioning) animations, with specified duration and curves, and has an animation end event. Can only be used within a Stack.", "lever": 3, "family": 1, "linkIds": [108, 93, 122], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of AnimatedPositioned", "desc": ["【child】: Child component   【Widget】", "【duration】: Animation duration   【Duration】", "【onEnd】: Animation end callback   【Function()】", "【curve】: Animation curve   【Duration】", "【top】: Distance to parent top   【double】", "【right】: Distance to parent right   【double】", "【left】: Distance to parent left   【double】", "【bottom】: Distance to parent bottom   【double】"]}]}