{"id": 121, "name": "AnimatedPositioned", "localName": "Positionierte Animation", "info": "Ermöglicht die Positionierungsanimation von Unterkomponenten, kann <PERSON> und Ku<PERSON> angeben und hat ein Ereignis zum Ende der Animation. Kann nur in einem Stack verwendet werden.", "lever": 3, "family": 1, "linkIds": [108, 93, 122], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AnimatedPositioned", "desc": ["【child】 : Kindkomponente   【Widget】", "【duration】 : Animationsdauer   【Duration】", "【onEnd】 : Rückruf am Ende der Animation   【Function()】", "【curve】 : Animationskurve   【Duration】", "【top】 : Abstand zum oberen Rand des Elternteils   【double】", "【right】 : Abstand zum rechten Rand des Elternteils   【double】", "【left】 : Abstand zum linken Rand des Elternteils   【double】", "【bottom】 : Abstand zum unteren Rand des Elternteils   【double】"]}]}