{"id": 121, "name": "AnimatedPositioned", "localName": "定位动画", "info": "能让子组件进行Positioned(定位)动画，可指定时长和曲线，有动画结束事件。只能用于Stack之中。", "lever": 3, "family": 1, "linkIds": [108, 93, 122], "nodes": [{"file": "node1_base.dart", "name": "AnimatedPositioned基本使用", "desc": ["【child】 : 孩子组件   【Widget】", "【duration】 : 动画时长   【Duration】", "【onEnd】 : 动画结束回调   【Function()】", "【curve】 : 动画曲线   【Duration】", "【top】 : 到父顶距离   【double】", "【right】 : 到父右距离   【double】", "【left】 : 到父左距离   【double】", "【bottom】 : 到父底距离   【double】"]}]}