{"id": 117, "name": "AnimatedList", "localName": "Lista animada", "info": "Versión mejorada de ListView, permite animar los ítems. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, animaciones al añadir o eliminar ítems.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de AnimatedList", "desc": ["【itemBuilder】: Constructor de componentes 【AnimatedListItemBuilder】", "【initialItemCount】: Número de componentes hijos 【int】", "【scrollDirection】: Dirección de desplazamiento 【Axis】", "【controller】: Controlador de desplazamiento 【ScrollController】", "【reverse】: Si los datos están invertidos 【bool】", "【padding】: <PERSON><PERSON><PERSON> interno 【EdgeInsetsGeometry】"]}]}