{"id": 139, "name": "CupertinoPicker", "localName": "iOS селектор", "info": "Высококлассный цилиндрический слайдер-селектор, очень изысканный, может быть настроен с множеством свойств, принимает события при выборе во время скольжения.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование CupertinoPicker", "desc": ["【children】 : С<PERSON>ис<PERSON><PERSON> дочерних компонентов   【List<Widget>】", "【offAxisFraction】 : Коэффициент смещения оси   【double】", "【squeeze】 : Коэффициент сжатия   【double】", "【diameterRatio】 : Соотношение высоты к диаметру цилиндра   【double】", "【itemExtent】 : Расстояние между элементами   【double】", "【backgroundColor】 : Цвет фона   【Color】", "【onSelectedItemChanged】 : Событие выбора  【Function(int)】"]}]}