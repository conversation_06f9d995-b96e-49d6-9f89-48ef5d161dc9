{"id": 358, "name": "NavigationRail", "localName": "Barra de Navegação Lateral", "info": "Barra de navegação lateral, geralmente usada para menus de navegação em desktop. Suporta áreas expansíveis e retráteis, com componentes especificáveis nas posições inicial e final.", "lever": 4, "family": 1, "linkIds": [60, 61], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do NavigationRail", "desc": ["【destinations】 : Lista de dados do menu   【List<NavigationRailDestination>】", "【selectedIndex】: Índice ativo    【int】", "【labelType】: Estilo da etiqueta    【NavigationRailLabelType?】", "【onDestinationSelected】: Evento de clique no menu    【ValueChanged<int>?】"]}, {"file": "node2_extend.dart", "name": "Efeito de Recolhimento do NavigationRail", "desc": ["【elevation】 : Profundidade da sombra   【double】", "【leading】: Componente inicial    【Widget?】", "【trailing】: Componente final    【Widget?】", "【extended】: Expandido    【bool】"]}, {"file": "node3_dark.dart", "name": "Estilo do NavigationRail", "desc": ["【useIndicator】 : Mostrar indicador   【bool】", "【indicatorColor】: Cor do indicador    【Color?】", "【backgroundColor】: Cor de fundo    【Color?】", "【labelType】: Estilo da etiqueta    【NavigationRailLabelType?】", "【selectedIconTheme】: Estilo do ícone selecionado    【IconThemeData?】", "【unselectedIconTheme】: Estilo do ícone não selecionado    【IconThemeData?】", "【selectedLabelTextStyle】: Estilo do texto selecionado    【TextStyle?】", "【unselectedLabelTextStyle】: Estilo do texto não selecionado    【TextStyle?】", "【minExtendedWidth】: <PERSON><PERSON><PERSON> expandida    【double?】", "【minWidth】: <PERSON><PERSON><PERSON> não expandida    【double?】"]}]}