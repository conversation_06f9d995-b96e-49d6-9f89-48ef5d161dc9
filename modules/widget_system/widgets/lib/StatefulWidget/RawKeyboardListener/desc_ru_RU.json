{"id": 254, "name": "RawKeyboardListener", "localName": "Слушатель клавиатуры", "info": "Может использоваться для обнаружения событий нажатия и отпускания клавиш на клавиатуре. В настоящее время может обнаруживать только физические клавиатуры и может использоваться на настольных устройствах.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование RawGestureDetector", "desc": ["【onKey】 : Событие клавиатуры   【ValueChanged<RawKeyEvent>】", "【focusNode】 : Фокус   【FocusNode】", "【autofocus】 : Автоматическая фокусировка   【bool】", "【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】"]}]}