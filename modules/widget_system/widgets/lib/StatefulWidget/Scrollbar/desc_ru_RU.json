{"id": 194, "name": "Sc<PERSON><PERSON>", "localName": "Ползунок прокрутки", "info": "Необходимо обернуть прокручиваемую область, при прокрутке будет отображаться ползунок для индикации.", "lever": 3, "family": 1, "linkIds": [195, 164, 162], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Scrollbar", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【controller】 : Контро<PERSON><PERSON>ер  【ScrollController】"]}]}