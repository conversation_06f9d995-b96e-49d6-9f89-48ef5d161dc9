{"id": 112, "name": "SlideTransition", "localName": "Slide Transition", "info": "A subclass of AnimatedWidget, using an animator of type Offset to allow child components to transition between two Offset objects.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of SlideTransition", "desc": ["【child】: Child component   【Widget】", "【textDirection】: X-axis direction   【TextDirection】", "【position】: Animation   【Animation<Offset>】"]}]}