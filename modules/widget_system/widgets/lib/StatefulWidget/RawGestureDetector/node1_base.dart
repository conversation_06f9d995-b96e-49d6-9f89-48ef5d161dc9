
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2020/9/21
/// contact me <NAME_EMAIL>


class RawGestureDetectorDemo extends StatefulWidget {
  const RawGestureDetectorDemo({Key? key}) : super(key: key);

  @override
  _RawGestureDetectorDemoState createState() => _RawGestureDetectorDemoState();
}

class _RawGestureDetectorDemoState extends State<RawGestureDetectorDemo> {
  String _last = "";

  @override
  Widget build(BuildContext context) {
    return RawGestureDetector(
      gestures: <Type, GestureRecognizerFactory>{
        TapGestureRecognizer:
            GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
          () => TapGestureRecognizer(),
          init,
        ),
      },
      child: Container(
          width: 300.0,
          height: 100.0,
          alignment: Alignment.center,
          color: Colors.yellow,
          child: Text(_last)),
    );
  }

  void init(TapGestureRecognizer instance) {
    instance..onTapDown = (TapDownDetails details) {
        setState(() {
          _last = 'down';
        });
      }
      ..onTapUp = (TapUpDetails details) {
        setState(() {
          _last = 'up';
        });
      }
      ..onTap = () {
        setState(() {
          _last = 'tap';
        });
      }
      ..onTapCancel = () {
        setState(() {
          _last = 'cancel';
        });
      }
      ;
  }
}
