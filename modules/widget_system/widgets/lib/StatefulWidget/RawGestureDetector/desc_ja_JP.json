{"id": 248, "name": "RawGestureDetector", "localName": "生のジェスチャー検出器", "info": "指定されたジェスチャーファクトリの記述に基づいてジェスチャーを検出するために使用できます。独自のジェスチャー認識器を開発する際に非常に便利です。一般的なジェスチャーには、GestureRecognizerを使用してください。", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RawGestureDetectorの基本的な使用法", "desc": ["【behavior】 : 検出動作   【HitTestBehavior】", "【gestures】 : ジェスチャーマッピング   【Map<Type, GestureRecognizerFactory>】", "【child】 : 子ウィジェット   【Widget】"]}]}