{"id": 198, "name": "Form", "localName": "Componente de Formulário", "info": "Componente de formulário, pode receber retornos de chamada de alterações dos componentes FormField abaixo dele, interceptar o retorno da página através de onWillPop, e salvar ou validar os campos do formulário através de FormState.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do Form", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【onChanged】 : Retorno de chamada de alteração do formulário   【VoidCallback】", "【onWillPop】 : Retorno de chamada de retorno  【WillPopCallback】"]}]}