{"id": 54, "name": "TextField", "localName": "Campo de entrada", "info": "Componente para entrada de dados, possui propriedades complexas. Pode especificar controlador, estilo de texto, decoração de linha, limite de linhas, estilo do cursor, etc. Recebe eventos de mudança de entrada, conclusão de entrada, etc.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do TextField", "desc": ["【controller】 : Controlador   【TextEditingController】", "【style】 : <PERSON><PERSON><PERSON> de texto   【TextStyle】", "【decoration】 : Decoração de linha   【InputDecoration】", "【onEditingComplete】 : Evento de conclusão de entrada   【Function()】", "【onSubmitted】 : Evento de submissão   【Function(String)】", "【onChanged】 : Evento de entrada   【Function(String)】"]}, {"file": "node2_cursor.dart", "name": "Número de linhas e cursor do TextField", "desc": ["【minLines】 : <PERSON><PERSON><PERSON><PERSON> mín<PERSON> de <PERSON>   【int】", "【maxLines】 : Número máximo de linhas   【int】", "【cursorRadius】 : <PERSON><PERSON> do cursor   【Radius】", "【cursorColor】 : Cor do cursor   【Color】", "【cursorWidth】 : <PERSON><PERSON><PERSON> do cursor   【double】", "【showCursor】 : Mostrar cursor   【bool】", "【autofocus】 : Foco automático   【bool】"]}, {"file": "node3_decoration.dart", "name": "Decoração complexa do decoration", "desc": ["InputDecoration tem muitos pontos de decoração, correspondentes ao código:", "border: Relacionado à borda", "helper: Dica relacionada ao canto inferior esquerdo", "counter: Dica relacionada ao canto inferior direito", "prefix: Extremidade esquerda interna do campo de entrada", "suffix: Extremidade direita interna do campo de entrada", "label: Texto sem foco", "label: Texto sem foco", "hint: Texto de dica relacionado", "border: Relacionado à borda"]}]}