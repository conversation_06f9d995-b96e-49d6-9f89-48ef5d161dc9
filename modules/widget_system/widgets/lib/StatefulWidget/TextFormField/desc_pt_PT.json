{"id": 199, "name": "TextFormField", "localName": "Campo de Entrada de Texto", "info": "As propriedades são basicamente as mesma<PERSON> que o TextField, com a adição de validação de campo e callbacks de envio. O save do FormState irá acionar o callback onSaved.", "lever": 4, "family": 1, "linkIds": [54, 198], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do TextFormField", "desc": ["As propried<PERSON> b<PERSON><PERSON><PERSON> as mesma<PERSON> que o TextField, consulte-o para mais de<PERSON>hes", "【validator】: Função de validação 【FormFieldValidator<String>】", "【onFieldSubmitted】: Callback de envio 【ValueChanged<String>】", "【onSaved】: Callback quando o formulário é salvo 【FormFieldSetter<String>】"]}]}