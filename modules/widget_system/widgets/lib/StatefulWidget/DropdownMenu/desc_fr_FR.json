{"id": 370, "name": "DropdownMenu", "localName": "<PERSON><PERSON>", "info": "Composant de sélection déroulante, prend en charge le filtrage par saisie de texte, et permet de personnaliser les éléments du menu. Il repose principalement sur MenuAnchor et TextFiled pour son implémentation.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1.dart", "name": "Utilisation simple du menu déroulant", "desc": ["【dropdownMenuEntries】 : Liste des entrées du menu   【List<DropdownMenuEntry<T>>】", "【initialSelection】 : Rappel de validation du formulaire   【T?】", "【onSelected】 : Rappel de sauvegarde du formulaire   【ValueChanged<T?>?】", "【menuHeight】 : <PERSON><PERSON> du menu   【double】", "【width】 : <PERSON>ur du champ de saisie   【double】"]}, {"file": "node2.dart", "name": "Configuration du style du menu déroulant", "desc": ["【controller】 : Contr<PERSON>leur de saisie de texte   【TextEditingController?】", "【label】 : Étiquette du champ de saisie   【Widget?】", "【textStyle】 : Style du texte du champ de saisie   【TextStyle?】", "【inputDecorationTheme】 : Thème de décoration du champ de saisie   【InputDecorationTheme?】", "【leadingIcon】 : Icône de gauche   【Widget?】", "【trailingIcon】 : Icône de droite lorsque le menu est déployé   【Widget?】", "【selectedTrailingIcon】 : Icône de droite lorsque le menu est déployé   【Widget?】", "【hintText】 : Texte d'indication du champ de saisie   【String?】", "【helperText】 : Texte d'aide du champ de saisie   【String?】", "【errorText】 : Texte d'erreur du champ de saisie   【String?】", "【menuStyle】 : Style du menu contextuel   【MenuStyle?】"]}, {"file": "node3.dart", "name": "Personnalisation des éléments du menu déroulant", "desc": ["Il est possible de personnaliser la construction des éléments du menu via le labelWidget de DropdownMenuEntry."]}]}