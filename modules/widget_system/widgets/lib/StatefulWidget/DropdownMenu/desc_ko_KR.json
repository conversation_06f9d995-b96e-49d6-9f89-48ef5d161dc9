{"id": 370, "name": "DropdownMenu", "localName": "드롭다운 메뉴", "info": "드롭다운 선택 컴포넌트로, 텍스트 입력 필터링을 지원하며 메뉴 항목을 사용자 정의할 수 있습니다. 기본적으로 MenuAnchor와 TextFiled에 의존하여 구현됩니다.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1.dart", "name": "드롭다운 메뉴의 간단한 사용", "desc": ["【dropdownMenuEntries】 : 메뉴 항목 리스트   【List<DropdownMenuEntry<T>>】", "【initialSelection】 : 폼 검증 콜백   【T?】", "【onSelected】 : 폼 저장 콜백   【ValueChanged<T?>?】", "【menuHeight】 : 메뉴 높이   【double】", "【width】 : 입력 필드 너비   【double】"]}, {"file": "node2.dart", "name": "드롭다운 메뉴 스타일 설정", "desc": ["【controller】 : 텍스트 입력 컨트롤러   【TextEditingController?】", "【label】 : 입력 필드 라벨   【Widget?】", "【textStyle】 : 입력 필드 텍스트 스타일   【TextStyle?】", "【inputDecorationTheme】 : 입력 필드 장식 테마   【InputDecorationTheme?】", "【leadingIcon】 : 왼쪽 아이콘   【Widget?】", "【trailingIcon】 : 오른쪽 메뉴 확장 아이콘   【Widget?】", "【selectedTrailingIcon】 : 오른쪽 메뉴 확장 아이콘   【Widget?】", "【hintText】 : 입력 필드 힌트 텍스트   【String?】", "【helperText】 : 입력 필드 보조 텍스트   【String?】", "【errorText】 : 입력 필드 오류 텍스트   【String?】", "【menuStyle】 : 팝업 메뉴 스타일   【MenuStyle?】"]}, {"file": "node3.dart", "name": "드롭다운 메뉴 사용자 정의 메뉴 항목", "desc": ["DropdownMenuEntry의 labelWidget을 통해 메뉴 항목을 사용자 정의할 수 있습니다."]}]}