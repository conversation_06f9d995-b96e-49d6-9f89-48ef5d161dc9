{"id": 56, "name": "PopupMenuButton", "localName": "Botón de menú", "info": "Menú emergente, se pueden especificar propiedades como desplazamiento, color, profundidad de sombra, forma, etc. Recibe eventos de selección de ítem y eventos de cancelación.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de PopupMenuButton", "desc": ["【itemBuilder】 : Constructor   【PopupMenuItemBuilder<T>】", "【offset】 : Desplazamiento   【Offset】", "【color】 : Color de fondo   【Color】", "【shape】 : Forma   【ShapeBorder】", "【elevation】 : Profundidad de sombra   【double】", "【onCanceled】 : Evento de cancelación   【Function()】", "【onSelected】 : Evento de selección   【Function(T)】"]}]}