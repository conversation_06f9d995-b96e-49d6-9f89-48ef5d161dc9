{"id": 138, "name": "CupertinoTimerPicker", "localName": "Seletor de Tempo iOS", "info": "Um seletor de tempo sofisticado com rolagem, que permite especificar o tipo de seleção, hora inicial, cor de fundo, etc., e recebe eventos de seleção de tempo.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do CupertinoTimerPicker", "desc": ["【initialTimerDuration】 : Hora inicial   【Duration】", "【minuteInterval】 : Intervalo de minutos   【double】", "【secondInterval】 : Intervalo de segundos   【double】", "【alignment】 : Alinhamento  【AlignmentGeometry】", "【backgroundColor】 : Cor de fundo  【Color】", "【mode】 : Modo*3  【CupertinoTimerPickerMode】"]}]}