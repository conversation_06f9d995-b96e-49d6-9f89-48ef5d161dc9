{"id": 113, "name": "DecoratedBoxTransition", "localName": "Decoration Transition", "info": "A subclass of AnimatedWidget that uses a Decorated type animator to allow child components to transition between two Decorated objects.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of DecoratedBoxTransition", "desc": ["【child】: Child component   【Widget】", "【position】: Foreground/Background color   【DecorationPosition】", "【decoration】: Animation   【Animation<Decoration>】"]}]}