{"id": 245, "name": "CupertinoTextField", "localName": "iOS风格输入框", "info": "Cupertino风格的输入框,属性和TextField类似,可指定控制器、文字样式、装饰线、行数限制、游标样式等。接收输入变化、完成输入等事件。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoTextField基础使用", "desc": ["【placeholder】 : 提示文字   【String】", "【showCursor】 : 是否显示游标   【bool】", "【minLines】 : 最小行数   【int】", "【maxLines】 : 最大行数   【int】", "【padding】 : 内边距   【EdgeInsetsGeometry】", "【onChanged】 : 变化监听   【ValueChanged<String>】", "【onTap】: 点击监听   【GestureTapCallback】", "【onSubmitted】: 提交监听    【ValueChanged<String>】"]}, {"file": "node2_style.dart", "name": "CupertinoTextField常用样式属性", "desc": ["【style】 : 输入文字样式   【TextStyle】", "【prefix】: 前缀组件   【Widget】", "【prefixMode】: 前缀模式   【OverlayVisibilityMode】", "【suffix】: 后缀组件   【Widget】", "【suffixMode】: 后缀模式   【OverlayVisibilityMode】", "【cursorColor】: 游标颜色   【Color】", "【cursorWidth】: 游标宽度   【double】", "【cursorRadius】: 游标圆角   【Radius】", "【readOnly】: 是否只读    【bool】"]}]}