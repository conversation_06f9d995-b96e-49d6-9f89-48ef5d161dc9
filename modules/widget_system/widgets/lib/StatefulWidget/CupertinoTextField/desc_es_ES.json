{"id": 245, "name": "CupertinoTextField", "localName": "Campo de texto estilo iOS", "info": "Campo de texto de estilo Cupertino, con propiedades similares a TextField, se puede especificar controlador, estilo de texto, línea de decoración, límite de líneas, estilo de cursor, etc. Recibe eventos de cambio de entrada, finalización de entrada, etc.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de CupertinoTextField", "desc": ["【placeholder】 : <PERSON><PERSON> de sugerencia   【String】", "【showCursor】 : Mostrar cursor   【bool】", "【minLines】 : <PERSON><PERSON><PERSON><PERSON> mínimo de líneas   【int】", "【maxLines】 : Número máximo de líneas   【int】", "【padding】 : <PERSON><PERSON><PERSON> interno   【EdgeInsetsGeometry】", "【onChanged】 : Escucha de cambios   【ValueChanged<String>】", "【onTap】: Escucha de clics   【GestureTapCallback】", "【onSubmitted】: Escucha de envío    【ValueChanged<String>】"]}, {"file": "node2_style.dart", "name": "Propiedades de estilo comunes de CupertinoTextField", "desc": ["【style】 : Est<PERSON> del texto de entrada   【TextStyle】", "【prefix】: Componente de prefijo   【Widget】", "【prefixMode】: Modo de prefijo   【OverlayVisibilityMode】", "【suffix】: Componente de sufijo   【Widget】", "【suffixMode】: Modo de sufijo   【OverlayVisibilityMode】", "【cursorColor】: Color del cursor   【Color】", "【cursorWidth】: <PERSON><PERSON> del cursor   【double】", "【cursorRadius】: Radio del cursor   【Radius】", "【readOnly】: Solo lectura    【bool】"]}]}