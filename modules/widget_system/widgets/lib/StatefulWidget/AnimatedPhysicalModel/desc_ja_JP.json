{"id": 225, "name": "AnimatedPhysicalModel", "localName": "物理モジュールアニメーション", "info": "関連する属性が変化する際にアニメーション効果を持つPhysicalModelコンポーネントで、本質的にはPhysicalModelとアニメーションの組み合わせです。影、影の深さ、角の丸み、アニメーションの長さ、終了コールバックなどの属性を指定できます。", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedPhysicalModelの基本使用", "desc": ["【color】 : 背景色   【Color】", "【duration】 : アニメーションの長さ   【Duration】", "【onEnd】 : アニメーション終了コールバック   【Function()】", "【curve】 : アニメーションカーブ   【Duration】", "【shape】 : 形状   【BoxShape】", "【elevation】 : 影の深さ   【double】", "【borderRadius】 : 角の丸み   【BorderRadius】", "【shadowColor】 : 影の色   【Color】", "【child】 : 子コンポーネント   【Widget】"]}]}