{"id": 177, "name": "ReorderableListView", "localName": "Sortierbare Liste", "info": "<PERSON><PERSON> ListView, die durch langes Drücken sortiert werden kann. Es können Eigenschaften wie die Scrollrichtung, ob umgeke<PERSON><PERSON>, Scroll-Controller usw. angegeben werden.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von ReorderableListView", "desc": ["【children】 : Liste der Unterkomponenten   【List<Widget>】", "【header】 : Kopfkomponente   【Widget】", "【padding】 : Innenabstand   【EdgeInsets】", "【onReorder】 : <PERSON><PERSON><PERSON><PERSON> beim <PERSON>  【ReorderCallback】"]}, {"file": "node2_direction.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> von ReorderableListView", "desc": ["【scrollDirection】 : Scrollrichtung   【Axis】", "【reverse】 : Ob umgekehrt  【bool】"]}]}