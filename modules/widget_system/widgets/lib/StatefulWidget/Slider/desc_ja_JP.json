{"id": 42, "name": "Slide<PERSON>", "localName": "スライダー", "info": "スライダーコンポーネントは、指定された最大値と最小値の間でドラッグして選択することができます。色、分割数、表示されるラベルを指定でき、進捗変化のコールバックを受け取ります。", "lever": 4, "family": 1, "linkIds": [43, 44, 331], "nodes": [{"file": "node1_base.dart", "name": "スライダーの基本使用", "desc": ["【value】 : 数値   【double】", "【min】 : 最小値   【double】", "【max】 : 最大値   【double】", "【activeColor】 : アクティブカラー   【Color】", "【inactiveColor】 : 非アクティブカラー   【Color】", "【onChanged】 : 変更時のコールバック   【Function(double)】"]}, {"file": "node2_lable.dart", "name": "スライダーの分割とラベル", "desc": ["【divisions】 : 分割数   【int】", "【label】 : ヒントバブルテキスト   【String】", "【onChangeStart】 : スライド開始時のリスナー   【Function(double)】", "【onChangeEnd】 : スライド終了時のリスナー   【Function(double)】"]}]}