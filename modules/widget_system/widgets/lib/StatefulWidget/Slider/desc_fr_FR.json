{"id": 42, "name": "Slide<PERSON>", "localName": "<PERSON><PERSON>", "info": "Composant curseur, permet de faire glisser pour sélectionner entre une valeur maximale et minimale spécifiées. Peut spécifier la couleur, le nombre de segments et les étiquettes affichées, reçoit un rappel de changement de progression.", "lever": 4, "family": 1, "linkIds": [43, 44, 331], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base du Slider", "desc": ["【value】 : valeur   【double】", "【min】 : valeur minimale   【double】", "【max】 : valeur maximale   【double】", "【activeColor】 : couleur active   【Color】", "【inactiveColor】 : couleur inactive   【Color】", "【onChanged】 : rappel lors du changement   【Function(double)】"]}, {"file": "node2_lable.dart", "name": "Segmentation et étiquettes du Slider", "desc": ["【divisions】 : nombre de segments   【int】", "【label】 : texte de la bulle d'info   【String】", "【onChangeStart】 : écoute au début du glissement   【Function(double)】", "【onChangeEnd】 : écoute à la fin du glissement   【Function(double)】"]}]}