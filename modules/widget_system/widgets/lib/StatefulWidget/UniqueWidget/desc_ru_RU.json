{"id": 243, "name": "UniqueWidget", "localName": "Уникальный компонент", "info": "Абстрактный класс, должен предоставлять GlobalKey для идентификации. Этот тип компонента будет inflated только один экземпляр, и в любой момент времени будет только одно состояние, которое можно получить через свойство currentState.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Введение в UniqueWidget", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】"]}]}