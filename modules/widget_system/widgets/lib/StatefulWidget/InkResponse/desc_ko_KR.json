{"id": 149, "name": "InkResponse", "localName": "물결 반응", "info": "물결 클릭 효과, 클릭, 더블 클릭, 길게 누르기, 취소, 하이라이트 변경 이벤트를 수신하며, 물결 색상, 반경, 하이라이트 모양 등의 속성을 지정할 수 있습니다.", "lever": 1, "family": 1, "linkIds": [150, 152], "nodes": [{"file": "node1_base.dart", "name": "InkResponse 기본 이벤트", "desc": ["【child】 : 자식 위젯   【Widget】", "【onTap】 : 클릭 이벤트   【Function()】", "【onDoubleTap】 : 더블 클릭 이벤트   【Function()】", "【onTapCancel】 : 클릭 취소   【Function()】", "【onLongPress】 : 길게 누르기 이벤트   【Function()】"]}, {"file": "node2_color.dart", "name": "InkResponse 기타 속성", "desc": ["【child】 : 자식 위젯   【Widget】", "【onHighlightChanged】 : 하이라이트 변경 콜백   【Function(bool)】", "【highlightColor】 : 하이라이트 색상   【Color】", "【splashColor】 : 물결 색상   【Color】", "【radius】 : 물결 반경   【double】"]}]}