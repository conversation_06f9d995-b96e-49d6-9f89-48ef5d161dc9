{"id": 60, "name": "BottomNavigationBar", "localName": "Bottom Navigation", "info": "A bottom navigation bar, usually used at the bottom of the Scaffold component, can specify colors and modes, accepts click callbacks, and can achieve page switching effects with PageView.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of BottomNavigationBar", "desc": ["【currentIndex】 : Current index   【int】", "【elevation】 : Shadow depth   【double】", "【type】 : Type*2   【BottomNavigationBarType】", "【fixedColor】 : Color when type is fix   【Color】", "【backgroundColor】 : Background color   【Color】", "【iconSize】 : Icon size   【double】", "【selectedLabelStyle】 : Selected text style   【TextStyle】", "【unselectedLabelStyle】 : Unselected text style   【TextStyle】", "【showUnselectedLabels】 : Show unselected labels   【bool】", "【showSelectedLabels】 : Show selected labels   【bool】", "【items】 : Items   【List<BottomNavigationBarItem>】", "【onTap】 : Click event   【Function(int)】"]}, {"file": "node2_page.dart", "name": "<PERSON> Switching with <PERSON><PERSON><PERSON><PERSON>", "desc": ["Use the controller for page switching during onTap"]}]}