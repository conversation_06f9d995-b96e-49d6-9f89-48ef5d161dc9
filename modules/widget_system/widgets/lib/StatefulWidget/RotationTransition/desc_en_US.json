{"id": 90, "name": "RotationTransition", "localName": "Rotation Transition", "info": "Can accommodate a child component and make it perform a rotation animation. Requires an animator turns and has an alignment property.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of RotationTransition", "desc": ["【child】 : Child component   【Widget】", "【turns】 : Whether to disappear   【Animation<double>】"]}]}