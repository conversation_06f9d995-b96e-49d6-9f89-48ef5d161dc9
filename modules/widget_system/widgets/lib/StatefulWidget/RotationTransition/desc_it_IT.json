{"id": 90, "name": "RotationTransition", "localName": "Transizione di rotazione", "info": "P<PERSON><PERSON> contenere un componente figlio e farlo ruotare con un'animazione, richiede un animatore turns, possiede l'attributo alignment.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di RotationTransition", "desc": ["【child】 : Componente figlio   【Widget】", "【turns】 : Scompare   【Animation<double>】"]}]}