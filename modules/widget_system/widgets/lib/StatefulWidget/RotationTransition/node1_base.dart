
import 'package:flutter/material.dart';
/// create by 张风捷特烈 on 2020/4/30
/// contact me <NAME_EMAIL>

class CustomRotationTransition extends StatefulWidget {
  const CustomRotationTransition({Key? key}) : super(key: key);

  @override
  _CustomRotationTransitionState createState() => _CustomRotationTransitionState();
}

class _CustomRotationTransitionState extends State<CustomRotationTransition> with SingleTickerProviderStateMixin{

  late AnimationController _ctrl;

  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    _ctrl.forward();
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _ctrl.forward(from: 0),
      child: Container(
        color: Colors.grey.with<PERSON><PERSON><PERSON>(22),
        width: 100,
        height: 100,
        child: RotationTransition(
          turns: CurvedAnimation(
            parent: _ctrl,
            curve: Curves.linear,
          ),
          child: const Icon(
            Icons.camera_outlined,
            color: Colors.green,
            size: 60,
          ),
        ),
      ),
    );
  }
}
