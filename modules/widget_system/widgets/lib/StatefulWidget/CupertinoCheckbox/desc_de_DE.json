{"id": 238, "name": "CupertinoCheckbox", "localName": "Checkbox·macOS-Stil", "info": "Ein macOS-stilisiertes Checkbox, das drei Zustände unterstützt; bei der Verwendung müssen der Wert und die onChanged-Rückruffunktion bereitgestellt werden.", "lever": 4, "family": 1, "linkIds": [39, 17], "nodes": [{"file": "node1.dart", "name": "CupertinoCheckbox Verwendung", "desc": ["【value】 : Ob ausgewählt   【bool?】", "【onChanged】 : Änderungsrückruf   【ValueChanged<bool?>?】", "【checkColor】 : Farbe des Häkchens bei Auswahl   【Color?】", "【activeColor】 : Hintergrundfarbe bei Auswahl   【Color?】", "【activeColor】 : Hintergrundfarbe bei Auswahl   【Color?】", "【inactiveColor】 : Randfarbe/Hintergrundfarbe bei Inaktivität   【Color?】", "<PERSON><PERSON> on<PERSON><PERSON>ed null ist, bedeu<PERSON><PERSON> dies, dass es nicht verfügbar ist."]}, {"file": "node2.dart", "name": "CupertinoCheckbox Drei Zustände", "desc": ["【tristate】 : Ob der Dreizustand aktiviert ist   【bool】", "Im Dreizustand ist der Wert null als -"]}, {"file": "node3.dart", "name": "CupertinoCheckbox Randlinie und Form", "desc": ["【shape】 : Form   【OutlinedBorder?】", "【side】 : Randlinie   【BorderSide?】"]}]}