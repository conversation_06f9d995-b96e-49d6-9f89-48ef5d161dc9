{"id": 158, "name": "CupertinoTabScaffold", "localName": "iOS-Tab-Scaffold", "info": "iOS-stilisiertes Seitenlayout-Scaffold, das eine Navigationsleiste am unteren Rand und eine Hauptinhaltseite festlegen kann.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von CupertinoTabScaffold", "desc": ["【tabBar】 : Tab-Leiste   【CupertinoTabBar】", "【backgroundColor】 : Hintergrundfarbe   【Color】", "【controller】 : Controller   【CupertinoTabController】", "【tabBuilder】 : Seitenkonstruktor   【IndexedWidgetBuilder】"]}]}