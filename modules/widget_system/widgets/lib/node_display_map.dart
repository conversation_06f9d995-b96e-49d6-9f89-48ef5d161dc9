import 'package:flutter/cupertino.dart';
import 'exp/Other.dart';
import 'exp/ProxyWidget.dart';
import 'exp/MultiChildRenderObjectWidget.dart';
import 'exp/SingleChildRenderObjectWidget.dart';
import 'exp/Sliver.dart';
import 'exp/StatefulWidget.dart';
import 'exp/StatelessWidget.dart';


Widget mapNodeDisplay(int widgetId, int nodePriority) {
  String name = '$widgetId#$nodePriority';

  return switch (name) {
    '96#0' => const ColumnNode1(),
    '341#0' => const CustomMultiChildLayoutNode1(),
    '94#0' => const FlexNode1(),
    '94#1' => const FlexNode2(),
    '94#2' => const FlexNode3(),
    '94#3' => const FlexNode4(),
    '94#4' => const FlexNode5(),
    '99#0' => const FlowNode1(),
    '99#1' => const FlowNode02(),
    '161#0' => const IndexedStackNode01(),
    '342#0' => const ListBodyDemo(),
    '344#0' => const NestedScrollViewViewportDemo(),
    '101#0' => const CustomRichText(),
    '101#1' => const RichTextWithWidget(),
    '95#0' => const CustomRow(),
    '343#0' => const ShrinkWrappingViewportDemo(),
    '97#0' => const CustomStack(),
    '97#1' => const PositionedStack(),
    '340#0' => const ViewportDemo(),
    '98#0' => const DirectionWrap(),
    '98#1' => const WrapAlignmentWrap(),
    '98#2' => const CrossAxisAlignmentWrap(),
    '98#3' => const TextDirectionWrap(),
    '98#4' => const VerticalDirectionWrap(),
    '197#0' => const ErrorWidgetDemo(),
    '291#0' => const ListWheelViewportDemo(),
    '291#1' => const ListWheelViewportDemo2(),
    '291#2' => const ListWheelViewportDemo3(),
    '291#3' => const ListWheelViewportDemo4(),
    '312#0' => const PerformanceOverlayDemo(),
    '313#0' => const RawImageDemo(),
    '289#0' => const RenderObjectToWidgetAdapterDemo(),
    '110#0' => const CustomTable(),
    '338#0' => const ButtonBarThemeDemo(),
    '326#0' => const ButtonThemeDemo(),
    '328#0' => const ChipThemeDemo(),
    '337#0' => const CupertinoUserInterfaceLevelDemo(),
    '320#0' => const DefaultAssetBundleDemo(),
    '324#0' => const DefaultTextStyleDemo(),
    '319#0' => const DirectionalityDemo(),
    '329#0' => const DividerThemeDemo(),
    '181#0' => const CustomDropDownButtonHideUnderline(),
    '106#0' => const CustomExpended(),
    '109#0' => const CustomFlexible(),
    '325#0' => const IconThemeDemo(),
    '336#0' => const InheritedModelDemo(),
    '345#0' => const InheritedThemeDemo(),
    '346#0' => const InheritedWidgetDemo(),
    '346#1' => const InheritedWidgetDemo2(),
    '316#0' => const KeepAliveDemo(),
    '315#0' => const LayoutIdDemo(),
    '334#0' => const ListTileThemeDemo(),
    '327#0' => const MaterialBannerThemeDemo(),
    '167#0' => const CustomMediaQuery(),
    '347#0' => const ParentDataWidgetDemo(),
    '330#0' => const PopupMenuThemeDemo(),
    '108#0' => const CustomPositioned(),
    '335#0' => const PrimaryScrollControllerDemo(),
    '180#0' => const CustomScrollConfiguration(),
    '331#0' => const SliderThemeDemo(),
    '331#1' => const DIYSliderTheme(),
    '317#0' => const TableCellDemo(),
    '332#0' => const ToggleButtonsThemeDemo(),
    '333#0' => const TooltipThemeDemo(),
    '295#0' => const CustomAbsorbPointer(),
    '85#0' => const CustomAlign(),
    '85#1' => const Ball(),
    '201#0' => const CustomAnimatedSize(),
    '288#0' => const AnnotatedRegionDemo(),
    '77#0' => const CustomAspectRatio(),
    '278#0' => const CustomBackdropFilter(),
    '75#0' => const CustomBaseline(),
    '283#0' => const CallbackShortcutsDemo1(),
    '86#0' => const CustomCenter(),
    '66#0' => const CustomClipOval(),
    '69#0' => const CustomClipPath(),
    '67#0' => const CustomClipRect(),
    '68#0' => const CustomClipRRect(),
    '267#0' => const ColoredBoxDemo(),
    '88#0' => const CustomColorFiltered(),
    '265#0' => const CompositedTransformFollowerDemo(),
    '266#0' => const CompositedTransformTargetDemo(),
    '80#0' => const CustomConstrainedBox(),
    '299#0' => const CupertinoTextSelectionToolbarDemo(),
    '166#0' => const ClockPage(),
    '166#1' => const PlayBezier3Page(),
    '285#0' => const CustomSingleChildLayoutDemo(),
    '285#1' => const OffSetWidgetDemo(),
    '70#0' => const BoxDecorationDemo(),
    '70#1' => const ShapeImageDemo(),
    '70#2' => const BorderDemo(),
    '70#3' => const ShapeDecorationDemo(),
    '70#4' => const UnderlineTabIndicatorDemo(),
    '70#5' => const FlutterLogoDecorationDemo(),
    '89#0' => const CustomFadeTransition(),
    '87#0' => const CustomFittedBox(),
    '82#0' => const CustomFractionallySizedBox(),
    '263#0' => const FractionalTranslationDemo(),
    '292#0' => const CustomIgnorePointer(),
    '357#0' => const ImageFilteredBlur(),
    '357#1' => const ImageFilteredColor(),
    '357#2' => const ImageFilteredMatrix(),
    '298#0' => const IntrinsicHeightDemo(),
    '297#0' => const IntrinsicWidthDemo(),
    '287#0' => const CustomLayoutBuilder(),
    '287#1' => const FitByLayoutBuilder(),
    '287#2' => const SimpleExpandableText(),
    '79#0' => const CustomLimitedBox(),
    '71#0' => const CustomOffstage(),
    '73#0' => const CustomOpacity(),
    '83#0' => const CustomOverflowBox(),
    '74#0' => const PaddingAll(),
    '74#1' => const PaddingOnly(),
    '74#2' => const PaddingSymmetric(),
    '296#0' => const PhysicalModelDemo(),
    '279#0' => const PhysicalShapeDemo(),
    '264#0' => const RepaintBoundaryDemo(),
    '264#1' => const RepaintBoundarySave(),
    '72#0' => const CustomRotatedBox(),
    '277#0' => const RadialShaderMask(),
    '277#1' => const LinearShaderMask(),
    '294#0' => const SizeChangedLayoutNotifierDemo(),
    '76#0' => const CustomSizedBox(),
    '84#0' => const CustomSizedOverflowBox(),
    '280#0' => const TapRegionDemo1(),
    '281#0' => const TextFieldTapRegionDemo1(),
    '78#0' => const SkewTransform(),
    '78#1' => const TranslationTransform(),
    '78#2' => const ScaleTransform(),
    '78#3' => const RotateTransform(),
    '78#4' => const R3C2(),
    '81#0' => const CustomUnConstrainedBox(),
    '302#0' => const CupertinoSliverNavigationBarDemo(),
    '303#0' => const CupertinoSliverRefreshControlDemo(),
    '183#0' => const CustomScrollViewDemo(),
    '209#0' => const DecorationSliverDemo(),
    '196#0' => const FlexibleSpaceBarDemo(),
    '309#0' => const PinnedHeaderSliverNode1(),
    '309#1' => const PinnedHeaderSliverNode2(),
    '309#2' => const PinnedHeaderSliverNode3(),
    '301#0' => const SliverAnimatedListDemo(),
    '184#0' => const SliverAppBarDemo(),
    '270#0' => const SliverConstrainedCrossAxisDemo(),
    '271#0' => const SliverCrossAxisExpandedDemo(),
    '269#0' => const SliverCrossAxisGroupDemo(),
    '306#0' => const SliverFillRemainingDemo(),
    '187#0' => const SliverFillViewportDemo(),
    '186#0' => const SliverFixedExtentListDemo(),
    '188#0' => const SliverGirdDemo(),
    '305#0' => const SliverIgnorePointerDemo(),
    '304#0' => const SliverLayoutBuilderDemo(),
    '185#0' => const SliverListDemo(),
    '268#0' => const SliverMainAxisGroupDemo(),
    '192#0' => const SliverOpacityDemo(),
    '307#0' => const SliverOverlapAbsorberDemo(),
    '308#0' => const SliverOverlapInjectorDemo(),
    '191#0' => const SliverPaddingDemo(),
    '190#0' => const SliverPersistentHeaderDemo(),
    '314#0' => const SliverPrototypeExtentListDemo(),
    '189#0' => const SliverToBoxAdapterDemo(),
    '348#0' => const SliverWithKeepAliveWidgetDemo(),
    '111#0' => const CustomAlignTransition(),
    '120#0' => const CustomAnimatedAlign(),
    '228#0' => const AnimatedBuilderDemo(),
    '123#0' => const CustomAnimatedContainer(),
    '100#0' => const CustomAnimatedCrossFade(),
    '100#1' => const CurveAnimatedCrossFade(),
    '124#0' => const CustomAnimatedDefaultTextStyle(),
    '260#0' => const AnimatedFractionallySizedBoxDemo(),
    '117#0' => const CustomAnimatedList(),
    '227#0' => const AnimatedModalBarrierDemo(),
    '118#0' => const CustomAnimatedOpacity(),
    '119#0' => const CustomAnimatedPadding(),
    '225#0' => const AnimatedPhysicalModelDemo(),
    '121#0' => const CustomAnimatedPositioned(),
    '122#0' => const CustomAnimatedPositionedDirectional(),
    '259#0' => const AnimatedRotationDemo(),
    '249#0' => const AnimatedScaleDemo(),
    '247#0' => const AnimatedSlideDemo(),
    '116#0' => const CustomAnimatedSwitcher(),
    '224#0' => const AnimatedThemeDemo(),
    '57#0' => const CustomAppBar(),
    '57#1' => const TabAppBar(),
    '239#0' => const AutomaticKeepAliveDemo(),
    '61#0' => const CustomBottomAppBar(),
    '60#0' => const CustomBottomNavigationBar(),
    '60#1' => const BottomNavigationBarWithPageView(),
    '237#0' => const CarouselNode1(),
    '237#1' => const CarouselNode2(),
    '39#0' => const CustomCheckbox(),
    '39#1' => const TristateCheckBok(),
    '46#0' => const CustomCircularProgressIndicator(),
    '48#0' => const CustomCupertinoActivityIndicator(),
    '156#0' => const CustomCupertinoApp(),
    '24#0' => const CustomCupertinoButton(),
    '238#0' => const CupertinoCheckboxDemo1(),
    '238#1' => const CupertinoCheckboxDemo2(),
    '238#2' => const CupertinoCheckboxDemo3(),
    '143#0' => const CustomCupertinoContextMenu(),
    '144#0' => const CustomCupertinoContextMenuAction(),
    '137#0' => const CustomCupertinoDatePicker(),
    '62#0' => const CustomCupertinoNavigationBar(),
    '157#0' => const CustomCupertinoPageScaffold(),
    '139#0' => const CustomCupertinoPicker(),
    '240#0' => const CupertinoRadioDemo1(),
    '240#1' => const CupertinoRadioDemo2(),
    '240#2' => const CupertinoRadioDemo3(),
    '195#0' => const CustomCupertinoScrollbar(),
    '262#0' => const CupertinoSegmentedControlDemo(),
    '262#1' => const CupertinoSegmentedControlColor(),
    '43#0' => const CustomCupertinoSlider(),
    '256#0' => const CupertinoSlidingSegmentedControlDemo(),
    '41#0' => const CustomCupertinoSwitch(),
    '63#0' => const CustomCupertinoTabBar(),
    '158#0' => const CustomCupertinoTabScaffold(),
    '229#0' => const CupertinoTabViewDemo(),
    '245#0' => const CupertinoTextFieldDemo(),
    '245#1' => const CupertinoTextFieldStyle(),
    '138#0' => const CustomCupertinoTimerPicker(),
    '339#0' => const DateRangePickerDialogDemo(),
    '339#1' => const DiyDateRangePickerDialogDemo(),
    '113#0' => const CustomDecoratedBoxTransition(),
    '230#0' => const DefaultTabControllerDemo(),
    '114#0' => const CustomDefaultTextStyleTransition(),
    '176#0' => const CustomDismissible(),
    '176#1' => const DirectionDismissible(),
    '103#0' => const CustomDraggable(),
    '103#1' => const DraggablePage(),
    '103#2' => const DeleteDraggable(),
    '252#0' => const DraggableScrollableSheetDemo(),
    '104#0' => const CustomDragTarget(),
    '257#0' => const DrawerControllerDemo(),
    '55#0' => const CustomDropDownButton(),
    '55#1' => const StyleDropDownButton(),
    '223#0' => const DropdownButtonFormFieldDemo(),
    '370#0' => const DropdownMenuNode1(),
    '370#1' => const DropdownMenuNode2(),
    '370#2' => const DropdownMenuNode3(),
    '244#0' => const EditableTextDemo(),
    '354#0' => const ElevatedButtonDemo(),
    '354#1' => const ElevatedButtonStyleDemo(),
    '51#0' => const CustomExpandIcon(),
    '178#0' => const CustomExpansionPanelList(),
    '52#0' => const CustomExpansionTile(),
    '359#0' => const FilledButtonDemo1(),
    '359#1' => const FilledButtonDemo2(),
    '282#0' => const FocusDemo1(),
    '282#1' => const FocusDemo2(),
    '282#2' => const FocusDemo3(),
    '198#0' => const CustomForm(),
    '222#0' => const FormFieldDemo(),
    '172#0' => const CustomFutureBuilder(),
    '250#0' => const GlowingOverscrollIndicatorDemo(),
    '171#0' => const CustomHero(),
    '38#0' => const LoadImage(),
    '38#1' => const FitImage(),
    '38#2' => const AlignmentImage(),
    '38#3' => const BlendModeImage(),
    '38#4' => const RepeatImage(),
    '38#5' => const CenterSliceImage(),
    '152#0' => const CustomInk(),
    '152#1' => const InkImage(),
    '149#0' => const CustomInkResponse(),
    '149#1' => const ColorInkResponse(),
    '150#0' => const CustomInkWell(),
    '150#1' => const ColorInkWell(),
    '231#0' => const InputDecoratorDemo(),
    '351#0' => const InteractiveViewerDemo(),
    '351#1' => const InteractiveViewerDemo2(),
    '351#2' => const InteractiveViewerDemo3(),
    '284#0' => const KeyboardListenerDemo1(),
    '145#0' => const CustomLicensePage(),
    '47#0' => const CustomLinearProgressIndicator(),
    '179#0' => const CustomListWheelScrollView(),
    '290#0' => const LocalizationsDemo1(),
    '105#0' => const CustomLongPressDraggable(),
    '160#0' => const CustomMaterial(),
    '160#1' => const ShapeMaterial(),
    '65#0' => const MaterialAppDemo(),
    '261#0' => const MergeableMaterialDemo(),
    '135#0' => const CustomMonthPicker(),
    '293#0' => const MouseRegionDemo(),
    '358#0' => const CustomNavigationRail(),
    '358#1' => const ExtendableNavigationRail(),
    '358#2' => const DarkNavigationRail(),
    '232#0' => const NavigatorDemo(),
    '251#0' => const NestedScrollViewDemo(),
    '355#0' => const OutlinedButtonDemo(),
    '355#1' => const OutlinedButtonStyleDemo(),
    '182#0' => const CustomOverlay(),
    '165#0' => const CustomPageView(),
    '165#1' => const DirectionPageView(),
    '165#2' => const CtrlPageView(),
    '235#0' => const PaginatedDataTableDemo(),
    '56#0' => const CustomPopupMenuButton(),
    '174#0' => const CustomPopupMenuDivider(),
    '93#0' => const CustomPositionedTransition(),
    '45#0' => const CustomRadio(),
    '44#0' => const CustomRangeSlider(),
    '153#0' => const PressRawChip(),
    '153#1' => const SelectRawChip(),
    '248#0' => const RawGestureDetectorDemo(),
    '254#0' => const RawKeyboardListenerDemo(),
    '175#0' => const CustomRawMaterialButton(),
    '175#1' => const ShapeRawMaterialButton(),
    '49#0' => const CustomRefreshIndicator(),
    '115#0' => const CustomRelativePositionedTransition(),
    '177#0' => const CustomReorderableListView(),
    '177#1' => const DirectionReorderableListView(),
    '90#0' => const CustomRotationTransition(),
    '64#0' => const CustomScaffold(),
    '91#0' => const CustomScaleTransition(),
    '253#0' => const ScrollableDemo(),
    '194#0' => const CustomScrollbar(),
    '53#0' => const CustomSelectableText(),
    '53#1' => const AlignSelectableText(),
    '92#0' => const CustomSizeTransition(),
    '42#0' => const CustomSlider(),
    '42#1' => const DivisionsSlider(),
    '112#0' => const CustomSlideTransition(),
    '242#0' => const StatefulBuilderDemo(),
    '233#0' => const StatusTransitionWidgetDemo(),
    '200#0' => const StepperDemo(),
    '200#1' => const VerticalStepper(),
    '173#0' => const CustomStreamBuilder(),
    '40#0' => const CustomSwitch(),
    '40#1' => const ImageSwitch(),
    '59#0' => const CustomTabBarView(),
    '151#0' => const CustomTableRowInkWell(),
    '353#0' => const TextButtonDemo(),
    '353#1' => const TextButtonStyleDemo(),
    '54#0' => const CustomTextField(),
    '54#1' => const CursorTextField(),
    '54#2' => const ComplexTextField(),
    '199#0' => const CustomTextFormField(),
    '246#0' => const TickerModeDemo1(),
    '50#0' => const CustomTooltip(),
    '50#1' => const DecorationTooltip(),
    '226#0' => const TweenAnimationBuilderDemo(),
    '241#0' => const UndoHistoryDemo1(),
    '243#0' => const UniqueWidgetDemo(),
    '255#0' => const ValueListenableBuilderDemo(),
    '234#0' => const WidgetInspectorDemo(),
    '236#0' => const WidgetsAppDemo(),
    '170#0' => const CustomWillPopScope(),
    '136#0' => const CustomYearPicker(),
    '130#0' => const CustomAboutDialog(),
    '193#0' => const AboutListTileDemo(),
    '13#0' => const CustomActionChip(),
    '127#0' => const CustomAlertDialog(),
    '125#0' => const CustomAnimatedIcon(),
    '356#0' => const AutocompleteDemo(),
    '356#1' => const AutocompleteType(),
    '31#0' => const CustomBackButton(),
    '272#0' => const BackButtonIconDemo(),
    '258#0' => const BadgeDemo(),
    '258#1' => const BadgeLabelDemo(),
    '258#2' => const BadgeAlignOffsetDemo(),
    '5#0' => const CustomBanner(),
    '142#0' => const CustomBottomSheet(),
    '350#0' => const BoxScrollViewDemo(),
    '202#0' => const BuilderDemo(),
    '29#0' => const CustomButtonBar(),
    '29#1' => const PaddingButtonBar(),
    '3#0' => const CustomCard(),
    '3#1' => const ShapeCard(),
    '17#0' => const CustomCheckBoxListTile(),
    '17#1' => const SelectCheckBoxListTile(),
    '17#2' => const DenseCheckBoxListTile(),
    '215#0' => const CheckedModeBannerDemo(),
    '11#0' => const CustomChip(),
    '11#1' => const ColorOfChip(),
    '11#2' => const DeleteOfChip(),
    '12#0' => const CustomChoiceChip(),
    '9#0' => const CustomCircleAvatar(),
    '32#0' => const CustomCloseButton(),
    '274#0' => const CloseButtonIconDemo(),
    '1#0' => const CustomContainer(),
    '1#1' => const ContainerWithChild(),
    '1#2' => const ContainerAlignment(),
    '1#3' => const ContainerDecoration(),
    '1#4' => const ContainerTransform(),
    '1#5' => const ContainerConstraints(),
    '131#0' => const CustomCupertinoActionSheet(),
    '132#0' => const CustomCupertinoActionSheetAction(),
    '129#0' => const CustomCupertinoAlertDialog(),
    '352#0' => const CupertinoDialogActionDemo(),
    '219#0' => const CupertinoFullscreenDialogTransitionDemo(),
    '218#0' => const CupertinoNavigationBarBackButtonDemo(),
    '216#0' => const CupertinoPageTransitionDemo(),
    '217#0' => const CupertinoPopupSurfaceDemo(),
    '169#0' => const TextCupertinoTheme(),
    '169#1' => const CustomCupertinoTheme(),
    '102#0' => const CustomDataTable(),
    '102#1' => const SortDataTable(),
    '134#0' => const CustomDayPicker(),
    '126#0' => const CustomDialog(),
    '34#0' => const CustomDivider(),
    '34#1' => const HeightDivider(),
    '221#0' => const DraggableScrollableActuatorDemo(),
    '154#0' => const CustomDrawer(),
    '276#0' => const DrawerButtonDemo(),
    '273#0' => const DrawerButtonIconDemo(),
    '155#0' => const CustomDrawerHeader(),
    '361#0' => const EndDrawerButtonDemo(),
    '275#0' => const EndDrawerButtonIconDemo(),
    '8#0' => const CustomFadeInImage(),
    '15#0' => const CustomFilterChip(),
    '25#0' => const CustomFlatButton(),
    '28#0' => const CustomFAB(),
    '28#1' => const MiniFAB(),
    '28#2' => const ShapeFAB(),
    '4#0' => const CustomFlutterLogo(),
    '4#1' => const FlutterLogoWithText(),
    '146#0' => const CustomGestureDetector(),
    '146#1' => const TapGestureDetector(),
    '146#2' => const PanGestureDetector(),
    '37#0' => const CustomGridPaper(),
    '37#1' => const DivisionsGridPaper(),
    '21#0' => const CustomGridTile(),
    '20#0' => const CustomGridTileBar(),
    '163#0' => const CustomGridView(),
    '163#1' => const HorizontalGridView(),
    '163#2' => const ExtentGridView(),
    '163#3' => const BuilderGridView(),
    '213#0' => const HtmlElementViewDemo(),
    '6#0' => const CustomIcon(),
    '6#1' => const MyIcon(),
    '30#0' => const CustomIconButton(),
    '7#0' => const CustomImageIcon(),
    '14#0' => const PressInputChip(),
    '14#1' => const SelectInputChip(),
    '147#0' => const CustomListener(),
    '16#0' => const CustomListTile(),
    '16#1' => const SelectListTile(),
    '16#2' => const DenseListTile(),
    '162#0' => const CustomListView(),
    '162#1' => const HorizontalListView(),
    '162#2' => const BuilderListView(),
    '162#3' => const SeparatedListView(),
    '211#0' => const MaterialBannerDemo(),
    '211#1' => const MaterialBannerDemoTwo(),
    '23#0' => const CustomMaterialButton(),
    '23#1' => const LongPressMaterialButton(),
    '23#2' => const ShapeMaterialButton(),
    '212#0' => const ModalBarrierDemo(),
    '214#0' => const NavigationToolbarDemo(),
    '220#0' => const NotificationListenerDemo(),
    '220#1' => const NotificationListenerUpdate(),
    '203#0' => const OrientationBuilderDemo(),
    '27#0' => const CustomOutlineButton(),
    '210#0' => const PageStorageDemo(),
    '36#0' => const CustomPlaceholder(),
    '36#1' => const FallbackPlaceholder(),
    '159#0' => const CustomPositionedDirectional(),
    '204#0' => const CustomPreferredSize(),
    '204#1' => const AdapterPreferredSize(),
    '19#0' => const CustomRadioListTile(),
    '19#1' => const DenseRadioListTile(),
    '26#0' => const CustomRaisedButton(),
    '360#0' => const MagnifierCircleShape(),
    '360#1' => const MagnifierStarShape(),
    '207#0' => const SafeAreaDemo(),
    '349#0' => const ScrollViewDemo(),
    '128#0' => const CustomSimpleDialog(),
    '133#0' => const CustomSimpleDialogOption(),
    '164#0' => const CustomSingleChildScrollView(),
    '164#1' => const DirectionSingleChildScrollView(),
    '140#0' => const CustomSnackBar(),
    '141#0' => const CustomSnackBarAction(),
    '107#0' => const OneSpacer(),
    '107#1' => const ManySpacer(),
    '18#0' => const CustomSwitchListTile(),
    '18#1' => const SelectSwitchListTile(),
    '18#2' => const DenseSwitchListTile(),
    '148#0' => const CustomTab(),
    '58#0' => const CustomTabBar(),
    '58#1' => const NoShadowTabBarDemo(),
    '205#0' => const TabPageSelectorDemo(),
    '206#0' => const TabPageSelectorIndicatorDemo(),
    '2#0' => const TextDemo1(),
    '2#1' => const TextDemo2(),
    '2#2' => const DecorationText(),
    '2#3' => const TextAlignText(),
    '2#4' => const TextDirectionText(),
    '2#5' => const SoftWrapText(),
    '168#0' => const TextThemeDemo(),
    '168#1' => const CustomTheme(),
    '208#0' => const TitleDemo(),
    '33#0' => const CustomToggleButtons(),
    '33#1' => const ColorToggleButtons(),
    '33#2' => const ProToggleButtons(),
    '22#0' => const CustomUAGHP(),
    '22#1' => const ProUAGHP(),
    '35#0' => const CustomVerticalDivider(),
    '35#1' => const WidthVerticalDivider(),
    '10#0' => const CustomVisibility(),
    '10#1' => const ReplacementVisibility(),

    _ => const SizedBox(),
  };
}