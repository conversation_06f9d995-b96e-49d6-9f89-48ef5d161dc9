{"id": 188, "name": "SliverGrid", "localName": "Cuadrícula <PERSON>", "info": "Componente de lista de cuadrícula de la familia Sliver, similar a GirdView, construido a través de count y extent. Normalmente se utiliza en CustomScrollView.", "lever": 4, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de SliverList", "desc": ["SliverGrid.count construye especificando la cantidad axial", "SliverGrid.extent construye especificando la longitud axial", "Las características de los atributos son las mismas que GridView, se pueden ver en detalle"]}]}