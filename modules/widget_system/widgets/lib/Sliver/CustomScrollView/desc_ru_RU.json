{"id": 183, "name": "CustomScrollView", "localName": "Универсальная прокрутка", "info": "Универсальная структура прокрутки, которая позволяет задать направление прокрутки, обратное ли оно, контроллер прокрутки и другие свойства. Вложенные компоненты должны принадлежать семейству Sliver.", "lever": 5, "family": 4, "linkIds": [184, 185, 188], "nodes": [{"file": "node1_base.dart", "name": "Основное использование CustomScrollView", "desc": ["【slivers】 : Список дочерних компонентов   【List<Widget>】", "【reverse】 : Обратная ли прокрутка   【bool】", "【scrollDirection】 : Направление прокрутки   【Axis】", "【controller】 : Контро<PERSON><PERSON>ер   【ScrollController】"]}]}