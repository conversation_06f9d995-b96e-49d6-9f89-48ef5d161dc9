{"id": 305, "name": "SliverIgnorePointer", "localName": "Sliver ignorar eventos", "info": "Puede envolver un componente sliver, y controlar si este componente sliver puede responder a eventos a través de ignoring.", "lever": 3, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de SliverIgnorePointer", "desc": ["【sliver】: componente sliver   【Widget】", "【ignoring】: si se ignoran los eventos   【bool】"]}]}