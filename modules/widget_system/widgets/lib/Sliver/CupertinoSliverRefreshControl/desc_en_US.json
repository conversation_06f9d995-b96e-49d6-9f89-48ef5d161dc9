{"id": 303, "name": "CupertinoSliverRefreshControl", "localName": "Sliver Refresh Controller", "info": "iOS-style pull-to-refresh controller that can execute asynchronous refresh methods, customize controller components, set the height at which the indicator stays, and the scroll height that triggers loading.", "lever": 4, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Refresh Controller", "desc": ["【refreshIndicatorExtent】: Height of the loading indicator   【double】", "【refreshTriggerPullDistance】: Scroll height that triggers loading   【double】", "【onRefresh】: Pull-down event   【RefreshCallback】", "【builder】: Indicator builder   【RefreshControlIndicatorBuilder】"]}]}