{"id": 196, "name": "FlexibleSpaceBar", "localName": "Barra de espacio flexible", "info": "Normalmente se utiliza en la región extensible de SliverAppBar, donde se pueden especificar el título, el espaciado del título, el fondo, el modo de plegado, etc.", "lever": 3, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de FlexibleSpaceBar", "desc": ["【title】 : Componente de título   【Widget】", "【titlePadding】 : Espaciado del título   【EdgeInsetsGeometry】", "【collapseMode】 : <PERSON><PERSON> de plegado   【CollapseMode】", "【stretchModes】 : Modos de extensión   【List<StretchMode>】", "【background】 : Componente de fondo   【Widget】", "【centerTitle】 : <PERSON><PERSON><PERSON> o no   【bool】"]}]}