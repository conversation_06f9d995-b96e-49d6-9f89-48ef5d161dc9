{"id": 209, "name": "DecoratedSliver", "localName": "Panel deslizante decorado", "info": "Este componente se aplica en ventanas deslizantes y puede envolver un componente Sliver hijo para agregar efectos de decoración Decoration.", "lever": 4, "family": 4, "linkIds": [], "nodes": [{"file": "node1.dart", "name": "Uso básico de DecoratedSliver", "desc": ["【sliver】 : Componente hijo   【Widget?】", "【decoration】 : Objeto de decoración   【Decoration】", "【position】 : Posición de la decoración   【DecorationPosition】"]}]}