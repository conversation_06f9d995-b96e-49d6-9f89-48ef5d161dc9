{"id": 209, "name": "DecoratedSliver", "localName": "Decorazione Sliver", "info": "Questo componente viene utilizzato in una vista scorrevole e può avvolgere un componente Sliver figlio, aggiungendo un effetto decorativo Decoration.", "lever": 4, "family": 4, "linkIds": [], "nodes": [{"file": "node1.dart", "name": "Uso di base di DecoratedSliver", "desc": ["【sliver】 : Componente figlio   【Widget?】", "【decoration】 : Oggetto decorativo   【Decoration】", "【position】 : Posizione della decorazione   【DecorationPosition】"]}]}