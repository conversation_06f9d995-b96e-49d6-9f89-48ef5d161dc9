{"id": 209, "name": "DecoratedSliver", "localName": "Decorated Sliver", "info": "This component is used in sliding viewports and can wrap a child Sliver component to add Decoration effects.", "lever": 4, "family": 4, "linkIds": [], "nodes": [{"file": "node1.dart", "name": "Basic Usage of DecoratedSliver", "desc": ["【sliver】: Child component   【Widget?】", "【decoration】: Decoration object   【Decoration】", "【position】: Decoration position   【DecorationPosition】"]}]}