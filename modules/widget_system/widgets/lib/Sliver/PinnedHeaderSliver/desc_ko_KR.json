{"id": 309, "name": "PinnedHeaderSliver", "localName": "고정 헤더 슬라이버", "info": "뷰포트 상단에 고정될 수 있는 슬라이버로, 쉽게 상단 고정 효과를 구현할 수 있습니다.", "lever": 4, "family": 4, "linkIds": [190], "nodes": [{"file": "node_01.dart", "name": "PinnedHeaderSliver 공식 예제", "display": "new_page", "desc": ["【child】 : 자식 위젯   【Widget?】", "PinnedHeaderSliver는 스크롤 뷰포트에서만 사용할 수 있으며, 자식 위젯이 상단으로 스크롤될 때 뷰포트에서 벗어나지 않고 상단에 고정됩니다."]}, {"file": "node_02.dart", "name": "제목 상단 고정", "display": "new_page", "desc": ["PinnedHeaderSliver를 기반으로 구현한 제목 상단 고정 효과, 아래 구분선도 상단 고정 효과가 적용된 것을 확인할 수 있습니다."]}, {"file": "node_03.dart", "name": "제목+검색 상단 고정", "display": "new_page", "desc": ["PinnedHeaderSliver를 기반으로 구현한 제목과 검색창의 상단 고정 효과."]}]}