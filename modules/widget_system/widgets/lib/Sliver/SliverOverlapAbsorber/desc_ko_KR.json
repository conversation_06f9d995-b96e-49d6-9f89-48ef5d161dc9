{"id": 307, "name": "SliverOverlapAbsorber", "localName": "겹침 흡수기", "info": "다른 sliver를 감싸고, 그 레이아웃 범위가 겹치는 것으로 간주되도록 강제합니다. SliverOverlapInjector와 함께 사용해야 합니다.", "lever": 3, "family": 4, "linkIds": [251, 308], "nodes": [{"file": "node1_base.dart", "name": "SliverOverlapAbsorber 기본 사용", "desc": ["【sliver】 : 자식 위젯   【Widget】", "【handle】 *핸들러   【SliverOverlapAbsorberHandle】", "SliverOverlapAbsorber와 SliverOverlapInjector 컴포넌트를 사용하지 않으면, NestedScrollView의 내용이 헤더 바와 겹치게 됩니다."]}]}