{"id": 307, "name": "SliverOverlapAbsorber", "localName": "Assorbitore di Sovrapposizione", "info": "Avvolge un altro sliver e costringe la sua area di layout a essere considerata sovrapposta. Deve essere utilizzato insieme a SliverOverlapInjector.", "lever": 3, "family": 4, "linkIds": [251, 308], "nodes": [{"file": "node1_base.dart", "name": "Uso Base di SliverOverlapAbsorber", "desc": ["【sliver】 : Componente figlio   【Widget】", "【handle】 : *Gestore   【SliverOverlapAbsorberHandle】", "Se non si utilizzano i componenti SliverOverlapAbsorber e SliverOverlapInjector, il contenuto di NestedScrollView si sovrapporrà alla barra dell'intestazione."]}]}