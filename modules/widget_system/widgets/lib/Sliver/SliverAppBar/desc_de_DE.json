{"id": 184, "name": "SliverAppBar", "localName": "Sliver-Kopfleiste", "info": "Die allgemeine Struktur der oberen Leiste der Sliver-Familie, die linke, mittlere und rechte Komponenten, Schrumpfraum, Schattentiefe, Festmodus, Hintergrundfarbe usw. angeben kann.", "lever": 4, "family": 4, "linkIds": [183, 196], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SliverAppBar", "desc": ["【leading】 : <PERSON><PERSON>   【Widget】", "【title】 : <PERSON><PERSON><PERSON>   【Widget】", "【actions】 : Liste der rechten Komponenten   【List<Widget>】", "【floating】 : <PERSON><PERSON> schwebend   【bool】", "【pinned】 : Ob oben verbleibend   【bool】", "【snap】 : <PERSON><PERSON> halb zu<PERSON>t   【bool】", "【bottom】 : Untere Komponente   【PreferredSizeWidget】", "【expandedHeight】 : Erweiterungshöhe   【double】", "【elevation】 : Sc<PERSON>tentiefe   【double】", "【flexibleSpace】 : Erweiterungsraum   【FlexibleSpaceBar】", "【backgroundColor】 : Hintergrundfarbe   【Color】", "【controller】 : Controller   【ScrollController】", "   <PERSON>n snap true ist, muss floating true sein"]}]}