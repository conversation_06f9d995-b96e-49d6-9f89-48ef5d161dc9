{"id": 308, "name": "SliverOverlapInjector", "localName": "Injecteur de chevauchement", "info": "Un sliver, doit être utilisé conjointement avec SliverOverlapAbsorber pour gérer les problèmes de chevauchement de vues.", "lever": 3, "family": 4, "linkIds": [251, 307], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de SliverOverlapInjector", "desc": ["【sliver】 : Composant enfant   【Widget】", "【handle】 : *Gestionnaire   【SliverOverlapAbsorberHandle】", "Si les composants SliverOverlapAbsorber et SliverOverlapInjector ne sont pas utilisés, le contenu de NestedScrollView chevauchera la barre d'en-tête."]}]}