{"id": 308, "name": "SliverOverlapInjector", "localName": "Overlap Injector", "info": "A sliver that needs to be used with SliverOverlapAbsorber to handle view overlap issues.", "lever": 3, "family": 4, "linkIds": [251, 307], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of SliverOverlapInjector", "desc": ["【sliver】 : Child component   【Widget】", "【handle】 : *Handler   【SliverOverlapAbsorberHandle】", "If the SliverOverlapAbsorber and SliverOverlapInjector components are not used, the content of the NestedScrollView will overlap with the header bar."]}]}