{"id": 308, "name": "SliverOverlapInjector", "localName": "Inyector de superposición", "info": "Un sliver que debe usarse junto con SliverOverlapAbsorber para manejar problemas de superposición de vistas.", "lever": 3, "family": 4, "linkIds": [251, 307], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de SliverOverlapInjector", "desc": ["【sliver】 : componente hijo   【Widget】", "【handle】 : *manejador   【SliverOverlapAbsorberHandle】", "Si no se utilizan los componentes SliverOverlapAbsorber y SliverOverlapInjector, el contenido de NestedScrollView se superpondrá con la barra de encabezado."]}]}