{"id": 308, "name": "SliverOverlapInjector", "localName": "겹침 인젝터", "info": "SliverOverlapAbsorber와 함께 사용해야 하는 sliver로, 뷰 겹침 문제를 처리합니다.", "lever": 3, "family": 4, "linkIds": [251, 307], "nodes": [{"file": "node1_base.dart", "name": "SliverOverlapInjector 기본 사용법", "desc": ["【sliver】 : 자식 컴포넌트   【Widget】", "【handle】 : *처리기   【SliverOverlapAbsorberHandle】", "SliverOverlapAbsorber와 SliverOverlapInjector 컴포넌트를 사용하지 않으면, NestedScrollView의 내용이 헤더 바와 겹칩니다."]}]}