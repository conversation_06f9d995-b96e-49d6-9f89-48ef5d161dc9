{"id": 190, "name": "SliverPersistentHeader", "localName": "Intestazione persistente", "info": "Utilizzato solitamente in CustomScrollView, consente a un componente di rimanere in cima durante lo scorrimento, senza scomparire.", "lever": 5, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di SliverPersistentHeader", "desc": ["【delegate】 : delegato   【SliverPersistentHeaderDelegate】", "【floating】 : se galleggiante   【bool】", "【pinned】 : se rimane in cima   【bool】"]}]}