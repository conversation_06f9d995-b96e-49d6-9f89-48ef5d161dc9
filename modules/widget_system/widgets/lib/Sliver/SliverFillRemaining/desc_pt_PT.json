{"id": 306, "name": "SliverFillRemaining", "localName": "<PERSON>liver preencher restante", "info": "Um sliver que contém um único elemento box, que preenche o espaço restante na janela de visualização. Dois valores bool podem ser especificados para controlar o efeito de deslizamento, como no exemplo a seguir, experimente você mesmo.", "lever": 4, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do SliverFillRemaining", "desc": ["【hasScrollBody】 : Se tem um corpo de rolagem   【bool】", "【fillOverscroll】 : Se pode preencher a área de rolagem   【bool】", "【child】 : Compo<PERSON><PERSON> filho   【Widget】"]}]}