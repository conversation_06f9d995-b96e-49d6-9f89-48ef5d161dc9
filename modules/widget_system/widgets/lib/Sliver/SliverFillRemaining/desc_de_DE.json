{"id": 306, "name": "SliverFillRemaining", "localName": "S<PERSON>ver füllt den Rest", "info": "<PERSON> Sliver, das ein einzelnes Box-Kind enthält und den verbleibenden Platz im Viewport ausfüllt. Es können zwei boolesche Werte angegeben werden, um den Scroll-Effekt zu steuern, wie im folgenden Beispiel gezeigt, um es selbst zu erleben.", "lever": 4, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SliverFillRemaining", "desc": ["【hasScrollBody】 : Ob es einen Scroll-Body hat   【bool】", "【fillOverscroll】 : <PERSON><PERSON> <PERSON> Scroll-Bereich gefüllt werden kann   【bool】", "【child】 : Kindkomponente   【Widget】"]}]}