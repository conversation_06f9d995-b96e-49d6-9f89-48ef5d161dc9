{"id": 314, "name": "SliverPrototypeExtentList", "localName": "Lista de Extensión de Prototipo Sliver", "info": "La propiedad prototypeItem es un Widget, que se encarga de restringir el tamaño del ítem en la dirección del eje principal, pero no se mostrará. El delegate acepta un SliverChildDelegate para completar la creación del ítem.", "lever": 2, "family": 4, "linkIds": [185, 186], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de SliverPrototypeExtentList", "desc": ["【prototypeItem】 : Componente de tamaño en la dirección del eje principal   【Widget】", "【delegate】 : <PERSON><PERSON><PERSON> de hijos   【SliverChildDelegate】"]}]}