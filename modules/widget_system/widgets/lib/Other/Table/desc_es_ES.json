{"id": 110, "name": "Table", "localName": "Componente de tabla", "info": "Componente utilizado para mostrar tablas, se pueden especificar atributos como bordes, ancho de columna, dirección del texto, etc. El tipo de objeto principal es TableRow.", "lever": 4, "family": 6, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de Table", "desc": ["【children】 : Lista de componentes   【List<Widget>】", "【columnWidths】 : <PERSON><PERSON> de columna   【Map<int, TableColumnWidth>】", "【defaultColumnWidth】 : <PERSON><PERSON> de columna predeterminado  【TableColumnWidth】", "【border】 : Borde   【TableBorder】", "【textDirection】 : Dirección del texto   【TextDirection】", "【defaultVerticalAlignment】 : Modo de alineación vertical de la celda   【TableCellVerticalAlignment】"]}]}