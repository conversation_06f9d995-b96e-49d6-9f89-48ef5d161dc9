{"id": 291, "name": "ListWheelViewport", "localName": "Vista della ruota di scorrimento", "info": "Una vista che visualizza un elenco di elementi in una ruota cilindrica, è la dipendenza sottostante di ListWheelScrollView e CupertinoPicker", "lever": 4, "family": 6, "linkIds": [179, 139, 137, 253], "nodes": [{"file": "node1_base.dart", "name": "Uso semplice di ListWheelViewport", "desc": ["【itemExtent】 : Dimensione dell'elemento lungo l'asse   【double】", "【offset】 : Offset della vista   【ViewportOffset】", "【childDelegate】 : Delegato di costruzione dei figli   【ListWheelChildDelegate】"]}, {"file": "node2_perspective.dart", "name": "<PERSON><PERSON>tto prospettico di ListWheelViewport", "desc": ["【perspective】 : Parametro di prospettiva   【double】", "【squeeze】 : <PERSON><PERSON> di <PERSON>   【double】", "【diameterRatio】 : Rapporto del diametro   【double】"]}, {"file": "node3_magnifier.dart", "name": "Ingrandimento di ListWheelViewport", "desc": ["【useMagnifier】 : Se ingrandire   【bool】", "【magnification】 : Rapporto di ingrandimento   【double】", "【clipBehavior】 : Comportamento di ritaglio   【Clip】", "【renderChildrenOutsideViewport】 : Se renderizzare i figli fuori dalla vista   【bool】"]}, {"file": "node4_opacity.dart", "name": "Offset e trasparenza", "desc": ["【offAxisFraction】 : Rapporto di offset del centro dell'asse   【double】", "【overAndUnderCenterOpacity】 : Trasparenza fuori dall'ingranditore   【double】"]}]}