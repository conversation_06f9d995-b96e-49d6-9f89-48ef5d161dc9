{"id": 291, "name": "ListWheelViewport", "localName": "<PERSON>ue de roue de liste", "info": "Une vue qui affiche une liste d'enfants sur une roue cylindrique, dépendance sous-jacente de ListWheelScrollView et CupertinoPicker", "lever": 4, "family": 6, "linkIds": [179, 139, 137, 253], "nodes": [{"file": "node1_base.dart", "name": "Utilisation simple de ListWheelViewport", "desc": ["【itemExtent】 : Taille de l'item axial   【double】", "【offset】 : Décalage de la vue   【ViewportOffset】", "【childDelegate】 : Constructeur de délégué enfant   【ListWheelChildDelegate】"]}, {"file": "node2_perspective.dart", "name": "Effet de perspective de ListWheelViewport", "desc": ["【perspective】 : Paramètre de perspective   【double】", "【squeeze】 : Valeur de compression   【double】", "【diameterRatio】 : <PERSON><PERSON> de diamètre   【double】"]}, {"file": "node3_magnifier.dart", "name": "Agrandissement de ListWheelViewport", "desc": ["【useMagnifier】 : Agrandir ou non   【bool】", "【magnification】 : Taux d'agrandissement   【double】", "【clipBehavior】 : Comportement de découpage   【Clip】", "【renderChildrenOutsideViewport】 : <PERSON>du hors de la vue   【bool】"]}, {"file": "node4_opacity.dart", "name": "Décalage et opacité", "desc": ["【offAxisFraction】 : Ratio de décalage du centre de l'axe   【double】", "【overAndUnderCenterOpacity】 : Opacité en dehors de l'amplificateur   【double】"]}]}